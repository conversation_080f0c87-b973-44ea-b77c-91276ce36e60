-- 数智化运营平台数据库设计
-- 创建时间: 2023-07-15
-- 版本: 1.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS digital_operation_platform DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE digital_operation_platform;

-- 用户表
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像URL',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role VARCHAR(50) NOT NULL DEFAULT 'user' COMMENT '角色',
    status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 角色表
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY COMMENT '角色ID',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_status (status)
) COMMENT '角色表';

-- 权限表
CREATE TABLE permissions (
    id VARCHAR(36) PRIMARY KEY COMMENT '权限ID',
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    type ENUM('menu', 'button', 'data') NOT NULL COMMENT '权限类型',
    resource VARCHAR(255) NOT NULL COMMENT '资源标识',
    description TEXT COMMENT '权限描述',
    parent_id VARCHAR(36) COMMENT '父权限ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_type (type),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (parent_id) REFERENCES permissions(id) ON DELETE SET NULL
) COMMENT '权限表';

-- 角色权限关联表
CREATE TABLE role_permissions (
    id VARCHAR(36) PRIMARY KEY COMMENT 'ID',
    role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
    permission_id VARCHAR(36) NOT NULL COMMENT '权限ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) COMMENT '角色权限关联表';

-- 用户角色关联表
CREATE TABLE user_roles (
    id VARCHAR(36) PRIMARY KEY COMMENT 'ID',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    role_id VARCHAR(36) NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) COMMENT '用户角色关联表';

-- 数据源表
CREATE TABLE data_sources (
    id VARCHAR(36) PRIMARY KEY COMMENT '数据源ID',
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    type ENUM('mysql', 'postgresql', 'oracle', 'mongodb', 'redis', 'elasticsearch', 'kafka', 'file', 'api') NOT NULL COMMENT '数据源类型',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口',
    database_name VARCHAR(100) COMMENT '数据库名称',
    schema_name VARCHAR(100) COMMENT 'Schema名称',
    username VARCHAR(100) COMMENT '用户名',
    password_encrypted TEXT COMMENT '加密密码',
    connection_params TEXT COMMENT '连接参数JSON',
    description TEXT COMMENT '描述',
    status ENUM('connected', 'disconnected', 'error') DEFAULT 'disconnected' COMMENT '连接状态',
    creator VARCHAR(36) NOT NULL COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_creator (creator),
    FOREIGN KEY (creator) REFERENCES users(id)
) COMMENT '数据源表';

-- 任务表
CREATE TABLE tasks (
    id VARCHAR(36) PRIMARY KEY COMMENT '任务ID',
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    type ENUM('realtime', 'offline') NOT NULL COMMENT '任务类型',
    data_source_id VARCHAR(36) NOT NULL COMMENT '数据源ID',
    status ENUM('running', 'stopped', 'error', 'pending') DEFAULT 'pending' COMMENT '任务状态',
    config JSON COMMENT '任务配置',
    schedule_config JSON COMMENT '调度配置',
    description TEXT COMMENT '任务描述',
    creator VARCHAR(36) NOT NULL COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_data_source_id (data_source_id),
    INDEX idx_creator (creator),
    FOREIGN KEY (data_source_id) REFERENCES data_sources(id),
    FOREIGN KEY (creator) REFERENCES users(id)
) COMMENT '任务表';

-- 任务执行记录表
CREATE TABLE task_executions (
    id VARCHAR(36) PRIMARY KEY COMMENT '执行记录ID',
    task_id VARCHAR(36) NOT NULL COMMENT '任务ID',
    status ENUM('running', 'success', 'failed', 'cancelled') NOT NULL COMMENT '执行状态',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    total_records BIGINT DEFAULT 0 COMMENT '总记录数',
    processed_records BIGINT DEFAULT 0 COMMENT '已处理记录数',
    error_records BIGINT DEFAULT 0 COMMENT '错误记录数',
    success_rate DECIMAL(5,2) DEFAULT 0 COMMENT '成功率',
    avg_process_time DECIMAL(10,3) DEFAULT 0 COMMENT '平均处理时间(秒)',
    error_message TEXT COMMENT '错误信息',
    logs TEXT COMMENT '执行日志',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
) COMMENT '任务执行记录表';

-- 报告表
CREATE TABLE reports (
    id VARCHAR(36) PRIMARY KEY COMMENT '报告ID',
    name VARCHAR(100) NOT NULL COMMENT '报告名称',
    type ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL COMMENT '报告类型',
    period VARCHAR(50) NOT NULL COMMENT '报告周期',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '报告状态',
    content JSON COMMENT '报告内容',
    template_id VARCHAR(36) COMMENT '模板ID',
    author VARCHAR(36) NOT NULL COMMENT '作者',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    publish_time TIMESTAMP NULL COMMENT '发布时间',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_period (period),
    INDEX idx_author (author),
    FOREIGN KEY (author) REFERENCES users(id)
) COMMENT '报告表';

-- 模板表
CREATE TABLE templates (
    id VARCHAR(36) PRIMARY KEY COMMENT '模板ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    type ENUM('dashboard', 'report', 'chart') NOT NULL COMMENT '模板类型',
    category VARCHAR(50) NOT NULL COMMENT '模板分类',
    description TEXT COMMENT '模板描述',
    config JSON COMMENT '模板配置',
    preview_url VARCHAR(255) COMMENT '预览图URL',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    creator VARCHAR(36) NOT NULL COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_creator (creator),
    FOREIGN KEY (creator) REFERENCES users(id)
) COMMENT '模板表';

-- 仪表板表
CREATE TABLE dashboards (
    id VARCHAR(36) PRIMARY KEY COMMENT '仪表板ID',
    name VARCHAR(100) NOT NULL COMMENT '仪表板名称',
    description TEXT COMMENT '描述',
    layout_config JSON COMMENT '布局配置',
    widgets_config JSON COMMENT '组件配置',
    permissions JSON COMMENT '权限配置',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    creator VARCHAR(36) NOT NULL COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_creator (creator),
    FOREIGN KEY (creator) REFERENCES users(id)
) COMMENT '仪表板表';

-- 告警表
CREATE TABLE alerts (
    id VARCHAR(36) PRIMARY KEY COMMENT '告警ID',
    title VARCHAR(200) NOT NULL COMMENT '告警标题',
    content TEXT NOT NULL COMMENT '告警内容',
    level ENUM('info', 'warning', 'error', 'critical') NOT NULL COMMENT '告警级别',
    type ENUM('system', 'task', 'data', 'security') NOT NULL COMMENT '告警类型',
    source VARCHAR(100) NOT NULL COMMENT '告警源',
    status ENUM('new', 'processing', 'resolved', 'closed') DEFAULT 'new' COMMENT '处理状态',
    assignee VARCHAR(36) COMMENT '处理人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    resolve_time TIMESTAMP NULL COMMENT '解决时间',
    INDEX idx_level (level),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_assignee (assignee),
    FOREIGN KEY (assignee) REFERENCES users(id) ON DELETE SET NULL
) COMMENT '告警表';

-- 系统日志表
CREATE TABLE system_logs (
    id VARCHAR(36) PRIMARY KEY COMMENT '日志ID',
    level ENUM('debug', 'info', 'warn', 'error') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    user_id VARCHAR(36) COMMENT '用户ID',
    ip VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    params JSON COMMENT '请求参数',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_level (level),
    INDEX idx_module (module),
    INDEX idx_action (action),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) COMMENT '系统日志表';

-- 系统配置表
CREATE TABLE system_configs (
    id VARCHAR(36) PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    value_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '值类型',
    category VARCHAR(50) NOT NULL COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_key (config_key),
    INDEX idx_category (category)
) COMMENT '系统配置表';

-- 文件表
CREATE TABLE files (
    id VARCHAR(36) PRIMARY KEY COMMENT '文件ID',
    name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    category VARCHAR(50) NOT NULL COMMENT '文件分类',
    description TEXT COMMENT '文件描述',
    uploader VARCHAR(36) NOT NULL COMMENT '上传人',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_uploader (uploader),
    INDEX idx_upload_time (upload_time),
    FOREIGN KEY (uploader) REFERENCES users(id)
) COMMENT '文件表';

-- 插入初始数据
INSERT INTO users (id, username, name, email, password_hash, role, status) VALUES
('1', 'admin', '系统管理员', '<EMAIL>', '$2b$10$example_hash', 'admin', 'active'),
('2', 'operator', '运营人员', '<EMAIL>', '$2b$10$example_hash', 'operator', 'active');

INSERT INTO roles (id, name, code, description) VALUES
('1', '系统管理员', 'admin', '拥有系统所有权限'),
('2', '运营人员', 'operator', '负责日常运营工作'),
('3', '普通用户', 'user', '基础用户权限');

INSERT INTO permissions (id, name, code, type, resource) VALUES
('1', '首页查看', 'dashboard:view', 'menu', '/'),
('2', '数据源管理', 'datasource:manage', 'menu', '/data-source'),
('3', '任务管理', 'task:manage', 'menu', '/task'),
('4', '报告管理', 'report:manage', 'menu', '/report'),
('5', '用户管理', 'user:manage', 'menu', '/user'),
('6', '系统设置', 'system:config', 'menu', '/system');

-- 创建索引优化查询性能
CREATE INDEX idx_tasks_composite ON tasks(status, type, create_time);
CREATE INDEX idx_task_executions_composite ON task_executions(task_id, start_time, status);
CREATE INDEX idx_reports_composite ON reports(type, status, create_time);
CREATE INDEX idx_alerts_composite ON alerts(level, status, create_time);
CREATE INDEX idx_logs_composite ON system_logs(level, module, create_time);
