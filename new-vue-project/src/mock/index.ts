// Mock数据服务
import type { DataSource, Task, Report, User } from '@/types'

// 生成随机ID
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// 生成随机日期
function randomDate(start: Date, end: Date): string {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  return date.toISOString().slice(0, 19).replace('T', ' ')
}

// Mock用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    name: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: 'https://picsum.photos/id/1005/100/100',
    role: 'admin',
    permissions: ['*'],
    status: 'active',
    createTime: '2023-01-01 00:00:00',
    updateTime: '2023-07-15 10:00:00',
    lastLoginTime: '2023-07-15 09:30:00'
  },
  {
    id: '2',
    username: 'operator',
    name: '运营人员',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://picsum.photos/id/1006/100/100',
    role: 'operator',
    permissions: ['dashboard:view', 'report:view', 'report:create'],
    status: 'active',
    createTime: '2023-02-01 00:00:00',
    updateTime: '2023-07-14 16:00:00',
    lastLoginTime: '2023-07-14 15:30:00'
  }
]

// Mock数据源数据
export const mockDataSources: DataSource[] = [
  {
    id: '1',
    name: '用户数据库',
    type: 'mysql',
    host: '*************',
    port: 3306,
    database: 'user_db',
    username: 'root',
    password: '******',
    description: '用户相关数据存储',
    status: 'connected',
    createTime: '2023-07-01 10:00:00',
    updateTime: '2023-07-15 10:30:00',
    creator: 'admin'
  },
  {
    id: '2',
    name: '订单数据库',
    type: 'postgresql',
    host: '*************',
    port: 5432,
    database: 'order_db',
    schema: 'public',
    username: 'postgres',
    password: '******',
    description: '订单交易数据存储',
    status: 'connected',
    createTime: '2023-07-02 14:00:00',
    updateTime: '2023-07-14 15:20:00',
    creator: 'admin'
  },
  {
    id: '3',
    name: '缓存数据库',
    type: 'redis',
    host: '*************',
    port: 6379,
    database: '0',
    description: '缓存和会话存储',
    status: 'disconnected',
    createTime: '2023-07-03 09:00:00',
    updateTime: '2023-07-13 09:15:00',
    creator: 'admin'
  },
  {
    id: '4',
    name: '日志存储',
    type: 'elasticsearch',
    host: '*************',
    port: 9200,
    description: '系统日志和搜索',
    status: 'connected',
    createTime: '2023-07-04 11:00:00',
    updateTime: '2023-07-12 16:45:00',
    creator: 'operator'
  },
  {
    id: '5',
    name: '消息队列',
    type: 'kafka',
    host: '*************',
    port: 9092,
    description: '实时数据流处理',
    status: 'error',
    createTime: '2023-07-05 13:00:00',
    updateTime: '2023-07-11 14:30:00',
    creator: 'admin'
  }
]

// Mock任务数据
export const mockTasks: Task[] = [
  {
    id: '1',
    name: '用户行为数据采集',
    type: 'realtime',
    dataSourceId: '5',
    dataSourceName: '消息队列',
    status: 'running',
    config: {
      sourceTable: 'user_events',
      targetTable: 'user_behavior_analysis',
      fields: ['user_id', 'event_type', 'timestamp', 'properties']
    },
    statistics: {
      totalRecords: 1250000,
      processedRecords: 1248500,
      errorRecords: 1500,
      successRate: 99.88,
      avgProcessTime: 0.05,
      lastRunTime: '2023-07-15 10:25:00'
    },
    createTime: '2023-07-10 08:00:00',
    updateTime: '2023-07-15 10:25:00',
    startTime: '2023-07-10 08:00:00',
    creator: 'admin'
  },
  {
    id: '2',
    name: '销售数据同步',
    type: 'offline',
    dataSourceId: '2',
    dataSourceName: '订单数据库',
    status: 'stopped',
    config: {
      sourceTable: 'orders',
      targetTable: 'sales_report',
      fields: ['order_id', 'customer_id', 'amount', 'order_date']
    },
    schedule: {
      type: 'cron',
      cron: '0 2 * * *'
    },
    statistics: {
      totalRecords: 50000,
      processedRecords: 50000,
      errorRecords: 0,
      successRate: 100,
      avgProcessTime: 0.02,
      lastRunTime: '2023-07-15 02:00:00'
    },
    createTime: '2023-07-08 20:00:00',
    updateTime: '2023-07-15 02:30:00',
    startTime: '2023-07-15 02:00:00',
    endTime: '2023-07-15 02:30:00',
    creator: 'operator'
  },
  {
    id: '3',
    name: '日志数据导入',
    type: 'offline',
    dataSourceId: '4',
    dataSourceName: '日志存储',
    status: 'error',
    config: {
      sourceTable: 'application_logs',
      targetTable: 'log_analysis',
      fields: ['timestamp', 'level', 'message', 'module']
    },
    statistics: {
      totalRecords: 100000,
      processedRecords: 85000,
      errorRecords: 15000,
      successRate: 85,
      avgProcessTime: 0.08,
      lastRunTime: '2023-07-14 18:00:00'
    },
    createTime: '2023-07-09 18:00:00',
    updateTime: '2023-07-14 18:15:00',
    startTime: '2023-07-14 18:00:00',
    endTime: '2023-07-14 18:15:00',
    creator: 'admin'
  },
  {
    id: '4',
    name: '产品库存监控',
    type: 'realtime',
    dataSourceId: '1',
    dataSourceName: '用户数据库',
    status: 'running',
    config: {
      sourceTable: 'inventory',
      targetTable: 'inventory_alerts',
      fields: ['product_id', 'quantity', 'threshold', 'last_updated']
    },
    statistics: {
      totalRecords: 25000,
      processedRecords: 24980,
      errorRecords: 20,
      successRate: 99.92,
      avgProcessTime: 0.03,
      lastRunTime: '2023-07-15 10:20:00'
    },
    createTime: '2023-07-07 00:00:00',
    updateTime: '2023-07-15 10:20:00',
    startTime: '2023-07-07 00:00:00',
    creator: 'operator'
  },
  {
    id: '5',
    name: '客户信息同步',
    type: 'offline',
    dataSourceId: '1',
    dataSourceName: '用户数据库',
    status: 'pending',
    config: {
      sourceTable: 'customers',
      targetTable: 'customer_profiles',
      fields: ['customer_id', 'name', 'email', 'phone', 'address']
    },
    schedule: {
      type: 'cron',
      cron: '0 20 * * *'
    },
    statistics: {
      totalRecords: 0,
      processedRecords: 0,
      errorRecords: 0,
      successRate: 0,
      avgProcessTime: 0
    },
    createTime: '2023-07-15 15:00:00',
    updateTime: '2023-07-15 15:00:00',
    creator: 'admin'
  }
]

// Mock报告数据
export const mockReports: Report[] = [
  {
    id: '1',
    name: '2023年7月运营日报',
    type: 'daily',
    period: '2023-07-15',
    status: 'published',
    content: {
      title: '2023年7月15日运营日报',
      summary: '今日系统运行稳定，数据处理量达到预期目标。',
      sections: [
        {
          id: '1',
          title: '数据处理概况',
          content: '今日共处理数据125万条，成功率99.8%。',
          order: 1,
          type: 'text'
        }
      ],
      charts: []
    },
    author: 'operator',
    createTime: '2023-07-15 18:00:00',
    updateTime: '2023-07-15 18:30:00',
    publishTime: '2023-07-15 19:00:00'
  },
  {
    id: '2',
    name: '2023年第28周运营周报',
    type: 'weekly',
    period: '2023-07-10 ~ 2023-07-16',
    status: 'draft',
    content: {
      title: '2023年第28周运营周报',
      summary: '本周系统整体运行良好，新增功能按计划上线。',
      sections: [],
      charts: []
    },
    author: 'operator',
    createTime: '2023-07-14 16:30:00',
    updateTime: '2023-07-15 10:00:00'
  },
  {
    id: '3',
    name: '2023年6月运营月报',
    type: 'monthly',
    period: '2023-06',
    status: 'published',
    content: {
      title: '2023年6月运营月报',
      summary: '6月份平台数据处理量创新高，用户活跃度显著提升。',
      sections: [],
      charts: []
    },
    author: 'admin',
    createTime: '2023-07-01 10:00:00',
    updateTime: '2023-07-01 15:00:00',
    publishTime: '2023-07-01 16:00:00'
  }
]

// Mock API响应
export function createMockResponse<T>(data: T, delay = 500): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(data)
    }, delay)
  })
}

// 分页处理
export function paginate<T>(data: T[], page: number, pageSize: number) {
  const start = (page - 1) * pageSize
  const end = start + pageSize
  return {
    list: data.slice(start, end),
    pagination: {
      page,
      pageSize,
      total: data.length,
      totalPages: Math.ceil(data.length / pageSize)
    }
  }
}

// 搜索过滤
export function filterData<T extends Record<string, any>>(
  data: T[],
  filters: Record<string, any>
): T[] {
  return data.filter(item => {
    return Object.entries(filters).every(([key, value]) => {
      if (!value) return true
      const itemValue = item[key]
      if (typeof itemValue === 'string') {
        return itemValue.toLowerCase().includes(value.toLowerCase())
      }
      return itemValue === value
    })
  })
}
