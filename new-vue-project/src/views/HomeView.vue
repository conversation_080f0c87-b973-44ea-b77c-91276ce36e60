<template>
  <div>
    <!-- 页面标题 -->
    <div class="flex items-center mb-6">
      <i class="fas fa-home text-primary text-xl mr-3"></i>
      <h1 class="text-2xl font-semibold text-gray-800">数据概览</h1>
    </div>

    <!-- 时间选择器 -->
    <div class="flex items-center mb-6">
      <label for="dateRange" class="mr-3 text-sm font-medium text-gray-700">时间范围:</label>
      <select
        id="dateRange"
        v-model="selectedDateRange"
        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent mr-3"
      >
        <option value="today">今日</option>
        <option value="yesterday">昨日</option>
        <option value="7days">近7天</option>
        <option value="30days">近30天</option>
        <option value="custom">自定义</option>
      </select>
      <BaseButton icon="fas fa-search" @click="handleQuery">
        查询
      </BaseButton>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <StatCard
        v-for="stat in stats"
        :key="stat.label"
        :icon="stat.icon"
        :value="stat.value"
        :label="stat.label"
        :color="stat.color"
      />
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <BaseCard title="数据采集趋势">
        <template #actions>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">导出数据</div>
              <div class="dropdown-item">刷新</div>
              <div class="dropdown-item">设置</div>
            </div>
          </div>
        </template>
        <div class="h-64 flex items-center justify-center text-gray-500">
          <div class="text-center">
            <i class="fas fa-chart-line text-4xl mb-2"></i>
            <div>图表组件待实现</div>
          </div>
        </div>
      </BaseCard>

      <BaseCard title="数据源分布">
        <template #actions>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">导出数据</div>
              <div class="dropdown-item">刷新</div>
              <div class="dropdown-item">设置</div>
            </div>
          </div>
        </template>
        <div class="h-64 flex items-center justify-center text-gray-500">
          <div class="text-center">
            <i class="fas fa-chart-pie text-4xl mb-2"></i>
            <div>图表组件待实现</div>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- 最近任务表格 -->
    <BaseCard title="最近任务">
      <template #actions>
        <BaseButton icon="fas fa-plus" @click="showAddTaskModal = true">
          新建任务
        </BaseButton>
      </template>

      <BaseTable
        :columns="taskColumns"
        :data="tasks"
        :hoverable="true"
      >
        <template #status="{ value }">
          <span :class="getStatusClass(value)">{{ value }}</span>
        </template>

        <template #actions="{ row }">
          <div class="flex space-x-2">
            <button class="text-primary hover:text-secondary">
              <i class="fas fa-eye"></i>
            </button>
            <button class="text-warning hover:text-yellow-600">
              <i class="fas fa-pause"></i>
            </button>
            <button class="text-danger hover:text-red-600">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </template>
      </BaseTable>

      <BasePagination
        v-model:current-page="currentPage"
        :total="totalTasks"
        :page-size="pageSize"
        @change="handlePageChange"
      />
    </BaseCard>

    <!-- 添加任务模态框 -->
    <BaseModal
      v-model="showAddTaskModal"
      title="新建任务"
      icon="fas fa-plus"
    >
      <div class="space-y-4">
        <div class="form-group">
          <label>任务名称</label>
          <input type="text" placeholder="请输入任务名称" />
        </div>
        <div class="form-group">
          <label>任务类型</label>
          <select>
            <option value="">请选择任务类型</option>
            <option value="realtime">实时采集</option>
            <option value="offline">离线采集</option>
          </select>
        </div>
        <div class="form-group">
          <label>数据源</label>
          <select>
            <option value="">请选择数据源</option>
            <option value="mysql">MySQL</option>
            <option value="oracle">Oracle</option>
            <option value="file">文件</option>
            <option value="api">API</option>
            <option value="kafka">Kafka</option>
          </select>
        </div>
      </div>

      <template #footer>
        <BaseButton variant="outline" @click="showAddTaskModal = false">取消</BaseButton>
        <BaseButton @click="handleAddTask">确认</BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseCard from '@/components/ui/BaseCard.vue'
import BaseTable from '@/components/ui/BaseTable.vue'
import BasePagination from '@/components/ui/BasePagination.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import type { TableColumn } from '@/components/ui/BaseTable.vue'

// 响应式数据
const selectedDateRange = ref('7days')
const showAddTaskModal = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalTasks = ref(50)

// 统计数据
const stats = ref([
  {
    icon: 'fas fa-database',
    value: '128',
    label: '数据源数量',
    color: 'blue'
  },
  {
    icon: 'fas fa-tasks',
    value: '56',
    label: '运行中任务',
    color: 'green'
  },
  {
    icon: 'fas fa-chart-line',
    value: '1.2T',
    label: '数据处理量',
    color: 'yellow'
  },
  {
    icon: 'fas fa-exclamation-triangle',
    value: '3',
    label: '告警数量',
    color: 'red'
  }
])

// 表格列定义
const taskColumns: TableColumn[] = [
  { key: 'name', title: '任务名称', sortable: true },
  { key: 'type', title: '任务类型' },
  { key: 'dataSource', title: '数据源' },
  { key: 'status', title: '状态' },
  { key: 'startTime', title: '开始时间', sortable: true },
  { key: 'endTime', title: '结束时间' },
  { key: 'actions', title: '操作', width: '120px' }
]

// 任务数据
const tasks = ref([
  {
    id: 1,
    name: '用户行为数据采集',
    type: '实时采集',
    dataSource: 'Kafka',
    status: '运行中',
    startTime: '2023-07-15 08:00',
    endTime: '-'
  },
  {
    id: 2,
    name: '销售数据同步',
    type: '离线采集',
    dataSource: 'MySQL',
    status: '已完成',
    startTime: '2023-07-14 20:00',
    endTime: '2023-07-14 20:30'
  },
  {
    id: 3,
    name: '日志数据导入',
    type: '离线采集',
    dataSource: '文件',
    status: '失败',
    startTime: '2023-07-14 18:00',
    endTime: '2023-07-14 18:15'
  },
  {
    id: 4,
    name: '产品库存监控',
    type: '实时采集',
    dataSource: 'API',
    status: '运行中',
    startTime: '2023-07-14 00:00',
    endTime: '-'
  },
  {
    id: 5,
    name: '客户信息同步',
    type: '离线采集',
    dataSource: 'Oracle',
    status: '待执行',
    startTime: '2023-07-15 20:00',
    endTime: '-'
  }
])

// 方法
function handleQuery() {
  console.log('查询数据:', selectedDateRange.value)
  // 这里可以调用API获取数据
}

function getStatusClass(status: string) {
  const statusMap: Record<string, string> = {
    '运行中': 'tag tag-success',
    '已完成': 'tag tag-success',
    '失败': 'tag tag-danger',
    '待执行': 'tag tag-warning'
  }
  return statusMap[status] || 'tag'
}

function handlePageChange(page: number) {
  console.log('切换到第', page, '页')
  // 这里可以调用API获取对应页的数据
}

function handleAddTask() {
  console.log('添加任务')
  showAddTaskModal.value = false
  // 这里可以调用API添加任务
}

onMounted(() => {
  // 初始化数据
  console.log('首页组件已挂载')
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
