<template>
  <div>
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <i class="fas fa-chart-pie text-primary text-xl mr-3"></i>
        <h1 class="text-2xl font-semibold text-gray-800">运营报告管理</h1>
      </div>
      <BaseButton icon="fas fa-plus" @click="showCreateModal = true">
        创建报告
      </BaseButton>
    </div>

    <!-- 搜索筛选 -->
    <BaseCard class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="form-group">
          <label>报告名称</label>
          <input
            v-model="searchForm.name"
            type="text"
            placeholder="请输入报告名称"
          />
        </div>
        <div class="form-group">
          <label>报告类型</label>
          <select v-model="searchForm.type">
            <option value="">全部类型</option>
            <option value="daily">日报</option>
            <option value="weekly">周报</option>
            <option value="monthly">月报</option>
            <option value="quarterly">季报</option>
            <option value="yearly">年报</option>
          </select>
        </div>
        <div class="form-group">
          <label>状态</label>
          <select v-model="searchForm.status">
            <option value="">全部状态</option>
            <option value="draft">草稿</option>
            <option value="published">已发布</option>
            <option value="archived">已归档</option>
          </select>
        </div>
        <div class="form-group flex items-end">
          <BaseButton icon="fas fa-search" @click="handleSearch" class="mr-2">
            搜索
          </BaseButton>
          <BaseButton variant="outline" @click="handleReset">
            重置
          </BaseButton>
        </div>
      </div>
    </BaseCard>

    <!-- 报告列表 -->
    <BaseCard title="报告列表">
      <BaseTable
        :columns="columns"
        :data="reportList"
        :hoverable="true"
      >
        <template #type="{ value }">
          <span :class="getTypeClass(value)">{{ getTypeText(value) }}</span>
        </template>
        
        <template #status="{ value }">
          <span :class="getStatusClass(value)">{{ getStatusText(value) }}</span>
        </template>
        
        <template #actions="{ row }">
          <div class="flex space-x-2">
            <button
              @click="handleView(row)"
              class="text-blue-600 hover:text-blue-800"
              title="查看"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button
              @click="handleEdit(row)"
              class="text-green-600 hover:text-green-800"
              title="编辑"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              @click="handleDownload(row)"
              class="text-purple-600 hover:text-purple-800"
              title="下载"
            >
              <i class="fas fa-download"></i>
            </button>
            <button
              @click="handleDelete(row)"
              class="text-red-600 hover:text-red-800"
              title="删除"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </template>
      </BaseTable>
      
      <BasePagination
        v-model:current-page="currentPage"
        :total="total"
        :page-size="pageSize"
        @change="handlePageChange"
      />
    </BaseCard>

    <!-- 创建报告模态框 -->
    <BaseModal
      v-model="showCreateModal"
      title="创建报告"
      size="lg"
    >
      <div class="space-y-4">
        <div class="form-group">
          <label>报告名称 <span class="text-red-500">*</span></label>
          <input
            v-model="createForm.name"
            type="text"
            placeholder="请输入报告名称"
            required
          />
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-group">
            <label>报告类型 <span class="text-red-500">*</span></label>
            <select v-model="createForm.type" required>
              <option value="">请选择报告类型</option>
              <option value="daily">日报</option>
              <option value="weekly">周报</option>
              <option value="monthly">月报</option>
              <option value="quarterly">季报</option>
              <option value="yearly">年报</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>报告周期</label>
            <input
              v-model="createForm.period"
              type="date"
            />
          </div>
        </div>
        
        <div class="form-group">
          <label>报告描述</label>
          <textarea
            v-model="createForm.description"
            rows="3"
            placeholder="请输入报告描述"
          ></textarea>
        </div>
      </div>
      
      <template #footer>
        <BaseButton variant="outline" @click="showCreateModal = false">
          取消
        </BaseButton>
        <BaseButton @click="handleCreate">
          创建
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseCard from '@/components/ui/BaseCard.vue'
import BaseTable from '@/components/ui/BaseTable.vue'
import BasePagination from '@/components/ui/BasePagination.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import type { TableColumn } from '@/components/ui/BaseTable.vue'

// 响应式数据
const showCreateModal = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = ref({
  name: '',
  type: '',
  status: ''
})

// 创建表单
const createForm = ref({
  name: '',
  type: '',
  period: '',
  description: ''
})

// 表格列定义
const columns: TableColumn[] = [
  { key: 'name', title: '报告名称', sortable: true },
  { key: 'type', title: '报告类型' },
  { key: 'period', title: '报告周期' },
  { key: 'status', title: '状态' },
  { key: 'author', title: '创建人' },
  { key: 'createTime', title: '创建时间', sortable: true },
  { key: 'actions', title: '操作', width: '150px' }
]

// 报告列表
const reportList = ref([
  {
    id: 1,
    name: '2023年7月运营日报',
    type: 'daily',
    period: '2023-07-15',
    status: 'published',
    author: '张三',
    createTime: '2023-07-15 18:00:00'
  },
  {
    id: 2,
    name: '2023年第28周运营周报',
    type: 'weekly',
    period: '2023-07-10 ~ 2023-07-16',
    status: 'draft',
    author: '李四',
    createTime: '2023-07-14 16:30:00'
  },
  {
    id: 3,
    name: '2023年6月运营月报',
    type: 'monthly',
    period: '2023-06',
    status: 'published',
    author: '王五',
    createTime: '2023-07-01 10:00:00'
  }
])

// 方法
function getTypeClass(type: string): string {
  const classMap: Record<string, string> = {
    daily: 'tag tag-info',
    weekly: 'tag tag-success',
    monthly: 'tag tag-warning',
    quarterly: 'tag tag-danger',
    yearly: 'tag'
  }
  return classMap[type] || 'tag'
}

function getTypeText(type: string): string {
  const textMap: Record<string, string> = {
    daily: '日报',
    weekly: '周报',
    monthly: '月报',
    quarterly: '季报',
    yearly: '年报'
  }
  return textMap[type] || type
}

function getStatusClass(status: string): string {
  const classMap: Record<string, string> = {
    draft: 'tag tag-warning',
    published: 'tag tag-success',
    archived: 'tag'
  }
  return classMap[status] || 'tag'
}

function getStatusText(status: string): string {
  const textMap: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return textMap[status] || status
}

function handleSearch() {
  console.log('搜索报告:', searchForm.value)
}

function handleReset() {
  searchForm.value = {
    name: '',
    type: '',
    status: ''
  }
  handleSearch()
}

function handleView(row: any) {
  console.log('查看报告:', row)
}

function handleEdit(row: any) {
  console.log('编辑报告:', row)
}

function handleDownload(row: any) {
  console.log('下载报告:', row)
}

function handleDelete(row: any) {
  if (confirm('确定要删除这个报告吗？')) {
    console.log('删除报告:', row)
  }
}

function handleCreate() {
  console.log('创建报告:', createForm.value)
  showCreateModal.value = false
  // 重置表单
  createForm.value = {
    name: '',
    type: '',
    period: '',
    description: ''
  }
}

function handlePageChange(page: number) {
  currentPage.value = page
}

onMounted(() => {
  total.value = reportList.value.length
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
