<template>
  <div>
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <i class="fas fa-database text-primary text-xl mr-3"></i>
        <h1 class="text-2xl font-semibold text-gray-800">数据源管理</h1>
      </div>
      <BaseButton icon="fas fa-plus" @click="showAddModal = true">
        新建数据源
      </BaseButton>
    </div>

    <!-- 搜索和筛选 -->
    <BaseCard class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="form-group">
          <label>数据源名称</label>
          <input
            v-model="searchForm.name"
            type="text"
            placeholder="请输入数据源名称"
          />
        </div>
        <div class="form-group">
          <label>数据源类型</label>
          <select v-model="searchForm.type">
            <option value="">全部类型</option>
            <option value="mysql">MySQL</option>
            <option value="oracle">Oracle</option>
            <option value="postgresql">PostgreSQL</option>
            <option value="mongodb">MongoDB</option>
            <option value="redis">Redis</option>
            <option value="elasticsearch">Elasticsearch</option>
          </select>
        </div>
        <div class="form-group">
          <label>连接状态</label>
          <select v-model="searchForm.status">
            <option value="">全部状态</option>
            <option value="connected">已连接</option>
            <option value="disconnected">未连接</option>
            <option value="error">连接异常</option>
          </select>
        </div>
        <div class="form-group flex items-end">
          <BaseButton icon="fas fa-search" @click="handleSearch" class="mr-2">
            搜索
          </BaseButton>
          <BaseButton variant="outline" @click="handleReset">
            重置
          </BaseButton>
        </div>
      </div>
    </BaseCard>

    <!-- 数据源列表 -->
    <BaseCard title="数据源列表">
      <BaseTable
        :columns="columns"
        :data="dataSourceList"
        :hoverable="true"
      >
        <template #type="{ value }">
          <div class="flex items-center">
            <i :class="getTypeIcon(value)" class="mr-2"></i>
            {{ getTypeName(value) }}
          </div>
        </template>
        
        <template #status="{ value }">
          <span :class="getStatusClass(value)">{{ getStatusText(value) }}</span>
        </template>
        
        <template #actions="{ row }">
          <div class="flex space-x-2">
            <button
              @click="handleTest(row)"
              class="text-blue-600 hover:text-blue-800"
              title="测试连接"
            >
              <i class="fas fa-plug"></i>
            </button>
            <button
              @click="handleEdit(row)"
              class="text-green-600 hover:text-green-800"
              title="编辑"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              @click="handleDelete(row)"
              class="text-red-600 hover:text-red-800"
              title="删除"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </template>
      </BaseTable>
      
      <BasePagination
        v-model:current-page="currentPage"
        :total="total"
        :page-size="pageSize"
        @change="handlePageChange"
      />
    </BaseCard>

    <!-- 新建/编辑数据源模态框 -->
    <BaseModal
      v-model="showAddModal"
      :title="editingItem ? '编辑数据源' : '新建数据源'"
      size="lg"
    >
      <DataSourceForm
        :data="editingItem"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseCard from '@/components/ui/BaseCard.vue'
import BaseTable from '@/components/ui/BaseTable.vue'
import BasePagination from '@/components/ui/BasePagination.vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import DataSourceForm from '@/components/forms/DataSourceForm.vue'
import type { TableColumn } from '@/components/ui/BaseTable.vue'

// 响应式数据
const showAddModal = ref(false)
const editingItem = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = ref({
  name: '',
  type: '',
  status: ''
})

// 表格列定义
const columns: TableColumn[] = [
  { key: 'name', title: '数据源名称', sortable: true },
  { key: 'type', title: '类型' },
  { key: 'host', title: '主机地址' },
  { key: 'port', title: '端口' },
  { key: 'database', title: '数据库' },
  { key: 'status', title: '连接状态' },
  { key: 'createTime', title: '创建时间', sortable: true },
  { key: 'actions', title: '操作', width: '120px' }
]

// 数据源列表
const dataSourceList = ref([
  {
    id: 1,
    name: '用户数据库',
    type: 'mysql',
    host: '*************',
    port: 3306,
    database: 'user_db',
    status: 'connected',
    createTime: '2023-07-15 10:30:00'
  },
  {
    id: 2,
    name: '订单数据库',
    type: 'postgresql',
    host: '*************',
    port: 5432,
    database: 'order_db',
    status: 'connected',
    createTime: '2023-07-14 15:20:00'
  },
  {
    id: 3,
    name: '缓存数据库',
    type: 'redis',
    host: '*************',
    port: 6379,
    database: '0',
    status: 'disconnected',
    createTime: '2023-07-13 09:15:00'
  }
])

// 方法
function getTypeIcon(type: string): string {
  const iconMap: Record<string, string> = {
    mysql: 'fas fa-database text-blue-600',
    postgresql: 'fas fa-database text-blue-800',
    oracle: 'fas fa-database text-red-600',
    mongodb: 'fas fa-leaf text-green-600',
    redis: 'fas fa-memory text-red-500',
    elasticsearch: 'fas fa-search text-yellow-600'
  }
  return iconMap[type] || 'fas fa-database text-gray-600'
}

function getTypeName(type: string): string {
  const nameMap: Record<string, string> = {
    mysql: 'MySQL',
    postgresql: 'PostgreSQL',
    oracle: 'Oracle',
    mongodb: 'MongoDB',
    redis: 'Redis',
    elasticsearch: 'Elasticsearch'
  }
  return nameMap[type] || type.toUpperCase()
}

function getStatusClass(status: string): string {
  const classMap: Record<string, string> = {
    connected: 'tag tag-success',
    disconnected: 'tag tag-warning',
    error: 'tag tag-danger'
  }
  return classMap[status] || 'tag'
}

function getStatusText(status: string): string {
  const textMap: Record<string, string> = {
    connected: '已连接',
    disconnected: '未连接',
    error: '连接异常'
  }
  return textMap[status] || status
}

function handleSearch() {
  console.log('搜索数据源:', searchForm.value)
  // 这里调用API搜索数据源
}

function handleReset() {
  searchForm.value = {
    name: '',
    type: '',
    status: ''
  }
  handleSearch()
}

function handleTest(row: any) {
  console.log('测试连接:', row)
  // 这里调用API测试数据源连接
}

function handleEdit(row: any) {
  editingItem.value = { ...row }
  showAddModal.value = true
}

function handleDelete(row: any) {
  if (confirm('确定要删除这个数据源吗？')) {
    console.log('删除数据源:', row)
    // 这里调用API删除数据源
  }
}

function handleSubmit(data: any) {
  console.log('提交数据源:', data)
  showAddModal.value = false
  editingItem.value = null
  // 这里调用API保存数据源
}

function handleCancel() {
  showAddModal.value = false
  editingItem.value = null
}

function handlePageChange(page: number) {
  currentPage.value = page
  // 这里调用API获取对应页的数据
}

onMounted(() => {
  // 初始化数据
  total.value = dataSourceList.value.length
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
