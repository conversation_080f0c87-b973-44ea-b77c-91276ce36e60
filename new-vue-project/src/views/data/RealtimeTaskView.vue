<template>
  <div>
    <div class="flex items-center mb-6">
      <i class="fas fa-bolt text-primary text-xl mr-3"></i>
      <h1 class="text-2xl font-semibold text-gray-800">实时采集任务管理</h1>
    </div>
    
    <BaseCard>
      <div class="text-center py-12">
        <i class="fas fa-bolt text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-600 mb-2">实时采集任务管理</h3>
        <p class="text-gray-500">此页面功能正在开发中...</p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import BaseCard from '@/components/ui/BaseCard.vue'
</script>
