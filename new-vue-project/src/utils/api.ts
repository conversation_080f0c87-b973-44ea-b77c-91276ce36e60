import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.example.com',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 统一处理响应数据
    const { data } = response
    
    // 如果后端返回的数据格式是 { code, data, message }
    if (data && typeof data === 'object' && 'code' in data) {
      if (data.code === 200 || data.code === 0) {
        return data.data || data
      } else {
        // 业务错误
        const error = new Error(data.message || '请求失败')
        error.name = 'BusinessError'
        return Promise.reject(error)
      }
    }
    
    return data
  },
  (error) => {
    // 处理HTTP错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          window.location.href = '/login'
          break
        case 403:
          console.error('权限不足')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`请求失败: ${status}`)
      }
      
      const errorMessage = data?.message || error.message || '请求失败'
      return Promise.reject(new Error(errorMessage))
    } else if (error.request) {
      // 网络错误
      return Promise.reject(new Error('网络连接失败，请检查网络设置'))
    } else {
      // 其他错误
      return Promise.reject(error)
    }
  }
)

// 通用请求方法
export interface ApiRequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
}

export async function apiRequest<T = any>(
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any,
  config?: ApiRequestConfig
): Promise<T> {
  const requestConfig: AxiosRequestConfig = {
    url,
    method,
    ...config,
  }
  
  if (method === 'GET') {
    requestConfig.params = data
  } else {
    requestConfig.data = data
  }
  
  try {
    const response = await api(requestConfig)
    return response
  } catch (error) {
    // 如果配置了显示错误，可以在这里添加全局错误提示
    if (config?.showError !== false) {
      console.error('API请求错误:', error)
    }
    throw error
  }
}

// 便捷方法
export const get = <T = any>(url: string, params?: any, config?: ApiRequestConfig): Promise<T> =>
  apiRequest<T>(url, 'GET', params, config)

export const post = <T = any>(url: string, data?: any, config?: ApiRequestConfig): Promise<T> =>
  apiRequest<T>(url, 'POST', data, config)

export const put = <T = any>(url: string, data?: any, config?: ApiRequestConfig): Promise<T> =>
  apiRequest<T>(url, 'PUT', data, config)

export const del = <T = any>(url: string, params?: any, config?: ApiRequestConfig): Promise<T> =>
  apiRequest<T>(url, 'DELETE', params, config)

export default api
