import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { DataSource, PaginatedResponse, SearchParams } from '@/types'
import { get, post, put, del } from '@/utils/api'

export const useDataSourceStore = defineStore('dataSource', () => {
  // 状态
  const dataSources = ref<DataSource[]>([])
  const loading = ref(false)
  const currentDataSource = ref<DataSource | null>(null)
  
  // 计算属性
  const connectedCount = computed(() => 
    dataSources.value.filter(ds => ds.status === 'connected').length
  )
  
  const disconnectedCount = computed(() => 
    dataSources.value.filter(ds => ds.status === 'disconnected').length
  )
  
  const errorCount = computed(() => 
    dataSources.value.filter(ds => ds.status === 'error').length
  )
  
  const typeStats = computed(() => {
    const stats: Record<string, number> = {}
    dataSources.value.forEach(ds => {
      stats[ds.type] = (stats[ds.type] || 0) + 1
    })
    return stats
  })

  // 获取数据源列表
  async function fetchDataSources(params?: SearchParams): Promise<PaginatedResponse<DataSource>> {
    loading.value = true
    try {
      const response = await get<PaginatedResponse<DataSource>>('/api/datasources', params)
      dataSources.value = response.list
      return response
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取单个数据源
  async function fetchDataSource(id: string): Promise<DataSource> {
    loading.value = true
    try {
      const response = await get<DataSource>(`/api/datasources/${id}`)
      currentDataSource.value = response
      return response
    } catch (error) {
      console.error('获取数据源详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建数据源
  async function createDataSource(data: Partial<DataSource>): Promise<DataSource> {
    loading.value = true
    try {
      const response = await post<DataSource>('/api/datasources', data)
      dataSources.value.push(response)
      return response
    } catch (error) {
      console.error('创建数据源失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新数据源
  async function updateDataSource(id: string, data: Partial<DataSource>): Promise<DataSource> {
    loading.value = true
    try {
      const response = await put<DataSource>(`/api/datasources/${id}`, data)
      const index = dataSources.value.findIndex(ds => ds.id === id)
      if (index !== -1) {
        dataSources.value[index] = response
      }
      if (currentDataSource.value?.id === id) {
        currentDataSource.value = response
      }
      return response
    } catch (error) {
      console.error('更新数据源失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除数据源
  async function deleteDataSource(id: string): Promise<void> {
    loading.value = true
    try {
      await del(`/api/datasources/${id}`)
      const index = dataSources.value.findIndex(ds => ds.id === id)
      if (index !== -1) {
        dataSources.value.splice(index, 1)
      }
      if (currentDataSource.value?.id === id) {
        currentDataSource.value = null
      }
    } catch (error) {
      console.error('删除数据源失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 测试数据源连接
  async function testConnection(data: Partial<DataSource>): Promise<{ success: boolean; message: string }> {
    loading.value = true
    try {
      const response = await post<{ success: boolean; message: string }>('/api/datasources/test', data)
      return response
    } catch (error) {
      console.error('测试连接失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取数据源表列表
  async function fetchTables(dataSourceId: string): Promise<string[]> {
    try {
      const response = await get<string[]>(`/api/datasources/${dataSourceId}/tables`)
      return response
    } catch (error) {
      console.error('获取表列表失败:', error)
      throw error
    }
  }

  // 获取表字段信息
  async function fetchTableFields(dataSourceId: string, tableName: string): Promise<any[]> {
    try {
      const response = await get<any[]>(`/api/datasources/${dataSourceId}/tables/${tableName}/fields`)
      return response
    } catch (error) {
      console.error('获取表字段失败:', error)
      throw error
    }
  }

  // 预览表数据
  async function previewTableData(dataSourceId: string, tableName: string, limit = 100): Promise<any[]> {
    try {
      const response = await get<any[]>(`/api/datasources/${dataSourceId}/tables/${tableName}/preview`, { limit })
      return response
    } catch (error) {
      console.error('预览表数据失败:', error)
      throw error
    }
  }

  // 清空状态
  function clearState() {
    dataSources.value = []
    currentDataSource.value = null
    loading.value = false
  }

  // 根据ID查找数据源
  function findDataSourceById(id: string): DataSource | undefined {
    return dataSources.value.find(ds => ds.id === id)
  }

  // 根据类型筛选数据源
  function getDataSourcesByType(type: string): DataSource[] {
    return dataSources.value.filter(ds => ds.type === type)
  }

  // 获取可用的数据源
  function getAvailableDataSources(): DataSource[] {
    return dataSources.value.filter(ds => ds.status === 'connected')
  }

  return {
    // 状态
    dataSources,
    loading,
    currentDataSource,
    
    // 计算属性
    connectedCount,
    disconnectedCount,
    errorCount,
    typeStats,
    
    // 方法
    fetchDataSources,
    fetchDataSource,
    createDataSource,
    updateDataSource,
    deleteDataSource,
    testConnection,
    fetchTables,
    fetchTableFields,
    previewTableData,
    clearState,
    findDataSourceById,
    getDataSourcesByType,
    getAvailableDataSources
  }
})
