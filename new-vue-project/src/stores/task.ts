import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { Task, PaginatedResponse, SearchParams } from '@/types'
import { get, post, put, del } from '@/utils/api'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const loading = ref(false)
  const currentTask = ref<Task | null>(null)
  
  // 计算属性
  const runningTasks = computed(() => 
    tasks.value.filter(task => task.status === 'running')
  )
  
  const stoppedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'stopped')
  )
  
  const errorTasks = computed(() => 
    tasks.value.filter(task => task.status === 'error')
  )
  
  const pendingTasks = computed(() => 
    tasks.value.filter(task => task.status === 'pending')
  )
  
  const realtimeTasks = computed(() => 
    tasks.value.filter(task => task.type === 'realtime')
  )
  
  const offlineTasks = computed(() => 
    tasks.value.filter(task => task.type === 'offline')
  )
  
  const taskStats = computed(() => ({
    total: tasks.value.length,
    running: runningTasks.value.length,
    stopped: stoppedTasks.value.length,
    error: errorTasks.value.length,
    pending: pendingTasks.value.length,
    realtime: realtimeTasks.value.length,
    offline: offlineTasks.value.length
  }))

  // 获取任务列表
  async function fetchTasks(params?: SearchParams): Promise<PaginatedResponse<Task>> {
    loading.value = true
    try {
      const response = await get<PaginatedResponse<Task>>('/api/tasks', params)
      tasks.value = response.list
      return response
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取单个任务
  async function fetchTask(id: string): Promise<Task> {
    loading.value = true
    try {
      const response = await get<Task>(`/api/tasks/${id}`)
      currentTask.value = response
      return response
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建任务
  async function createTask(data: Partial<Task>): Promise<Task> {
    loading.value = true
    try {
      const response = await post<Task>('/api/tasks', data)
      tasks.value.push(response)
      return response
    } catch (error) {
      console.error('创建任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新任务
  async function updateTask(id: string, data: Partial<Task>): Promise<Task> {
    loading.value = true
    try {
      const response = await put<Task>(`/api/tasks/${id}`, data)
      const index = tasks.value.findIndex(task => task.id === id)
      if (index !== -1) {
        tasks.value[index] = response
      }
      if (currentTask.value?.id === id) {
        currentTask.value = response
      }
      return response
    } catch (error) {
      console.error('更新任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除任务
  async function deleteTask(id: string): Promise<void> {
    loading.value = true
    try {
      await del(`/api/tasks/${id}`)
      const index = tasks.value.findIndex(task => task.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
      }
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
    } catch (error) {
      console.error('删除任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 启动任务
  async function startTask(id: string): Promise<void> {
    loading.value = true
    try {
      await post(`/api/tasks/${id}/start`)
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.status = 'running'
        task.startTime = new Date().toISOString()
      }
    } catch (error) {
      console.error('启动任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 停止任务
  async function stopTask(id: string): Promise<void> {
    loading.value = true
    try {
      await post(`/api/tasks/${id}/stop`)
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.status = 'stopped'
        task.endTime = new Date().toISOString()
      }
    } catch (error) {
      console.error('停止任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重启任务
  async function restartTask(id: string): Promise<void> {
    loading.value = true
    try {
      await post(`/api/tasks/${id}/restart`)
      const task = tasks.value.find(t => t.id === id)
      if (task) {
        task.status = 'running'
        task.startTime = new Date().toISOString()
        task.endTime = undefined
      }
    } catch (error) {
      console.error('重启任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取任务日志
  async function fetchTaskLogs(id: string, params?: { page?: number; pageSize?: number }): Promise<any[]> {
    try {
      const response = await get<any[]>(`/api/tasks/${id}/logs`, params)
      return response
    } catch (error) {
      console.error('获取任务日志失败:', error)
      throw error
    }
  }

  // 获取任务统计信息
  async function fetchTaskStatistics(id: string): Promise<any> {
    try {
      const response = await get(`/api/tasks/${id}/statistics`)
      return response
    } catch (error) {
      console.error('获取任务统计失败:', error)
      throw error
    }
  }

  // 批量操作任务
  async function batchOperateTasks(ids: string[], operation: 'start' | 'stop' | 'delete'): Promise<void> {
    loading.value = true
    try {
      await post(`/api/tasks/batch/${operation}`, { ids })
      
      // 更新本地状态
      if (operation === 'delete') {
        tasks.value = tasks.value.filter(task => !ids.includes(task.id))
      } else {
        tasks.value.forEach(task => {
          if (ids.includes(task.id)) {
            if (operation === 'start') {
              task.status = 'running'
              task.startTime = new Date().toISOString()
            } else if (operation === 'stop') {
              task.status = 'stopped'
              task.endTime = new Date().toISOString()
            }
          }
        })
      }
    } catch (error) {
      console.error('批量操作任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 清空状态
  function clearState() {
    tasks.value = []
    currentTask.value = null
    loading.value = false
  }

  // 根据ID查找任务
  function findTaskById(id: string): Task | undefined {
    return tasks.value.find(task => task.id === id)
  }

  // 根据数据源ID获取任务
  function getTasksByDataSource(dataSourceId: string): Task[] {
    return tasks.value.filter(task => task.dataSourceId === dataSourceId)
  }

  // 根据状态获取任务
  function getTasksByStatus(status: string): Task[] {
    return tasks.value.filter(task => task.status === status)
  }

  // 根据类型获取任务
  function getTasksByType(type: string): Task[] {
    return tasks.value.filter(task => task.type === type)
  }

  return {
    // 状态
    tasks,
    loading,
    currentTask,
    
    // 计算属性
    runningTasks,
    stoppedTasks,
    errorTasks,
    pendingTasks,
    realtimeTasks,
    offlineTasks,
    taskStats,
    
    // 方法
    fetchTasks,
    fetchTask,
    createTask,
    updateTask,
    deleteTask,
    startTask,
    stopTask,
    restartTask,
    fetchTaskLogs,
    fetchTaskStatistics,
    batchOperateTasks,
    clearState,
    findTaskById,
    getTasksByDataSource,
    getTasksByStatus,
    getTasksByType
  }
})
