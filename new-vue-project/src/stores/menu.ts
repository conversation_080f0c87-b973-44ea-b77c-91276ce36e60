import { ref } from 'vue'
import { defineStore } from 'pinia'

export interface MenuItem {
  id: string
  title: string
  icon: string
  path?: string
  children?: MenuItem[]
  expanded?: boolean
}

export const useMenuStore = defineStore('menu', () => {
  const collapsed = ref(false)
  const activeMenuId = ref('')
  const expandedMenus = ref<Set<string>>(new Set())
  
  // 菜单数据
  const menuItems = ref<MenuItem[]>([
    {
      id: 'home',
      title: '首页',
      icon: 'fas fa-home',
      path: '/'
    },
    {
      id: 'data',
      title: '数据融通',
      icon: 'fas fa-database',
      children: [
        {
          id: 'data-source',
          title: '数据源管理',
          icon: '',
          path: '/data-source'
        },
        {
          id: 'offline-task',
          title: '离线采集任务管理',
          icon: '',
          path: '/offline-task'
        },
        {
          id: 'realtime-task',
          title: '实时采集任务管理',
          icon: '',
          path: '/realtime-task'
        }
      ]
    },
    {
      id: 'insight',
      title: '智能洞察分析',
      icon: 'fas fa-chart-pie',
      children: [
        {
          id: 'report-management',
          title: '运营报告管理',
          icon: '',
          path: '/report-management'
        },
        {
          id: 'bulletin-management',
          title: '运营通报管理',
          icon: '',
          path: '/bulletin-management'
        },
        {
          id: 'bulletin-browse-statistics',
          title: '运营通报浏览查询统计',
          icon: '',
          path: '/bulletin-browse-statistics'
        },
        {
          id: 'bulletin-generation',
          title: '运营通报生成与审核',
          icon: '',
          path: '/bulletin-generation'
        }
      ]
    },
    {
      id: 'operation',
      title: '运营视图',
      icon: 'fas fa-server',
      children: [
        {
          id: 'intelligent-query',
          title: '智能问数',
          icon: '',
          path: '/intelligent-query'
        },
        {
          id: 'operation-views',
          title: '大屏模板',
          icon: '',
          path: '/operation-views'
        },
        {
          id: 'theme-management',
          title: '画布管理',
          icon: '',
          path: '/theme-management'
        },
        {
          id: 'custom-report',
          title: '自定义报表',
          icon: '',
          children: [
            {
              id: 'custom-report-display',
              title: '自定义报表展示',
              icon: '',
              path: '/custom-report-display'
            },
            {
              id: 'report-history',
              title: '报表历史记录',
              icon: '',
              path: '/report-history'
            },
            {
              id: 'data-permission-control',
              title: '数据权限控制',
              icon: '',
              path: '/data-permission-control'
            }
          ]
        },
        {
          id: 'view-interaction',
          title: '视图交互',
          icon: '',
          children: [
            {
              id: 'indicator-bloodline',
              title: '指标血缘管理',
              icon: '',
              path: '/indicator-bloodline'
            },
            {
              id: 'map-view',
              title: '地图',
              icon: '',
              path: '/map-view'
            }
          ]
        },
        {
          id: 'template-management',
          title: '模板管理',
          icon: '',
          children: [
            {
              id: 'template-list',
              title: '模板列表',
              icon: '',
              path: '/template-list'
            },
            {
              id: 'template-permission',
              title: '模板控制',
              icon: '',
              path: '/template-permission'
            }
          ]
        }
      ]
    },
    {
      id: 'portal',
      title: '统一运营门户',
      icon: 'fas fa-database',
      children: [
        {
          id: 'permission-manage',
          title: '视图权限管理',
          icon: '',
          path: '/permission-manage'
        },
        {
          id: 'permission-log-management',
          title: '权限日志管理',
          icon: '',
          path: '/permission-log-management'
        }
      ]
    },
    {
      id: 'penetration',
      title: '五级穿透调度',
      icon: 'fas fa-tasks',
      children: [
        {
          id: 'task-scheduling',
          title: '任务调度看板',
          icon: '',
          path: '/task-scheduling'
        },
        {
          id: 'task-scheduling-export',
          title: '统计分析与报表管理',
          icon: '',
          path: '/task-scheduling-export'
        },
        {
          id: 'task-scheduling-group',
          title: '任务调度',
          icon: '',
          children: [
            {
              id: 'data-masking-process',
              title: '数据脱密处理',
              icon: '',
              path: '/data-masking-process'
            },
            {
              id: 'alarm-notification',
              title: '告警通知',
              icon: '',
              path: '/alarm-notification'
            },
            {
              id: 'alarm-generation',
              title: '告警生成',
              icon: '',
              path: '/alarm-generation'
            }
          ]
        },
        {
          id: 'five-level-scheduling',
          title: '五级调度',
          icon: '',
          children: [
            {
              id: 'penetration-page',
              title: '维护穿透页面',
              icon: '',
              path: '/penetration-page'
            }
          ]
        }
      ]
    },
    {
      id: 'devops',
      title: 'DevOps 平台',
      icon: 'fas fa-chart-line',
      children: [
        {
          id: 'devops-dashboard',
          title: 'DevOps 总览',
          icon: '',
          path: '/devops-dashboard'
        },
        {
          id: 'pipeline-management',
          title: 'CI/CD 流水线',
          icon: '',
          path: '/pipeline-management'
        },
        {
          id: 'deployment-management',
          title: '容器部署',
          icon: '',
          path: '/deployment-management'
        },
        {
          id: 'monitoring-center',
          title: '监控中心',
          icon: '',
          path: '/monitoring-center'
        },
        {
          id: 'service-topology',
          title: '服务拓扑',
          icon: '',
          path: '/service-topology'
        }
      ]
    }
  ])
  
  // 切换侧边栏折叠状态
  function toggleCollapsed() {
    collapsed.value = !collapsed.value
  }
  
  // 设置活动菜单
  function setActiveMenu(menuId: string) {
    activeMenuId.value = menuId
  }
  
  // 切换菜单展开状态
  function toggleMenuExpanded(menuId: string) {
    if (expandedMenus.value.has(menuId)) {
      expandedMenus.value.delete(menuId)
    } else {
      expandedMenus.value.add(menuId)
    }
  }
  
  // 展开菜单
  function expandMenu(menuId: string) {
    expandedMenus.value.add(menuId)
  }
  
  // 收起菜单
  function collapseMenu(menuId: string) {
    expandedMenus.value.delete(menuId)
  }
  
  // 收起所有菜单
  function collapseAllMenus() {
    expandedMenus.value.clear()
  }
  
  // 检查菜单是否展开
  function isMenuExpanded(menuId: string): boolean {
    return expandedMenus.value.has(menuId)
  }

  return {
    collapsed,
    activeMenuId,
    expandedMenus,
    menuItems,
    toggleCollapsed,
    setActiveMenu,
    toggleMenuExpanded,
    expandMenu,
    collapseMenu,
    collapseAllMenus,
    isMenuExpanded
  }
})
