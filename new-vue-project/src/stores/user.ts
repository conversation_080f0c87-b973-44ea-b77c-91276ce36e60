import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export interface User {
  id: string
  name: string
  email: string
  avatar: string
  role: string
  permissions: string[]
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!user.value)
  
  // 登录
  function login(userData: User) {
    user.value = userData
    localStorage.setItem('user', JSON.stringify(userData))
  }
  
  // 登出
  function logout() {
    user.value = null
    localStorage.removeItem('user')
  }
  
  // 初始化用户信息（从localStorage恢复）
  function initUser() {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        localStorage.removeItem('user')
      }
    }
  }
  
  // 检查权限
  function hasPermission(permission: string): boolean {
    return user.value?.permissions.includes(permission) || false
  }
  
  // 更新用户信息
  function updateUser(userData: Partial<User>) {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  return {
    user,
    isLoggedIn,
    login,
    logout,
    initUser,
    hasPermission,
    updateUser
  }
})
