// 基础类型定义

// 通用响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 分页响应
export interface PaginatedResponse<T> {
  list: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 用户相关类型
export interface User {
  id: string
  username: string
  name: string
  email: string
  phone?: string
  avatar?: string
  role: string
  permissions: string[]
  status: 'active' | 'inactive' | 'locked'
  createTime: string
  updateTime: string
  lastLoginTime?: string
}

// 数据源类型
export interface DataSource {
  id: string
  name: string
  type: 'mysql' | 'postgresql' | 'oracle' | 'mongodb' | 'redis' | 'elasticsearch' | 'kafka' | 'file' | 'api'
  host: string
  port: number
  database?: string
  schema?: string
  username?: string
  password?: string
  params?: string
  description?: string
  status: 'connected' | 'disconnected' | 'error'
  createTime: string
  updateTime: string
  creator: string
}

// 任务类型
export interface Task {
  id: string
  name: string
  type: 'realtime' | 'offline'
  dataSourceId: string
  dataSourceName?: string
  status: 'running' | 'stopped' | 'error' | 'pending'
  config: TaskConfig
  schedule?: TaskSchedule
  statistics?: TaskStatistics
  createTime: string
  updateTime: string
  startTime?: string
  endTime?: string
  creator: string
}

export interface TaskConfig {
  sourceTable?: string
  targetTable?: string
  fields?: string[]
  filters?: Record<string, any>
  transformRules?: TransformRule[]
}

export interface TaskSchedule {
  type: 'once' | 'interval' | 'cron'
  interval?: number // 间隔时间（秒）
  cron?: string // cron表达式
  startTime?: string
  endTime?: string
}

export interface TaskStatistics {
  totalRecords: number
  processedRecords: number
  errorRecords: number
  successRate: number
  avgProcessTime: number
  lastRunTime?: string
}

export interface TransformRule {
  field: string
  type: 'map' | 'filter' | 'format' | 'calculate'
  rule: string
  params?: Record<string, any>
}

// 报告类型
export interface Report {
  id: string
  name: string
  type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  period: string
  status: 'draft' | 'published' | 'archived'
  content: ReportContent
  template?: string
  author: string
  createTime: string
  updateTime: string
  publishTime?: string
}

export interface ReportContent {
  title: string
  summary: string
  sections: ReportSection[]
  charts: ChartConfig[]
  attachments?: string[]
}

export interface ReportSection {
  id: string
  title: string
  content: string
  order: number
  type: 'text' | 'chart' | 'table' | 'image'
}

// 图表配置
export interface ChartConfig {
  id: string
  title: string
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area' | 'gauge'
  dataSource: string
  query: string
  options: Record<string, any>
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

// 权限类型
export interface Permission {
  id: string
  name: string
  code: string
  type: 'menu' | 'button' | 'data'
  resource: string
  description?: string
  parentId?: string
  children?: Permission[]
}

export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: string[]
  status: 'active' | 'inactive'
  createTime: string
  updateTime: string
}

// 告警类型
export interface Alert {
  id: string
  title: string
  content: string
  level: 'info' | 'warning' | 'error' | 'critical'
  type: 'system' | 'task' | 'data' | 'security'
  source: string
  status: 'new' | 'processing' | 'resolved' | 'closed'
  createTime: string
  updateTime: string
  resolveTime?: string
  assignee?: string
}

// 日志类型
export interface Log {
  id: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  module: string
  action: string
  userId?: string
  ip?: string
  userAgent?: string
  params?: Record<string, any>
  createTime: string
}

// 模板类型
export interface Template {
  id: string
  name: string
  type: 'dashboard' | 'report' | 'chart'
  category: string
  description?: string
  config: Record<string, any>
  preview?: string
  status: 'active' | 'inactive'
  isPublic: boolean
  creator: string
  createTime: string
  updateTime: string
  usageCount: number
}

// 仪表板类型
export interface Dashboard {
  id: string
  name: string
  description?: string
  layout: DashboardLayout
  widgets: Widget[]
  permissions: string[]
  isPublic: boolean
  creator: string
  createTime: string
  updateTime: string
}

export interface DashboardLayout {
  rows: number
  cols: number
  margin: [number, number]
  containerPadding: [number, number]
}

export interface Widget {
  id: string
  type: 'chart' | 'table' | 'text' | 'image' | 'iframe'
  title: string
  config: Record<string, any>
  layout: {
    x: number
    y: number
    w: number
    h: number
  }
  dataSource?: string
  refreshInterval?: number
}

// 系统配置类型
export interface SystemConfig {
  id: string
  key: string
  value: string
  type: 'string' | 'number' | 'boolean' | 'json'
  category: string
  description?: string
  isPublic: boolean
  updateTime: string
}

// 文件类型
export interface FileInfo {
  id: string
  name: string
  originalName: string
  path: string
  size: number
  mimeType: string
  category: string
  description?: string
  uploadTime: string
  uploader: string
}

// 搜索参数类型
export interface SearchParams {
  keyword?: string
  filters?: Record<string, any>
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  pagination?: PaginationParams
}
