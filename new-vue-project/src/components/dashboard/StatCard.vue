<template>
  <div class="bg-white rounded-lg shadow-card p-6 hover:shadow-lg transition-shadow duration-200">
    <div class="flex items-center">
      <div :class="iconClasses" class="flex items-center justify-center w-12 h-12 rounded-lg">
        <i :class="icon" class="text-xl"></i>
      </div>
      <div class="ml-4 flex-1">
        <div class="text-2xl font-bold text-gray-900">{{ value }}</div>
        <div class="text-sm text-gray-600">{{ label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  icon: string
  value: string | number
  label: string
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo'
}

const props = withDefaults(defineProps<Props>(), {
  color: 'blue'
})

const iconClasses = computed(() => {
  const colorMap = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    red: 'bg-red-100 text-red-600',
    purple: 'bg-purple-100 text-purple-600',
    indigo: 'bg-indigo-100 text-indigo-600'
  }
  
  return colorMap[props.color]
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
