<template>
  <form @submit.prevent="handleSubmit">
    <div class="space-y-4">
      <!-- 基本信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-group">
          <label>数据源名称 <span class="text-red-500">*</span></label>
          <input
            v-model="form.name"
            type="text"
            placeholder="请输入数据源名称"
            required
          />
        </div>
        
        <div class="form-group">
          <label>数据源类型 <span class="text-red-500">*</span></label>
          <select v-model="form.type" required @change="handleTypeChange">
            <option value="">请选择数据源类型</option>
            <option value="mysql">MySQL</option>
            <option value="postgresql">PostgreSQL</option>
            <option value="oracle">Oracle</option>
            <option value="mongodb">MongoDB</option>
            <option value="redis">Redis</option>
            <option value="elasticsearch">Elasticsearch</option>
          </select>
        </div>
      </div>

      <!-- 连接信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-group">
          <label>主机地址 <span class="text-red-500">*</span></label>
          <input
            v-model="form.host"
            type="text"
            placeholder="请输入主机地址"
            required
          />
        </div>
        
        <div class="form-group">
          <label>端口 <span class="text-red-500">*</span></label>
          <input
            v-model.number="form.port"
            type="number"
            :placeholder="getDefaultPort(form.type)"
            required
          />
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-group">
          <label>数据库名称</label>
          <input
            v-model="form.database"
            type="text"
            placeholder="请输入数据库名称"
          />
        </div>
        
        <div class="form-group" v-if="needsSchema">
          <label>Schema</label>
          <input
            v-model="form.schema"
            type="text"
            placeholder="请输入Schema"
          />
        </div>
      </div>

      <!-- 认证信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4" v-if="needsAuth">
        <div class="form-group">
          <label>用户名</label>
          <input
            v-model="form.username"
            type="text"
            placeholder="请输入用户名"
          />
        </div>
        
        <div class="form-group">
          <label>密码</label>
          <input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
          />
        </div>
      </div>

      <!-- 连接参数 -->
      <div class="form-group">
        <label>连接参数</label>
        <textarea
          v-model="form.params"
          rows="3"
          placeholder="请输入连接参数，如：charset=utf8&timeout=30"
        ></textarea>
      </div>

      <!-- 描述 -->
      <div class="form-group">
        <label>描述</label>
        <textarea
          v-model="form.description"
          rows="3"
          placeholder="请输入数据源描述"
        ></textarea>
      </div>
    </div>

    <!-- 表单按钮 -->
    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
      <BaseButton
        type="button"
        variant="outline"
        @click="handleTest"
        :loading="testing"
      >
        测试连接
      </BaseButton>
      <BaseButton
        type="button"
        variant="outline"
        @click="$emit('cancel')"
      >
        取消
      </BaseButton>
      <BaseButton
        type="submit"
        :loading="submitting"
      >
        确认
      </BaseButton>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import BaseButton from '@/components/ui/BaseButton.vue'

interface Props {
  data?: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  submit: [data: any]
  cancel: []
}>()

// 响应式数据
const submitting = ref(false)
const testing = ref(false)

// 表单数据
const form = ref({
  name: '',
  type: '',
  host: '',
  port: null as number | null,
  database: '',
  schema: '',
  username: '',
  password: '',
  params: '',
  description: ''
})

// 计算属性
const needsAuth = computed(() => {
  return ['mysql', 'postgresql', 'oracle', 'mongodb'].includes(form.value.type)
})

const needsSchema = computed(() => {
  return ['postgresql', 'oracle'].includes(form.value.type)
})

// 方法
function getDefaultPort(type: string): string {
  const portMap: Record<string, number> = {
    mysql: 3306,
    postgresql: 5432,
    oracle: 1521,
    mongodb: 27017,
    redis: 6379,
    elasticsearch: 9200
  }
  return portMap[type]?.toString() || ''
}

function handleTypeChange() {
  // 当类型改变时，自动设置默认端口
  const defaultPort = getDefaultPort(form.value.type)
  if (defaultPort && !form.value.port) {
    form.value.port = parseInt(defaultPort)
  }
}

async function handleTest() {
  testing.value = true
  try {
    // 这里调用API测试连接
    console.log('测试连接:', form.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('连接测试成功！')
  } catch (error) {
    alert('连接测试失败：' + error)
  } finally {
    testing.value = false
  }
}

async function handleSubmit() {
  submitting.value = true
  try {
    // 这里调用API保存数据源
    console.log('保存数据源:', form.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('submit', { ...form.value })
  } catch (error) {
    alert('保存失败：' + error)
  } finally {
    submitting.value = false
  }
}

// 监听props变化，初始化表单数据
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(form.value, newData)
  }
}, { immediate: true })

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
