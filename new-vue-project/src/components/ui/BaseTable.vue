<template>
  <div class="table-container">
    <table class="table">
      <thead>
        <tr>
          <th
            v-for="column in columns"
            :key="column.key"
            :class="column.headerClass"
            :style="{ width: column.width }"
          >
            <div class="flex items-center">
              {{ column.title }}
              <i
                v-if="column.sortable"
                :class="getSortIcon(column.key)"
                class="ml-2 cursor-pointer"
                @click="handleSort(column.key)"
              ></i>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(row, index) in sortedData"
          :key="getRowKey(row, index)"
          :class="getRowClass(row, index)"
        >
          <td
            v-for="column in columns"
            :key="column.key"
            :class="column.cellClass"
          >
            <slot
              :name="column.key"
              :row="row"
              :value="getColumnValue(row, column.key)"
              :index="index"
            >
              {{ getColumnValue(row, column.key) }}
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
    
    <!-- 空状态 -->
    <div v-if="sortedData.length === 0" class="text-center py-8 text-gray-500">
      <slot name="empty">
        <i class="fas fa-inbox text-4xl mb-2"></i>
        <div>暂无数据</div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

export interface TableColumn {
  key: string
  title: string
  width?: string
  sortable?: boolean
  headerClass?: string
  cellClass?: string
}

interface Props {
  columns: TableColumn[]
  data: any[]
  rowKey?: string | ((row: any) => string)
  striped?: boolean
  hoverable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  rowKey: 'id',
  striped: false,
  hoverable: true
})

const sortKey = ref<string>('')
const sortOrder = ref<'asc' | 'desc'>('asc')

// 排序后的数据
const sortedData = computed(() => {
  if (!sortKey.value) return props.data
  
  return [...props.data].sort((a, b) => {
    const aValue = getColumnValue(a, sortKey.value)
    const bValue = getColumnValue(b, sortKey.value)
    
    if (aValue === bValue) return 0
    
    const result = aValue > bValue ? 1 : -1
    return sortOrder.value === 'asc' ? result : -result
  })
})

// 获取列值
function getColumnValue(row: any, key: string) {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

// 获取行键
function getRowKey(row: any, index: number): string {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row)
  }
  return getColumnValue(row, props.rowKey) || index.toString()
}

// 获取行样式类
function getRowClass(row: any, index: number): string {
  const classes = []
  
  if (props.striped && index % 2 === 1) {
    classes.push('bg-gray-50')
  }
  
  if (props.hoverable) {
    classes.push('hover:bg-gray-100')
  }
  
  return classes.join(' ')
}

// 处理排序
function handleSort(key: string) {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
}

// 获取排序图标
function getSortIcon(key: string): string {
  if (sortKey.value !== key) {
    return 'fas fa-sort text-gray-400'
  }
  
  return sortOrder.value === 'asc' 
    ? 'fas fa-sort-up text-primary' 
    : 'fas fa-sort-down text-primary'
}
</script>

<style scoped>
.table-container {
  @apply overflow-x-auto;
}

.table {
  @apply w-full border-collapse;
}

.table th {
  @apply px-4 py-3 text-left border-b border-gray-200 bg-gray-50 font-medium text-gray-700;
}

.table td {
  @apply px-4 py-3 border-b border-gray-200;
}

.table tr:last-child td {
  @apply border-b-0;
}
</style>
