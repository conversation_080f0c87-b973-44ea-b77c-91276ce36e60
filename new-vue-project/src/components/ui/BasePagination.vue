<template>
  <div class="pagination" v-if="totalPages > 1">
    <!-- 上一页 -->
    <button
      :disabled="currentPage === 1"
      @click="handlePageChange(currentPage - 1)"
      class="pagination-item"
      :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
    >
      <i class="fas fa-chevron-left"></i>
    </button>
    
    <!-- 页码 -->
    <template v-for="page in visiblePages" :key="page">
      <button
        v-if="page !== '...'"
        @click="handlePageChange(page as number)"
        class="pagination-item"
        :class="{ active: page === currentPage }"
      >
        {{ page }}
      </button>
      <span v-else class="pagination-item cursor-default">...</span>
    </template>
    
    <!-- 下一页 -->
    <button
      :disabled="currentPage === totalPages"
      @click="handlePageChange(currentPage + 1)"
      class="pagination-item"
      :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
    >
      <i class="fas fa-chevron-right"></i>
    </button>
    
    <!-- 页面信息 -->
    <div v-if="showInfo" class="ml-4 text-sm text-gray-600">
      共 {{ total }} 条，第 {{ currentPage }} / {{ totalPages }} 页
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentPage: number
  total: number
  pageSize: number
  showInfo?: boolean
  maxVisiblePages?: number
}

const props = withDefaults(defineProps<Props>(), {
  showInfo: true,
  maxVisiblePages: 7
})

const emit = defineEmits<{
  'update:currentPage': [page: number]
  change: [page: number]
}>()

// 总页数
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

// 可见页码
const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const { currentPage } = props
  const { maxVisiblePages } = props
  
  if (totalPages.value <= maxVisiblePages) {
    // 总页数小于等于最大可见页数，显示所有页码
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i)
    }
  } else {
    // 总页数大于最大可见页数，需要省略
    const halfVisible = Math.floor(maxVisiblePages / 2)
    
    if (currentPage <= halfVisible + 1) {
      // 当前页在前半部分
      for (let i = 1; i <= maxVisiblePages - 2; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(totalPages.value)
    } else if (currentPage >= totalPages.value - halfVisible) {
      // 当前页在后半部分
      pages.push(1)
      pages.push('...')
      for (let i = totalPages.value - maxVisiblePages + 3; i <= totalPages.value; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间部分
      pages.push(1)
      pages.push('...')
      for (let i = currentPage - halfVisible + 1; i <= currentPage + halfVisible - 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(totalPages.value)
    }
  }
  
  return pages
})

function handlePageChange(page: number) {
  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('update:currentPage', page)
    emit('change', page)
  }
}
</script>

<style scoped>
/* 分页样式已在全局CSS中定义 */
</style>
