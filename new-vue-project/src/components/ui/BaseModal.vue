<template>
  <Teleport to="body">
    <div
      v-if="modelValue"
      class="modal show"
      @click="handleBackdropClick"
    >
      <div
        :class="modalClasses"
        @click.stop
      >
        <!-- 模态框头部 -->
        <div v-if="$slots.header || title" class="modal-header">
          <slot name="header">
            <div class="modal-title">
              <i v-if="icon" :class="icon" class="mr-2"></i>
              {{ title }}
            </div>
          </slot>
          <button
            v-if="closable"
            @click="handleClose"
            class="modal-close"
          >
            &times;
          </button>
        </div>
        
        <!-- 模态框内容 -->
        <div class="modal-body">
          <slot></slot>
        </div>
        
        <!-- 模态框底部 -->
        <div v-if="$slots.footer" class="modal-footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  icon?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  closeOnBackdrop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  closeOnBackdrop: true
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

const modalClasses = computed(() => {
  const baseClasses = 'modal-content'
  
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4'
  }
  
  return [baseClasses, sizeClasses[props.size]].join(' ')
})

function handleClose() {
  emit('update:modelValue', false)
  emit('close')
}

function handleBackdropClick() {
  if (props.closeOnBackdrop) {
    handleClose()
  }
}

// 监听ESC键关闭模态框
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.modelValue && props.closable) {
    handleClose()
  }
}

// 防止背景滚动
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden'
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.body.style.overflow = ''
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style scoped>
/* 模态框样式已在全局CSS中定义 */
</style>
