<template>
  <div>
    <!-- 菜单项 -->
    <div
      @click="handleClick"
      class="flex items-center px-3 py-2 rounded-md cursor-pointer transition-colors duration-200"
      :class="[
        isActive ? 'bg-primary text-white' : 'text-gray-700 hover:bg-gray-100',
        { 'justify-center': menuStore.collapsed && level === 0 }
      ]"
    >
      <!-- 图标 -->
      <i
        v-if="item.icon"
        :class="item.icon"
        class="text-lg"
        :class="{ 'mr-3': !menuStore.collapsed || level > 0 }"
      ></i>
      
      <!-- 标题 -->
      <span
        v-if="!menuStore.collapsed || level > 0"
        class="flex-1 text-sm font-medium"
        :style="{ paddingLeft: `${level * 16}px` }"
      >
        {{ item.title }}
      </span>
      
      <!-- 展开/收起箭头 -->
      <i
        v-if="hasChildren && (!menuStore.collapsed || level > 0)"
        class="fas fa-chevron-right text-xs transition-transform duration-200"
        :class="{ 'rotate-90': isExpanded }"
      ></i>
    </div>
    
    <!-- 子菜单 -->
    <div
      v-if="hasChildren && isExpanded && (!menuStore.collapsed || level > 0)"
      class="mt-1 space-y-1"
    >
      <SideBarItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
      />
    </div>
    
    <!-- 折叠状态下的悬浮提示 -->
    <div
      v-if="menuStore.collapsed && level === 0"
      class="absolute left-16 top-0 bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 z-50"
      :class="{ 'opacity-100': showTooltip }"
    >
      {{ item.title }}
      <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-800"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMenuStore } from '@/stores/menu'
import type { MenuItem } from '@/stores/menu'

interface Props {
  item: MenuItem
  level: number
}

const props = defineProps<Props>()
const router = useRouter()
const route = useRoute()
const menuStore = useMenuStore()

const showTooltip = ref(false)

// 计算属性
const hasChildren = computed(() => props.item.children && props.item.children.length > 0)
const isExpanded = computed(() => menuStore.isMenuExpanded(props.item.id))
const isActive = computed(() => {
  if (props.item.path) {
    return route.path === props.item.path
  }
  return false
})

// 处理点击事件
function handleClick() {
  if (hasChildren.value) {
    // 有子菜单，切换展开状态
    menuStore.toggleMenuExpanded(props.item.id)
    
    // 如果侧边栏是折叠状态且是顶级菜单，展开侧边栏
    if (menuStore.collapsed && props.level === 0) {
      menuStore.toggleCollapsed()
    }
  } else if (props.item.path) {
    // 没有子菜单且有路径，进行路由跳转
    router.push(props.item.path)
    menuStore.setActiveMenu(props.item.id)
  }
}

// 鼠标悬停事件（仅在折叠状态下的顶级菜单显示）
function handleMouseEnter() {
  if (menuStore.collapsed && props.level === 0) {
    showTooltip.value = true
  }
}

function handleMouseLeave() {
  showTooltip.value = false
}
</script>

<style scoped>
.rotate-90 {
  transform: rotate(90deg);
}
</style>
