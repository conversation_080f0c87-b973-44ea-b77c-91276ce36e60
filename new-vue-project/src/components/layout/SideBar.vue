<template>
  <aside
    class="fixed left-0 top-16 bottom-0 bg-white shadow-sm border-r border-gray-200 transition-all duration-300 z-30 overflow-y-auto"
    :class="{ 'w-64': !menuStore.collapsed, 'w-16': menuStore.collapsed }"
  >
    <nav class="p-4">
      <ul class="space-y-1">
        <li v-for="item in menuStore.menuItems" :key="item.id">
          <SideBarItem :item="item" :level="0" />
        </li>
      </ul>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { useMenuStore } from '@/stores/menu'
import SideBarItem from './SideBarItem.vue'

const menuStore = useMenuStore()
</script>

<style scoped>
/* 自定义滚动条样式 */
aside::-webkit-scrollbar {
  width: 4px;
}

aside::-webkit-scrollbar-track {
  background: #f1f1f1;
}

aside::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

aside::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
