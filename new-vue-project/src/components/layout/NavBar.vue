<template>
  <nav class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-40 h-16">
    <div class="flex items-center justify-between h-full px-4">
      <!-- 左侧：Logo和标题 -->
      <div class="flex items-center">
        <button
          @click="menuStore.toggleCollapsed()"
          class="p-2 rounded-md hover:bg-gray-100 mr-4"
        >
          <i class="fas fa-bars text-gray-600"></i>
        </button>
        
        <div class="flex items-center">
          <i class="fas fa-chart-line text-primary text-xl mr-2"></i>
          <span class="text-xl font-semibold text-gray-800">数智化运营平台</span>
        </div>
      </div>

      <!-- 右侧：通知和用户菜单 -->
      <div class="flex items-center space-x-4">
        <!-- 通知下拉菜单 -->
        <div class="relative">
          <button
            @click="toggleNotifications"
            class="p-2 rounded-md hover:bg-gray-100 relative"
          >
            <i class="fas fa-bell text-gray-600"></i>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              3
            </span>
          </button>
          
          <!-- 通知下拉内容 -->
          <div
            v-show="showNotifications"
            class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg border border-gray-200 z-50"
          >
            <div class="p-4 border-b border-gray-200">
              <h3 class="text-sm font-medium text-gray-900">通知</h3>
            </div>
            <div class="max-h-64 overflow-y-auto">
              <div
                v-for="notification in notifications"
                :key="notification.id"
                class="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
              >
                <div class="font-medium text-sm text-gray-900">{{ notification.title }}</div>
                <div class="text-xs text-gray-500 mt-1">{{ notification.message }}</div>
                <div class="text-xs text-gray-400 mt-1">{{ notification.time }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户下拉菜单 -->
        <div class="relative">
          <button
            @click="toggleUserMenu"
            class="flex items-center p-2 rounded-md hover:bg-gray-100"
          >
            <img
              :src="userStore.user?.avatar || 'https://picsum.photos/id/1005/40/40'"
              alt="用户头像"
              class="w-8 h-8 rounded-full mr-2"
            />
            <span class="text-sm text-gray-700">{{ userStore.user?.name || '管理员' }}</span>
            <i class="fas fa-chevron-down text-gray-400 ml-1 text-xs"></i>
          </button>
          
          <!-- 用户下拉内容 -->
          <div
            v-show="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
          >
            <div class="py-1">
              <a
                href="#"
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <i class="fas fa-user mr-3"></i>
                个人中心
              </a>
              <a
                href="#"
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <i class="fas fa-cog mr-3"></i>
                系统设置
              </a>
              <div class="border-t border-gray-100"></div>
              <button
                @click="handleLogout"
                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <i class="fas fa-sign-out-alt mr-3"></i>
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useMenuStore } from '@/stores/menu'
import { useUserStore } from '@/stores/user'

const menuStore = useMenuStore()
const userStore = useUserStore()

const showNotifications = ref(false)
const showUserMenu = ref(false)

// 模拟通知数据
const notifications = ref([
  {
    id: 1,
    title: '新任务通知',
    message: '您有3个新任务需要处理',
    time: '2分钟前'
  },
  {
    id: 2,
    title: '数据采集完成',
    message: '昨日数据采集已完成',
    time: '1小时前'
  },
  {
    id: 3,
    title: '系统更新',
    message: '平台将于今晚23:00进行维护',
    time: '3小时前'
  }
])

function toggleNotifications() {
  showNotifications.value = !showNotifications.value
  showUserMenu.value = false
}

function toggleUserMenu() {
  showUserMenu.value = !showUserMenu.value
  showNotifications.value = false
}

function handleLogout() {
  userStore.logout()
  // 这里可以添加跳转到登录页的逻辑
  console.log('用户已退出登录')
}

// 点击外部关闭下拉菜单
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showNotifications.value = false
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
