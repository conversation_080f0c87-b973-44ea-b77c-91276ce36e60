<template>
  <nav class="breadcrumb mb-6" v-if="breadcrumbs.length > 0">
    <div
      v-for="(item, index) in breadcrumbs"
      :key="index"
      class="breadcrumb-item"
      :class="{ active: index === breadcrumbs.length - 1 }"
    >
      <router-link
        v-if="item.path && index < breadcrumbs.length - 1"
        :to="item.path"
        class="text-primary hover:text-secondary transition-colors duration-200"
      >
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMenuStore } from '@/stores/menu'

const route = useRoute()
const menuStore = useMenuStore()

interface BreadcrumbItem {
  title: string
  path?: string
}

// 根据当前路由生成面包屑
const breadcrumbs = computed(() => {
  const items: BreadcrumbItem[] = []
  
  // 添加首页
  items.push({ title: '首页', path: '/' })
  
  // 根据当前路由查找对应的菜单项
  const currentPath = route.path
  const menuItem = findMenuItemByPath(menuStore.menuItems, currentPath)
  
  if (menuItem) {
    // 获取菜单项的层级路径
    const menuPath = getMenuPath(menuStore.menuItems, menuItem.id)
    
    // 添加菜单路径到面包屑（排除首页）
    menuPath.forEach(item => {
      if (item.path !== '/') {
        items.push({
          title: item.title,
          path: item.path
        })
      }
    })
  } else if (route.meta?.title) {
    // 如果没有找到菜单项但有页面标题，直接添加
    items.push({ title: route.meta.title as string })
  }
  
  return items
})

// 递归查找菜单项
function findMenuItemByPath(menuItems: any[], path: string): any {
  for (const item of menuItems) {
    if (item.path === path) {
      return item
    }
    if (item.children) {
      const found = findMenuItemByPath(item.children, path)
      if (found) return found
    }
  }
  return null
}

// 获取菜单项的完整路径
function getMenuPath(menuItems: any[], targetId: string, currentPath: any[] = []): any[] {
  for (const item of menuItems) {
    const newPath = [...currentPath, item]
    
    if (item.id === targetId) {
      return newPath
    }
    
    if (item.children) {
      const found = getMenuPath(item.children, targetId, newPath)
      if (found.length > 0) return found
    }
  }
  return []
}
</script>

<style scoped>
/* 面包屑样式已在全局CSS中定义 */
</style>
