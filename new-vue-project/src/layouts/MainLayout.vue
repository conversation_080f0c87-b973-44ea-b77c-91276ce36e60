<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <NavBar />
    
    <!-- 主体内容 -->
    <div class="flex">
      <!-- 侧边栏 -->
      <SideBar />
      
      <!-- 主内容区 -->
      <main class="flex-1 transition-all duration-300" :class="{ 'ml-64': !menuStore.collapsed, 'ml-16': menuStore.collapsed }">
        <div class="p-6">
          <!-- 面包屑导航 -->
          <BreadCrumb />
          
          <!-- 页面内容 -->
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import NavBar from '@/components/layout/NavBar.vue'
import SideBar from '@/components/layout/SideBar.vue'
import BreadCrumb from '@/components/layout/BreadCrumb.vue'
import { useMenuStore } from '@/stores/menu'
import { useUserStore } from '@/stores/user'

const menuStore = useMenuStore()
const userStore = useUserStore()

onMounted(() => {
  // 初始化用户信息
  userStore.initUser()
})
</script>

<style scoped>
/* 组件特定样式 */
</style>
