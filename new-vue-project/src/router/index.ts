import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 布局组件
import MainLayout from '../layouts/MainLayout.vue'

// 页面组件 - 懒加载
const HomeView = () => import('../views/HomeView.vue')
const DataSourceView = () => import('../views/data/DataSourceView.vue')
const OfflineTaskView = () => import('../views/data/OfflineTaskView.vue')
const RealtimeTaskView = () => import('../views/data/RealtimeTaskView.vue')
const ReportManagementView = () => import('../views/insight/ReportManagementView.vue')
const BulletinManagementView = () => import('../views/insight/BulletinManagementView.vue')
const BulletinBrowseStatisticsView = () => import('../views/insight/BulletinBrowseStatisticsView.vue')
const BulletinGenerationView = () => import('../views/insight/BulletinGenerationView.vue')
const IntelligentQueryView = () => import('../views/operation/IntelligentQueryView.vue')
const OperationViewsView = () => import('../views/operation/OperationViewsView.vue')
const ThemeManagementView = () => import('../views/operation/ThemeManagementView.vue')
const CustomReportDisplayView = () => import('../views/operation/CustomReportDisplayView.vue')
const ReportHistoryView = () => import('../views/operation/ReportHistoryView.vue')
const DataPermissionControlView = () => import('../views/operation/DataPermissionControlView.vue')
const IndicatorBloodlineView = () => import('../views/operation/IndicatorBloodlineView.vue')
const MapView = () => import('../views/operation/MapView.vue')
const TemplateListView = () => import('../views/operation/TemplateListView.vue')
const TemplatePermissionView = () => import('../views/operation/TemplatePermissionView.vue')
const PermissionManageView = () => import('../views/portal/PermissionManageView.vue')
const PermissionLogManagementView = () => import('../views/portal/PermissionLogManagementView.vue')
const TaskSchedulingView = () => import('../views/penetration/TaskSchedulingView.vue')
const TaskSchedulingExportView = () => import('../views/penetration/TaskSchedulingExportView.vue')
const DataMaskingProcessView = () => import('../views/penetration/DataMaskingProcessView.vue')
const AlarmNotificationView = () => import('../views/penetration/AlarmNotificationView.vue')
const AlarmGenerationView = () => import('../views/penetration/AlarmGenerationView.vue')
const PenetrationPageView = () => import('../views/penetration/PenetrationPageView.vue')
const DevOpsDashboardView = () => import('../views/devops/DevOpsDashboardView.vue')
const PipelineManagementView = () => import('../views/devops/PipelineManagementView.vue')
const DeploymentManagementView = () => import('../views/devops/DeploymentManagementView.vue')
const MonitoringCenterView = () => import('../views/devops/MonitoringCenterView.vue')
const ServiceTopologyView = () => import('../views/devops/ServiceTopologyView.vue')

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'home',
        component: HomeView,
        meta: { title: '首页' }
      },
      // 数据融通
      {
        path: '/data-source',
        name: 'data-source',
        component: DataSourceView,
        meta: { title: '数据源管理' }
      },
      {
        path: '/offline-task',
        name: 'offline-task',
        component: OfflineTaskView,
        meta: { title: '离线采集任务管理' }
      },
      {
        path: '/realtime-task',
        name: 'realtime-task',
        component: RealtimeTaskView,
        meta: { title: '实时采集任务管理' }
      },
      // 智能洞察分析
      {
        path: '/report-management',
        name: 'report-management',
        component: ReportManagementView,
        meta: { title: '运营报告管理' }
      },
      {
        path: '/bulletin-management',
        name: 'bulletin-management',
        component: BulletinManagementView,
        meta: { title: '运营通报管理' }
      },
      {
        path: '/bulletin-browse-statistics',
        name: 'bulletin-browse-statistics',
        component: BulletinBrowseStatisticsView,
        meta: { title: '运营通报浏览查询统计' }
      },
      {
        path: '/bulletin-generation',
        name: 'bulletin-generation',
        component: BulletinGenerationView,
        meta: { title: '运营通报生成与审核' }
      },
      // 运营视图
      {
        path: '/intelligent-query',
        name: 'intelligent-query',
        component: IntelligentQueryView,
        meta: { title: '智能问数' }
      },
      {
        path: '/operation-views',
        name: 'operation-views',
        component: OperationViewsView,
        meta: { title: '大屏模板' }
      },
      {
        path: '/theme-management',
        name: 'theme-management',
        component: ThemeManagementView,
        meta: { title: '画布管理' }
      },
      {
        path: '/custom-report-display',
        name: 'custom-report-display',
        component: CustomReportDisplayView,
        meta: { title: '自定义报表展示' }
      },
      {
        path: '/report-history',
        name: 'report-history',
        component: ReportHistoryView,
        meta: { title: '报表历史记录' }
      },
      {
        path: '/data-permission-control',
        name: 'data-permission-control',
        component: DataPermissionControlView,
        meta: { title: '数据权限控制' }
      },
      {
        path: '/indicator-bloodline',
        name: 'indicator-bloodline',
        component: IndicatorBloodlineView,
        meta: { title: '指标血缘管理' }
      },
      {
        path: '/map-view',
        name: 'map-view',
        component: MapView,
        meta: { title: '地图' }
      },
      {
        path: '/template-list',
        name: 'template-list',
        component: TemplateListView,
        meta: { title: '模板列表' }
      },
      {
        path: '/template-permission',
        name: 'template-permission',
        component: TemplatePermissionView,
        meta: { title: '模板控制' }
      },
      // 统一运营门户
      {
        path: '/permission-manage',
        name: 'permission-manage',
        component: PermissionManageView,
        meta: { title: '视图权限管理' }
      },
      {
        path: '/permission-log-management',
        name: 'permission-log-management',
        component: PermissionLogManagementView,
        meta: { title: '权限日志管理' }
      },
      // 五级穿透调度
      {
        path: '/task-scheduling',
        name: 'task-scheduling',
        component: TaskSchedulingView,
        meta: { title: '任务调度看板' }
      },
      {
        path: '/task-scheduling-export',
        name: 'task-scheduling-export',
        component: TaskSchedulingExportView,
        meta: { title: '统计分析与报表管理' }
      },
      {
        path: '/data-masking-process',
        name: 'data-masking-process',
        component: DataMaskingProcessView,
        meta: { title: '数据脱密处理' }
      },
      {
        path: '/alarm-notification',
        name: 'alarm-notification',
        component: AlarmNotificationView,
        meta: { title: '告警通知' }
      },
      {
        path: '/alarm-generation',
        name: 'alarm-generation',
        component: AlarmGenerationView,
        meta: { title: '告警生成' }
      },
      {
        path: '/penetration-page',
        name: 'penetration-page',
        component: PenetrationPageView,
        meta: { title: '维护穿透页面' }
      },
      // DevOps 平台
      {
        path: '/devops-dashboard',
        name: 'devops-dashboard',
        component: DevOpsDashboardView,
        meta: { title: 'DevOps 总览' }
      },
      {
        path: '/pipeline-management',
        name: 'pipeline-management',
        component: PipelineManagementView,
        meta: { title: 'CI/CD 流水线' }
      },
      {
        path: '/deployment-management',
        name: 'deployment-management',
        component: DeploymentManagementView,
        meta: { title: '容器部署' }
      },
      {
        path: '/monitoring-center',
        name: 'monitoring-center',
        component: MonitoringCenterView,
        meta: { title: '监控中心' }
      },
      {
        path: '/service-topology',
        name: 'service-topology',
        component: ServiceTopologyView,
        meta: { title: '服务拓扑' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

export default router
