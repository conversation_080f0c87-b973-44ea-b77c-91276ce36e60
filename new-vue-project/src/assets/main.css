@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    line-height: 1.5;
    zoom: 0.8; /* 保持原项目的缩放比例 */
  }
}

/* 自定义组件样式 */
@layer components {
  .btn {
    @apply inline-block px-4 py-1.5 text-sm font-medium text-center cursor-pointer border-none rounded transition-all duration-300;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-secondary;
  }

  .btn-success {
    @apply bg-success text-white;
  }

  .btn-warning {
    @apply bg-warning text-white;
  }

  .btn-danger {
    @apply bg-danger text-white;
  }

  .btn-info {
    @apply bg-info text-white;
  }

  .card {
    @apply bg-white rounded-lg shadow-card p-5 mb-5;
  }

  .form-group {
    @apply mb-4;
  }

  .form-group label {
    @apply block mb-2 font-medium;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  .table {
    @apply w-full border-collapse;
  }

  .table th,
  .table td {
    @apply px-4 py-3 text-left border-b border-gray-200;
  }

  .table th {
    @apply bg-gray-50 font-medium text-gray-700;
  }

  .tag {
    @apply inline-block px-2 py-1 text-xs font-medium rounded;
  }

  .tag-success {
    @apply bg-green-100 text-green-800;
  }

  .tag-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .tag-danger {
    @apply bg-red-100 text-red-800;
  }

  .tag-info {
    @apply bg-blue-100 text-blue-800;
  }

  .modal {
    @apply fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50;
  }

  .modal.show {
    @apply flex;
  }

  .modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
  }

  .modal-header {
    @apply flex items-center justify-between p-4 border-b border-gray-200;
  }

  .modal-title {
    @apply text-lg font-semibold;
  }

  .modal-close {
    @apply text-gray-400 hover:text-gray-600 text-2xl cursor-pointer;
  }

  .modal-body {
    @apply p-4;
  }

  .modal-footer {
    @apply flex justify-end gap-2 p-4 border-t border-gray-200;
  }

  .dropdown {
    @apply relative inline-block;
  }

  .dropdown-toggle {
    @apply bg-transparent border-none cursor-pointer p-2 rounded hover:bg-gray-100;
  }

  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10 hidden;
  }

  .dropdown.show .dropdown-menu {
    @apply block;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer;
  }

  .pagination {
    @apply flex items-center justify-center gap-1 mt-4;
  }

  .pagination-item {
    @apply px-3 py-2 text-sm border border-gray-300 cursor-pointer hover:bg-gray-50;
  }

  .pagination-item.active {
    @apply bg-primary text-white border-primary;
  }

  .breadcrumb {
    @apply flex items-center gap-2 text-sm text-gray-600 mb-4;
  }

  .breadcrumb-item {
    @apply flex items-center;
  }

  .breadcrumb-item:not(:last-child)::after {
    content: '/';
    @apply ml-2 text-gray-400;
  }

  .breadcrumb-item.active {
    @apply text-gray-900 font-medium;
  }
}
