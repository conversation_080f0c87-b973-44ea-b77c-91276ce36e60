# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

将旧工程转换为 vue3 + vite + ts + TailwindCSS 的新工程

### 要求

1、通读@old-project-demoV1/ 下的代码，分析代码结构和项目功能。辨识出项目的所有功能和模块。在根目录下创建一个新的 Vue 3 + Vite + TypeScript + TailwindCSS 项目。
2、将旧工程的代码迁移到新工程中，保持原有功能不变。保持原有的菜单结构和路由，保持原有标题内容。将样式文件迁移到新工程中，并使用 TailwindCSS 进行样式重构。
3、将旧工程中模型数据结构，迁移到新工程中，并进行必要的调整和优化。提取页面增删改查逻辑，封装成接口调用形式，使用 Pinia 进行状态管理。
5、使用 mock 数据进行测试，确保新工程的功能与旧工程一致。将可以进行后端调用的地方，封装成后端 api 调用。使用统一的前缀 https://api.example.com/，并将接口地址配置在环境变量中。

### 迁移步骤

1. **分析旧工程代码**：通读旧工程代码，理解其结构和功能。
2. **创建新工程**：使用 Vue CLI 创建一个新的 Vue 3 + Vite + TypeScript + TailwindCSS 项目。
3. **迁移代码**：将旧工程的代码迁移到新工程中，保持原有功能和结构。
4. **重构样式**：将旧工程的样式文件迁移到新工程中，并使用 TailwindCSS 进行样式重构。
5. **迁移数据模型**：将旧工程中的数据模型迁移到新工程中，并进行必要的调整和优化。提取页面增删改查逻辑，封装成接口调用形式，使用 Pinia 进行状态管理。
6. **导出数据物理模型**：根据旧工程的数据模型和功能，导出数据物理模型。确保新工程的数据结构与旧工程一致。将物理模型导出成 mysql 脚本。
7. **使用 Mock 数据测试**：使用 Mock 数据进行测试，确保新工程的功能与旧工程一致。
8. **封装后端 API 调用**：将可以进行后端调用的地方，封装成后端 api 调用。使用统一的前缀https://api.example.com/，并将接口地址配置在环境变量中。
9. **测试和验证**：对新工程进行全面测试，确保所有功能正常运行，并与旧工程保持一致。
10. **文档更新**：更新项目文档，记录迁移过程和新工程的使用说明。

请按照上述步骤进行迁移，确保每一步都仔细检查和验证。

## Old Project Analysis

### Project Structure
The old project is a traditional multi-page application built with vanilla HTML, CSS, and JavaScript. The structure includes:

1. **Main Entry Point**: `index.html` - The homepage/dashboard
2. **Module Pages**: Numerous HTML files representing different functional modules:
   - Data integration (`data_source.html`, `offline_task.html`, `realtime_task.html`)
   - Intelligent analytics (`report_management.html`, `bulletin_management.html`, etc.)
   - Operation views (`intelligent_query.html`, `operation_views.html`, etc.)
   - Unified portal (`permission_manage.html`, `permission_and_log_management.html`)
   - Five-level scheduling (`task_scheduling_diaodu.html`, `task_scheduling_export.html`, etc.)
   - DevOps platform (`devops_dashboard.html`, `pipeline_management.html`, etc.)
3. **Assets**:
   - CSS files in `/css` directory (`style.css`, `custom-tabs.css`, `style-1.css`)
   - JavaScript files in `/js` directory (`common.js`, `canvas_modal.js`)
   - Images in `/images` directory
   - Icons in `/icons` directory
4. **Data**: Empty `/data` directory (possibly intended for JSON or other data files)

### Key Features
1. **UI Components**:
   - Navigation bar with user profile and notifications
   - Collapsible sidebar menu with nested structure
   - Responsive grid system
   - Cards for data visualization
   - Tables with pagination
   - Modals for forms and dialogs
   - Tabs for content switching
   - Dropdown menus
   - Form elements with validation
   - Charts using Chart.js

2. **Functionality**:
   - Multi-level menu navigation
   - Page routing through direct HTML file links
   - Form handling and validation
   - API integration through `apiRequest` function
   - Component initialization on page load
   - State management through DOM manipulation

3. **Architecture**:
   - Traditional server-side routing (multi-page application)
   - Shared CSS for consistent styling
   - Common JavaScript utilities in `common.js`
   - Module-specific JavaScript where needed
   - CSS variables for theme management
   - Responsive design with media queries

### Data Models
Based on the page structure, the main data models likely include:
1. **User Management**: User profiles, permissions, login
2. **Data Sources**: Database connections, file sources, API endpoints
3. **Tasks**: Offline and real-time data collection tasks
4. **Reports**: Generated reports, templates, history
5. **Notifications**: System alerts, task status updates
6. **DevOps**: CI/CD pipelines, deployments, monitoring
7. **Permissions**: Role-based access control, audit logs

### Technical Details
1. **Styling**: CSS with custom properties (variables) for consistent theming
2. **JavaScript**: Vanilla JS with modular functions for component initialization
3. **API Integration**: Custom `apiRequest` function for REST API calls
4. **UI Framework**: Custom-built components without external frameworks
5. **Icons**: Font Awesome for iconography
6. **Charts**: Chart.js for data visualization

## Common Commands (Template)
Since there's no project yet, here are templates for common command types:

### For Vue.js projects:
- Build: `npm run build`
- Development server: `npm run serve` or `npm run dev`
- Linting: `npm run lint`
- Testing: `npm run test` or `npm run test:unit`

### For React projects:
- Build: `npm run build`
- Development server: `npm start` or `npm run dev`
- Linting: `npm run lint`
- Testing: `npm test` or `npm run test`

### For Node.js projects:
- Run: `node index.js` or `npm start`
- Development: `npm run dev`
- Testing: `npm test`

## Architecture Notes
Once a project is established, document:
- Key components/modules
- Data flow patterns
- State management approach
- Folder structure conventions
- Component communication patterns

## Development Workflow
Instructions for:
- Setting up the development environment
- Running the application locally
- Creating and running tests
- Contributing changes