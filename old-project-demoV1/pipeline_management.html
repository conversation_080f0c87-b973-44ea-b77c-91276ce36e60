<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CI/CD 流水线 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child  active" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration"  data-href="five_level_penetration.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-code-branch page-title-icon"></i>
      CI/CD 流水线
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">DevOps 平台</a></div>
      <div class="breadcrumb-item active">CI/CD 流水线</div>
    </div>

    <!-- 页面标题和操作 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <div>
        <h1 style="font-size: 24px; font-weight: bold; margin: 0; color: var(--text-primary);">CI/CD 流水线</h1>
        <p style="color: var(--text-secondary); margin: 4px 0 0 0;">持续集成与持续部署管理</p>
      </div>
      <button class="btn btn-primary" onclick="createNewPipeline()">
        <i class="fas fa-play"></i> 新建流水线
      </button>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card" style="padding: 16px; margin-bottom: 24px;">
      <div style="display: flex; flex-wrap: wrap; gap: 16px;">
        <!-- 搜索框 -->
        <div style="flex: 1; min-width: 300px;">
          <div style="position: relative;">
            <i class="fas fa-search" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: var(--text-tertiary);"></i>
            <input
              type="text"
              placeholder="搜索流水线..."
              id="searchInput"
              style="width: 100%; padding: 8px 12px 8px 36px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;"
              oninput="filterPipelines()"
            />
          </div>
        </div>

        <!-- 状态过滤 -->
        <div style="display: flex; align-items: center; gap: 8px;">
          <i class="fas fa-filter" style="color: var(--text-tertiary);"></i>
          <select
            id="statusFilter"
            style="border: 1px solid var(--border-color); border-radius: 4px; padding: 8px 12px; font-size: 14px;"
            onchange="filterPipelines()"
          >
            <option value="all">全部状态</option>
            <option value="running">运行中</option>
            <option value="success">成功</option>
            <option value="failed">失败</option>
            <option value="pending">等待中</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 流水线列表和详情 -->
    <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 24px;">
      <!-- 流水线列表 -->
      <div style="space-y: 16px;">
        <div id="pipelinesList">
          <!-- 流水线列表将通过JavaScript动态生成 -->
        </div>
      </div>

      <!-- 流水线详情 -->
      <div>
        <div id="pipelineDetail" class="card" style="padding: 32px; text-align: center;">
          <i class="fas fa-code-branch" style="font-size: 48px; color: var(--text-tertiary); margin-bottom: 16px;"></i>
          <h3 style="font-size: 18px; font-weight: 500; color: var(--text-primary); margin-bottom: 8px;">
            选择一个流水线
          </h3>
          <p style="color: var(--text-secondary);">
            从左侧列表中选择一个流水线来查看详细的执行过程
          </p>
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 模拟流水线数据
    const mockPipelines = [
      {
        id: '1',
        name: 'user-service-pipeline',
        description: '用户服务持续集成流水线',
        status: 'success',
        branch: 'main',
        author: '张三',
        duration: '6m 0s',
        createdAt: '2024-01-15T10:00:00Z',
        commitMessage: 'feat: add user profile update API',
        triggeredBy: '代码推送',
        stages: [
          { name: '代码检出', status: 'success', duration: '30s' },
          { name: '构建', status: 'success', duration: '1m 30s' },
          { name: '测试', status: 'success', duration: '2m 0s' },
          { name: '部署', status: 'success', duration: '2m 0s' }
        ]
      },
      {
        id: '2',
        name: 'order-service-pipeline',
        description: '订单服务持续集成流水线',
        status: 'running',
        branch: 'develop',
        author: '李四',
        duration: '1m 45s',
        createdAt: '2024-01-15T10:10:00Z',
        commitMessage: 'fix: order calculation bug',
        triggeredBy: '手动触发',
        stages: [
          { name: '代码检出', status: 'success', duration: '25s' },
          { name: '构建', status: 'running', duration: '1m 20s' },
          { name: '测试', status: 'pending', duration: '-' },
          { name: '部署', status: 'pending', duration: '-' }
        ]
      },
      {
        id: '3',
        name: 'payment-service-pipeline',
        description: '支付服务持续集成流水线',
        status: 'failed',
        branch: 'main',
        author: '王五',
        duration: '3m 12s',
        createdAt: '2024-01-15T09:45:00Z',
        commitMessage: 'refactor: payment gateway integration',
        triggeredBy: '代码推送',
        stages: [
          { name: '代码检出', status: 'success', duration: '28s' },
          { name: '构建', status: 'success', duration: '1m 15s' },
          { name: '测试', status: 'failed', duration: '1m 29s' },
          { name: '部署', status: 'cancelled', duration: '-' }
        ]
      },
      {
        id: '4',
        name: 'notification-service-pipeline',
        description: '通知服务持续集成流水线',
        status: 'success',
        branch: 'main',
        author: '赵六',
        duration: '1m 58s',
        createdAt: '2024-01-15T09:30:00Z',
        commitMessage: 'feat: add email notification template',
        triggeredBy: '定时触发',
        stages: [
          { name: '代码检出', status: 'success', duration: '22s' },
          { name: '构建', status: 'success', duration: '45s' },
          { name: '测试', status: 'success', duration: '31s' },
          { name: '部署', status: 'success', duration: '20s' }
        ]
      }
    ];

    let selectedPipelineId = null;
    let filteredPipelines = [...mockPipelines];

    // 获取状态图标和颜色
    function getStatusIcon(status) {
      switch (status) {
        case 'success': return '<i class="fas fa-check-circle" style="color: var(--success-color);"></i>';
        case 'failed': return '<i class="fas fa-times-circle" style="color: var(--danger-color);"></i>';
        case 'running': return '<div style="width: 16px; height: 16px; border: 2px solid var(--primary-color); border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>';
        case 'pending': return '<i class="fas fa-clock" style="color: var(--warning-color);"></i>';
        case 'cancelled': return '<i class="fas fa-ban" style="color: var(--text-tertiary);"></i>';
        default: return '<i class="fas fa-clock" style="color: var(--warning-color);"></i>';
      }
    }

    function getStatusText(status) {
      switch (status) {
        case 'success': return '成功';
        case 'failed': return '失败';
        case 'running': return '运行中';
        case 'pending': return '等待中';
        case 'cancelled': return '已取消';
        default: return '等待中';
      }
    }

    function getStatusColor(status) {
      switch (status) {
        case 'success': return 'var(--success-color)';
        case 'failed': return 'var(--danger-color)';
        case 'running': return 'var(--primary-color)';
        case 'pending': return 'var(--warning-color)';
        case 'cancelled': return 'var(--text-tertiary)';
        default: return 'var(--warning-color)';
      }
    }

    // 渲染流水线列表
    function renderPipelinesList() {
      const container = document.getElementById('pipelinesList');
      container.innerHTML = filteredPipelines.map(pipeline => `
        <div class="card pipeline-item ${selectedPipelineId === pipeline.id ? 'selected' : ''}"
             style="padding: 16px; cursor: pointer; transition: all 0.2s; margin-bottom: 16px;"
             onclick="selectPipeline('${pipeline.id}')">
          <div style="display: flex; align-items: flex-start; justify-content: space-between;">
            <div style="flex: 1;">
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                ${getStatusIcon(pipeline.status)}
                <h3 style="font-size: 16px; font-weight: 500; color: var(--text-primary); margin: 0; flex: 1;">
                  ${pipeline.name}
                </h3>
              </div>
              <p style="color: var(--text-secondary); margin: 0 0 12px 0; font-size: 14px; line-height: 1.4;">
                ${pipeline.description}
              </p>
              <div style="display: flex; flex-direction: column; gap: 4px; font-size: 12px; color: var(--text-tertiary);">
                <div style="display: flex; align-items: center; gap: 4px;">
                  <i class="fas fa-code-branch" style="width: 12px;"></i>
                  <span>${pipeline.branch}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 4px;">
                  <i class="fas fa-user" style="width: 12px;"></i>
                  <span>${pipeline.author}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 4px;">
                  <i class="fas fa-calendar" style="width: 12px;"></i>
                  <span>${new Date(pipeline.createdAt).toLocaleDateString('zh-CN')}</span>
                </div>
              </div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 8px;">
              <span class="tag" style="background-color: ${getStatusColor(pipeline.status)}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                ${getStatusText(pipeline.status)}
              </span>
              <div style="display: flex; gap: 4px;">
                ${pipeline.status === 'running' ?
                  '<button class="btn" style="padding: 4px 8px; color: var(--danger-color); border: 1px solid var(--danger-color);" onclick="event.stopPropagation(); stopPipeline(\'' + pipeline.id + '\')" title="停止"><i class="fas fa-stop"></i></button>' :
                  '<button class="btn" style="padding: 4px 8px; color: var(--success-color); border: 1px solid var(--success-color);" onclick="event.stopPropagation(); runPipeline(\'' + pipeline.id + '\')" title="运行"><i class="fas fa-play"></i></button>'
                }
                <button class="btn" style="padding: 4px 8px; color: var(--primary-color); border: 1px solid var(--primary-color);" onclick="event.stopPropagation(); runPipeline('${pipeline.id}')" title="重新运行">
                  <i class="fas fa-redo"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      `).join('');
    }

    // 渲染流水线详情
    function renderPipelineDetail(pipeline) {
      const container = document.getElementById('pipelineDetail');
      container.innerHTML = `
        <div style="text-align: left;">
          <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 24px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
            <div>
              <h2 style="font-size: 20px; font-weight: 600; color: var(--text-primary); margin: 0 0 4px 0;">
                ${pipeline.name}
              </h2>
              <p style="color: var(--text-secondary); margin: 0; font-size: 14px;">
                ${pipeline.description}
              </p>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              ${getStatusIcon(pipeline.status)}
              <span style="font-weight: 500; color: ${getStatusColor(pipeline.status)};">
                ${getStatusText(pipeline.status)}
              </span>
            </div>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
            <div>
              <h4 style="font-size: 14px; font-weight: 600; color: var(--text-primary); margin: 0 0 8px 0;">基本信息</h4>
              <div style="space-y: 8px;">
                <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                  <span style="color: var(--text-secondary);">分支:</span>
                  <span style="color: var(--text-primary); font-weight: 500;">${pipeline.branch}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                  <span style="color: var(--text-secondary);">作者:</span>
                  <span style="color: var(--text-primary); font-weight: 500;">${pipeline.author}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                  <span style="color: var(--text-secondary);">持续时间:</span>
                  <span style="color: var(--text-primary); font-weight: 500;">${pipeline.duration}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 4px 0;">
                  <span style="color: var(--text-secondary);">触发方式:</span>
                  <span style="color: var(--text-primary); font-weight: 500;">${pipeline.triggeredBy}</span>
                </div>
              </div>
            </div>
            <div>
              <h4 style="font-size: 14px; font-weight: 600; color: var(--text-primary); margin: 0 0 8px 0;">提交信息</h4>
              <div style="background-color: var(--bg-color); padding: 12px; border-radius: 4px; border-left: 3px solid var(--primary-color);">
                <p style="margin: 0; font-size: 14px; color: var(--text-primary); font-family: monospace;">
                  ${pipeline.commitMessage}
                </p>
                <p style="margin: 8px 0 0 0; font-size: 12px; color: var(--text-tertiary);">
                  ${new Date(pipeline.createdAt).toLocaleString('zh-CN')}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h4 style="font-size: 14px; font-weight: 600; color: var(--text-primary); margin: 0 0 16px 0;">执行阶段</h4>
            <div style="display: flex; flex-direction: column; gap: 12px;">
              ${pipeline.stages.map((stage, index) => `
                <div style="display: flex; align-items: center; padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; background-color: ${stage.status === 'running' ? 'rgba(24, 144, 255, 0.05)' : 'white'};">
                  <div style="display: flex; align-items: center; gap: 12px; flex: 1;">
                    <div style="width: 24px; height: 24px; border-radius: 50%; background-color: ${getStatusColor(stage.status)}; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold;">
                      ${index + 1}
                    </div>
                    <div>
                      <div style="font-weight: 500; color: var(--text-primary); margin-bottom: 2px;">
                        ${stage.name}
                      </div>
                      <div style="font-size: 12px; color: var(--text-secondary);">
                        ${getStatusText(stage.status)} ${stage.duration !== '-' ? '• ' + stage.duration : ''}
                      </div>
                    </div>
                  </div>
                  <div>
                    ${getStatusIcon(stage.status)}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      `;
    }

    // 选择流水线
    function selectPipeline(pipelineId) {
      selectedPipelineId = pipelineId;
      const pipeline = mockPipelines.find(p => p.id === pipelineId);
      if (pipeline) {
        renderPipelineDetail(pipeline);
      }
      renderPipelinesList(); // 重新渲染以更新选中状态
    }

    // 过滤流水线
    function filterPipelines() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value;

      filteredPipelines = mockPipelines.filter(pipeline => {
        const matchesSearch = pipeline.name.toLowerCase().includes(searchTerm) ||
                             pipeline.description.toLowerCase().includes(searchTerm) ||
                             pipeline.author.toLowerCase().includes(searchTerm);
        const matchesStatus = statusFilter === 'all' || pipeline.status === statusFilter;

        return matchesSearch && matchesStatus;
      });

      renderPipelinesList();
    }

    // 流水线操作
    function runPipeline(pipelineId) {
      alert('启动流水线: ' + pipelineId);
    }

    function stopPipeline(pipelineId) {
      alert('停止流水线: ' + pipelineId);
    }

    function createNewPipeline() {
      alert('创建新流水线功能');
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      renderPipelinesList();
    });
  </script>

  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .pipeline-item:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .pipeline-item.selected {
      border: 2px solid var(--primary-color);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
    }
  </style>
</body>
</html>
