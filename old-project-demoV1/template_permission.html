<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 模板权限控制</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#165DFF',
              secondary: '#4080FF',
              success: '#00B42A',
              warning: '#FF7D00',
              danger: '#F53F3F',
              info: '#86909C',
              light: '#F2F3F5',
              dark: '#1D2129',
            },
            fontFamily: {
              inter: ['Inter', 'sans-serif'],
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .menu-active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          border-left: 4px solid var(--primary-color);
        }
        .btn-primary {
          background-color: var(--primary-color);
          color: white;
          transition: all 200ms;
        }
        .btn-primary:hover {
          background-color: rgba(24, 144, 255, 0.9);
        }
        .btn-secondary {
          background-color: white;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
          transition: all 200ms;
        }
        .btn-secondary:hover {
          background-color: rgba(24, 144, 255, 0.05);
        }
        .btn-danger {
          background-color: var(--danger-color);
          color: white;
          transition: all 200ms;
        }
        .btn-danger:hover {
          background-color: rgba(255, 77, 79, 0.9);
        }
        .panel {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid #f3f4f6;
        }
        .panel-header {
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .panel-body {
          padding: 1rem;
        }
        .permission-item {
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #f3f4f6;
          transition: background-color 200ms;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .permission-item:hover {
          background-color: #f9fafb;
        }
        .permission-action {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }
        .role-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }
        .permission-table th {
          padding: 0.75rem 1rem;
          text-align: left;
          font-size: 0.75rem;
          font-weight: 500;
          color: #6b7280;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        .permission-table td {
          padding: 1rem;
          white-space: nowrap;
          font-size: 0.875rem;
          color: #374151;
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-inter text-dark">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      
      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>


      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" >
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child active" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

       <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="data_permission_control.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">管理⻚⾯穿透权限</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 面包屑导航 -->
      <div class="flex items-center text-sm text-gray-500 mb-6">
        <a href="index.html" class="hover:text-primary transition-colors duration-200">首页</a>
        <i class="fas fa-chevron-right mx-2 text-xs"></i>
        <a href="operation_views.html" class="hover:text-primary transition-colors duration-200">运营视图</a>
        <i class="fas fa-chevron-right mx-2 text-xs"></i>
        <a href="#" class="hover:text-primary transition-colors duration-200">模板管理</a>
        <i class="fas fa-chevron-right mx-2 text-xs"></i>
        <span class="text-primary">模板权限控制</span>
      </div>

      <!-- 页面标题 -->
      <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold text-gray-800 mb-6">模板权限控制</h1>

      <!-- 筛选和搜索区 -->
      <div class="panel mb-6">
        <div class="panel-header">
          <h2 class="text-sm font-medium text-gray-700">筛选条件</h2>
          <button id="resetAllButton" class="text-xs text-primary hover:text-primary/80 transition-colors duration-200">重置全部</button>
        </div>
        <div class="panel-body grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
            <input type="text" placeholder="输入模板名称" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">模板ID</label>
            <input type="text" placeholder="输入模板ID" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">模板类型</label>
            <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
              <option value="">全部模板类型</option>
              <option value="1">全国模板</option>
              <option value="2">分省模板</option>
              <option value="3">市级模板</option>
              <option value="4">区县级模板</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">权限类型</label>
            <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
              <option value="">全部权限类型</option>
              <option value="1">公开</option>
              <option value="2">部门</option>
              <option value="3">个人</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
            <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
              <option value="">全部部门</option>
              <option value="1">销售部</option>
              <option value="2">市场部</option>
              <option value="3">技术部</option>
              <option value="4">财务部</option>
              <option value="5">人力资源部</option>
            </select>
          </div>
          <div class="flex items-end">
            <button id="searchButton" class="w-full px-4 py-2 rounded-lg btn-primary text-sm whitespace-nowrap">
              <i class="fas fa-search mr-2"></i>
              查询
            </button>
          </div>
        </div>
      </div>

      <!-- 视图切换 -->
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex items-center gap-3">
          <span class="text-sm text-gray-500">视图模式:</span>
          <div class="flex rounded-lg overflow-hidden border border-gray-200">
            <button id="tableViewButton" class="px-3 py-1.5 bg-primary text-white text-sm">
              <i class="fas fa-th-large mr-1"></i>
              表格
            </button>
            <button id="cardViewButton" class="px-3 py-1.5 bg-white text-gray-600 hover:bg-gray-50 text-sm">
              <i class="fas fa-th mr-1"></i>
              卡片
            </button>
            <button id="compactViewButton" class="px-3 py-1.5 bg-white text-gray-600 hover:bg-gray-50 text-sm">
              <i class="fas fa-compress-alt mr-1"></i>
              紧凑
            </button>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <button id="batchSetPermissionButton" class="px-3 py-1.5 rounded-lg btn-secondary text-sm flex items-center">
            <i class="fas fa-plus mr-1.5"></i>
            批量设置权限
          </button>
          <button id="exportPermissionReportButton" class="px-3 py-1.5 rounded-lg btn-primary text-sm flex items-center">
            <i class="fas fa-file-export mr-1.5"></i>
            导出权限报告
          </button>
        </div>
      </div>

      <!-- 权限列表 -->
      <div class="panel mb-6">
        <div class="panel-header">
          <h2 class="text-sm font-medium text-gray-700">模板权限列表</h2>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 permission-table">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col">模板名称</th>
                <th scope="col">模板ID</th>
                <th scope="col">模板类型</th>
                <th scope="col">创建人</th>
                <th scope="col">创建时间</th>
                <th scope="col">权限类型</th>
                <th scope="col">授权部门/人员</th>
                <th scope="col">权限状态</th>
                <th scope="col">最近修改时间</th>
                <th scope="col">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                      <i class="fas fa-chart-bar text-gray-400"></i>
                    </div>
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">区域销售分析模板</div>
                      <div class="text-xs text-gray-500">销售分析 > 区域分析</div>
                    </div>
                  </div>
                </td>
                <td>TPL-20230501-001</td>
                <td>分省模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1001/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">张三</span>
                  </div>
                </td>
                <td>2023-05-01</td>
                <td>
                  <span class="role-badge bg-primary/10 text-primary">部门</span>
                </td>
                <td>销售部</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">有效</span>
                </td>
                <td>2023-05-12</td>
                <td>
                  <div class="permission-action">
                    <button id="editPermissionBtn1" class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm" onclick="openPermissionDetailModal('TPL-20230501-001', '区域销售分析模板')">
                      <i class="fas fa-edit mr-1"></i>
                      编辑
                    </button>
                    <button id="logPermissionBtn1" class="text-danger hover:text-danger/80 transition-colors duration-200 text-sm">
                      <i class="fas fa-history mr-1"></i>
                      日志
                    </button>
                  </div>
                </td>
              </tr>
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                      <i class="fas fa-chart-line text-gray-400"></i>
                    </div>
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">销售趋势分析模板</div>
                      <div class="text-xs text-gray-500">销售分析 > 趋势分析</div>
                    </div>
                  </div>
                </td>
                <td>TPL-20230420-002</td>
                <td>全国模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1002/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">李四</span>
                  </div>
                </td>
                <td>2023-04-20</td>
                <td>
                  <span class="role-badge bg-success/10 text-success">公开</span>
                </td>
                <td>全部</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">有效</span>
                </td>
                <td>2023-05-10</td>
                <td>
                  <div class="permission-action">
                    <button id="editPermissionBtn2" class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm" onclick="openPermissionDetailModal('TPL-20230420-002', '销售趋势分析模板')">
                      <i class="fas fa-edit mr-1"></i>
                      编辑
                    </button>
                    <button id="logPermissionBtn2" class="text-danger hover:text-danger/80 transition-colors duration-200 text-sm">
                      <i class="fas fa-history mr-1"></i>
                      日志
                    </button>
                  </div>
                </td>
              </tr>
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                      <i class="fas fa-chart-pie text-gray-400"></i>
                    </div>
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">客户满意度分析模板</div>
                      <div class="text-xs text-gray-500">客户分析 > 满意度分析</div>
                    </div>
                  </div>
                </td>
                <td>TPL-20230415-003</td>
                <td>全国模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1003/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">王五</span>
                  </div>
                </td>
                <td>2023-04-15</td>
                <td>
                  <span class="role-badge bg-warning/10 text-warning">个人</span>
                </td>
                <td>王五</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">有效</span>
                </td>
                <td>2023-05-05</td>
                <td>
                  <div class="permission-action">
                    <button id="editPermissionBtn3" class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm" onclick="openPermissionDetailModal('TPL-20230415-003', '客户满意度分析模板')">
                      <i class="fas fa-edit mr-1"></i>
                      编辑
                    </button>
                    <button id="logPermissionBtn3" class="text-danger hover:text-danger/80 transition-colors duration-200 text-sm">
                      <i class="fas fa-history mr-1"></i>
                      日志
                    </button>
                  </div>
                </td>
              </tr>
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                      <i class="fas fa-chart-bar text-gray-400"></i>
                    </div>
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">产品销量分析模板</div>
                      <div class="text-xs text-gray-500">产品分析 > 销量分析</div>
                    </div>
                  </div>
                </td>
                <td>TPL-20230410-004</td>
                <td>市级模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1004/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">赵六</span>
                  </div>
                </td>
                <td>2023-04-10</td>
                <td>
                  <span class="role-badge bg-primary/10 text-primary">部门</span>
                </td>
                <td>市场部</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-warning/10 text-warning">待审核</span>
                </td>
                <td>2023-05-01</td>
                <td>
                  <div class="permission-action">
                    <button id="editPermissionBtn4" class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm" onclick="openPermissionDetailModal('TPL-20230410-004', '产品销量分析模板')">
                      <i class="fas fa-edit mr-1"></i>
                      编辑
                    </button>
                    <button id="logPermissionBtn4" class="text-danger hover:text-danger/80 transition-colors duration-200 text-sm">
                      <i class="fas fa-history mr-1"></i>
                      日志
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                显示
                <span class="font-medium">1</span>
                到
                <span class="font-medium">4</span>
                条，共
                <span class="font-medium">24</span>
                条记录
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">上一页</span>
                  <i class="fas fa-chevron-left text-xs"></i>
                </a>
                <a href="#" aria-current="page" class="z-10 bg-primary/10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">3</a>
                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">4</a>
                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">5</a>
                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">6</a>
                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">下一页</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </a>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限申请记录 -->
      <div class="panel mb-6">
        <div class="panel-header">
          <h2 class="text-sm font-medium text-gray-700">权限申请记录</h2>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 permission-table">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col">申请ID</th>
                <th scope="col">模板名称</th>
                <th scope="col">申请人</th>
                <th scope="col">申请时间</th>
                <th scope="col">申请类型</th>
                <th scope="col">申请权限</th>
                <th scope="col">申请状态</th>
                <th scope="col">审批人</th>
                <th scope="col">审批时间</th>
                <th scope="col">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>REQ-20230515-001</td>
                <td>产品销量分析模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1006/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">孙七</span>
                  </div>
                </td>
                <td>2023-05-15 10:23</td>
                <td>权限变更</td>
                <td>查看权限</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">已通过</span>
                </td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1007/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">周八</span>
                  </div>
                </td>
                <td>2023-05-15 11:45</td>
                <td>
                  <button class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm">
                    <i class="fas fa-eye mr-1"></i>
                    查看
                  </button>
                </td>
              </tr>
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>REQ-20230514-002</td>
                <td>客户满意度分析模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1008/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">吴九</span>
                  </div>
                </td>
                <td>2023-05-14 15:30</td>
                <td>权限申请</td>
                <td>编辑权限</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-warning/10 text-warning">待审批</span>
                </td>
                <td>-</td>
                <td>-</td>
                <td>
                  <button class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm">
                    <i class="fas fa-eye mr-1"></i>
                    查看
                  </button>
                </td>
              </tr>
              <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td>REQ-20230513-003</td>
                <td>区域销售分析模板</td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1009/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">郑十</span>
                  </div>
                </td>
                <td>2023-05-13 09:15</td>
                <td>权限申请</td>
                <td>复制权限</td>
                <td>
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-danger/10 text-danger">已拒绝</span>
                </td>
                <td>
                  <div class="flex items-center">
                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1010/30/30" alt="用户头像" />
                    <span class="text-sm text-gray-700">王十一</span>
                  </div>
                </td>
                <td>2023-05-13 14:20</td>
                <td>
                  <button class="text-primary hover:text-primary/80 transition-colors duration-200 text-sm">
                    <i class="fas fa-eye mr-1"></i>
                    查看
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 权限统计 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div class="panel">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">权限分布统计</h2>
          </div>
          <div class="panel-body">
            <div class="h-64 bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <canvas id="permissionDistributionChart"></canvas>
            </div>
          </div>
        </div>
        <div class="panel">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">权限申请趋势</h2>
          </div>
          <div class="panel-body">
            <div class="h-64 bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <canvas id="permissionTrendChart"></canvas>
            </div>
          </div>
        </div>
        <div class="panel">
          <div class="panel-header">
            <h2 class="text-sm font-medium text-gray-700">权限状态统计</h2>
          </div>
          <div class="panel-body">
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-success/5 rounded-lg p-4 border border-success/10">
                <div class="text-3xl font-semibold text-success mb-1">126</div>
                <div class="text-sm text-gray-600">有效权限</div>
                <div class="text-xs text-gray-500 mt-1">
                  <i class="fas fa-arrow-up text-success mr-1"></i>
                  12.5% 较上月
                </div>
              </div>
              <div class="bg-warning/5 rounded-lg p-4 border border-warning/10">
                <div class="text-3xl font-semibold text-warning mb-1">18</div>
                <div class="text-sm text-gray-600">待审批</div>
                <div class="text-xs text-gray-500 mt-1">
                  <i class="fas fa-arrow-up text-danger mr-1"></i>
                  5.3% 较上月
                </div>
              </div>
              <div class="bg-primary/5 rounded-lg p-4 border border-primary/10">
                <div class="text-3xl font-semibold text-primary mb-1">74</div>
                <div class="text-sm text-gray-600">部门权限</div>
                <div class="text-xs text-gray-500 mt-1">
                  <i class="fas fa-arrow-up text-success mr-1"></i>
                  8.2% 较上月
                </div>
              </div>
              <div class="bg-danger/5 rounded-lg p-4 border border-danger/10">
                <div class="text-3xl font-semibold text-danger mb-1">12</div>
                <div class="text-sm text-gray-600">已拒绝</div>
                <div class="text-xs text-gray-500 mt-1">
                  <i class="fas fa-arrow-down text-success mr-1"></i>
                  3.1% 较上月
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限详情模态框 -->
    <div id="permissionDetailModal" class="modal-wrapper fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl overflow-hidden flex flex-col max-h-[90vh]">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900"><span id="modalTemplateName">模板权限设置</span></h3>
          <button id="closePermissionDetailModal" class="text-gray-400 hover:text-gray-500 transition-colors duration-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-6 overflow-y-auto flex-grow">
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">基本信息</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-xs text-gray-500 mb-1">模板ID</label>
                <p class="text-sm text-gray-900" id="modalTemplateId">TPL-20230501-001</p>
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">模板名称</label>
                <p class="text-sm text-gray-900" id="modalTemplateNameDisplay">区域销售分析模板</p>
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">创建人</label>
                <p class="text-sm text-gray-900">张三</p>
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">创建时间</label>
                <p class="text-sm text-gray-900">2023-05-01</p>
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">最近修改时间</label>
                <p class="text-sm text-gray-900">2023-05-12</p>
              </div>
              <div>
                <label class="block text-xs text-gray-500 mb-1">当前权限状态</label>
                <p class="text-sm text-success">
                  <i class="fas fa-check-circle mr-1"></i>
                  有效
                </p>
              </div>
            </div>
          </div>

          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">权限设置</h4>
            <div class="space-y-4">
              <div class="border border-gray-200 rounded-lg p-4">
                <h5 class="text-sm font-medium text-gray-700 mb-3">权限类型</h5>
                <div class="flex space-x-4">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="permissionType" value="public" class="sr-only peer" checked />
                    <div class="w-4 h-4 border border-gray-300 rounded-full peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center peer-checked:bg-white">
                      <div class="w-2 h-2 rounded-full bg-primary hidden peer-checked:block"></div>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">公开</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="permissionType" value="department" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded-full peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center peer-checked:bg-white">
                      <div class="w-2 h-2 rounded-full bg-primary hidden peer-checked:block"></div>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">部门</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="permissionType" value="personal" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded-full peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center peer-checked:bg-white">
                      <div class="w-2 h-2 rounded-full bg-primary hidden peer-checked:block"></div>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">个人</span>
                  </label>
                </div>
              </div>

              <div class="border border-gray-200 rounded-lg p-4" id="departmentPermissionSection">
                <h5 class="text-sm font-medium text-gray-700 mb-3">授权部门</h5>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">销售部</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">市场部</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">技术部</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">财务部</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">人力资源部</span>
                  </label>
                </div>
                <div class="flex items-center justify-between mt-4">
                  <button class="text-sm text-primary hover:text-primary/80 transition-colors duration-200 flex items-center">
                    <i class="fas fa-plus-circle mr-1"></i>
                    添加更多部门
                  </button>
                </div>
              </div>

              <div class="border border-gray-200 rounded-lg p-4" id="personalPermissionSection" style="display: none">
                <h5 class="text-sm font-medium text-gray-700 mb-3">授权人员</h5>
                <div class="relative mb-3">
                  <input type="text" placeholder="搜索人员" class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
                  <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <div class="space-y-2 max-h-40 overflow-y-auto mb-3">
                  <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md transition-colors duration-200">
                    <div class="flex items-center">
                      <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1001/30/30" alt="用户头像" />
                      <span class="text-sm text-gray-700">张三</span>
                      <span class="text-xs text-gray-500 ml-2">销售部</span>
                    </div>
                    <button class="text-primary hover:text-primary/80 transition-colors duration-200 p-1 rounded-full hover:bg-primary/10">
                      <i class="fas fa-user-plus"></i>
                    </button>
                  </div>
                  <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md transition-colors duration-200">
                    <div class="flex items-center">
                      <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1002/30/30" alt="用户头像" />
                      <span class="text-sm text-gray-700">李四</span>
                      <span class="text-xs text-gray-500 ml-2">市场部</span>
                    </div>
                    <button class="text-primary hover:text-primary/80 transition-colors duration-200 p-1 rounded-full hover:bg-primary/10">
                      <i class="fas fa-user-plus"></i>
                    </button>
                  </div>
                  <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md transition-colors duration-200">
                    <div class="flex items-center">
                      <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1003/30/30" alt="用户头像" />
                      <span class="text-sm text-gray-700">王五</span>
                      <span class="text-xs text-gray-500 ml-2">技术部</span>
                    </div>
                    <button class="text-primary hover:text-primary/80 transition-colors duration-200 p-1 rounded-full hover:bg-primary/10">
                      <i class="fas fa-user-plus"></i>
                    </button>
                  </div>
                </div>
                <div class="mt-3">
                  <h6 class="text-xs font-medium text-gray-500 mb-2">已选人员</h6>
                  <div class="flex flex-wrap gap-2">
                    <div class="flex items-center px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">
                      <img class="h-5 w-5 rounded-full mr-1.5" src="https://picsum.photos/id/1001/30/30" alt="用户头像" />
                      张三
                      <button class="ml-1.5 text-primary/50 hover:text-primary transition-colors duration-200">
                        <i class="fas fa-times text-xs"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="border border-gray-200 rounded-lg p-4">
                <h5 class="text-sm font-medium text-gray-700 mb-3">权限设置</h5>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">查看权限</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">编辑权限</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">复制权限</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">删除权限</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">发布权限</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center">
                      <i class="fas fa-check text-primary text-xs hidden peer-checked:block"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">权限管理</span>
                  </label>
                </div>
              </div>

              <div class="border border-gray-200 rounded-lg p-4">
                <h5 class="text-sm font-medium text-gray-700 mb-3">权限有效期</h5>
                <div class="flex space-x-4 items-center">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="expiryType" value="permanent" class="sr-only peer" checked />
                    <div class="w-4 h-4 border border-gray-300 rounded-full peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center peer-checked:bg-white">
                      <div class="w-2 h-2 rounded-full bg-primary hidden peer-checked:block"></div>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">永久有效</span>
                  </label>
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="radio" name="expiryType" value="temporary" class="sr-only peer" />
                    <div class="w-4 h-4 border border-gray-300 rounded-full peer-checked:ring-2 peer-checked:ring-primary/50 peer-checked:border-primary flex items-center justify-center peer-checked:bg-white">
                      <div class="w-2 h-2 rounded-full bg-primary hidden peer-checked:block"></div>
                    </div>
                    <span class="ml-2 text-sm text-gray-700">临时有效</span>
                  </label>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3" id="expiryDateSection" style="display: none">
                  <div>
                    <label class="block text-xs text-gray-500 mb-1">开始日期</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
                  </div>
                  <div>
                    <label class="block text-xs text-gray-500 mb-1">结束日期</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">权限变更记录</h4>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 text-sm">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更时间</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更人</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更内容</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更前</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更后</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">2023-05-12 14:30</td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center">
                        <img class="h-5 w-5 rounded-full mr-1.5" src="https://picsum.photos/id/1007/30/30" alt="用户头像" />
                        <span class="text-sm text-gray-700">周八</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">权限类型变更</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">个人</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-primary">部门</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">调整为部门共享</td>
                  </tr>
                  <tr>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">2023-05-05 09:15</td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center">
                        <img class="h-5 w-5 rounded-full mr-1.5" src="https://picsum.photos/id/1001/30/30" alt="用户头像" />
                        <span class="text-sm text-gray-700">张三</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">权限设置变更</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">查看</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-primary">查看,编辑,复制</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">增加编辑和复制权限</td>
                  </tr>
                  <tr>
                    <td class="px-4 py-3 whitespace-nowrap text-xs text-gray-500">2023-05-01 16:45</td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center">
                        <img class="h-5 w-5 rounded-full mr-1.5" src="https://picsum.photos/id/1001/30/30" alt="用户头像" />
                        <span class="text-sm text-gray-700">张三</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">创建权限</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">无</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-primary">个人</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">初始创建</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button id="cancelPermissionDetail" class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm">取消</button>
          <button id="savePermissionDetail" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200 text-sm">保存设置</button>
        </div>
      </div>
    </div>
    <script src="js/common.js"></script>
    <script>
      // 打开权限详情模态框
      function openPermissionDetailModal(templateId, templateName) {
        document.getElementById('modalTemplateId').textContent = templateId;
        document.getElementById('modalTemplateName').textContent = templateName + ' - 权限设置';
        document.getElementById('modalTemplateNameDisplay').textContent = templateName;
        document.getElementById('permissionDetailModal').classList.remove('hidden');
        document.getElementById('permissionDetailModal').classList.add('flex');
      }

      // 关闭权限详情模态框
      document.getElementById('closePermissionDetailModal').addEventListener('click', function () {
        document.getElementById('permissionDetailModal').classList.add('hidden');
        document.getElementById('permissionDetailModal').classList.remove('flex');
      });

      document.getElementById('cancelPermissionDetail').addEventListener('click', function () {
        document.getElementById('permissionDetailModal').classList.add('hidden');
        document.getElementById('permissionDetailModal').classList.remove('flex');
      });

      // 权限类型切换
      const permissionTypeRadios = document.querySelectorAll('input[name="permissionType"]');
      permissionTypeRadios.forEach(radio => {
        radio.addEventListener('change', function () {
          if (this.value === 'department') {
            document.getElementById('departmentPermissionSection').style.display = 'block';
            document.getElementById('personalPermissionSection').style.display = 'none';
          } else if (this.value === 'personal') {
            document.getElementById('departmentPermissionSection').style.display = 'none';
            document.getElementById('personalPermissionSection').style.display = 'block';
          } else {
            document.getElementById('departmentPermissionSection').style.display = 'none';
            document.getElementById('personalPermissionSection').style.display = 'none';
          }
        });
      });

      // 有效期类型切换
      const expiryTypeRadios = document.querySelectorAll('input[name="expiryType"]');
      expiryTypeRadios.forEach(radio => {
        radio.addEventListener('change', function () {
          if (this.value === 'temporary') {
            document.getElementById('expiryDateSection').style.display = 'grid';
          } else {
            document.getElementById('expiryDateSection').style.display = 'none';
          }
        });
      });

      // 保存权限设置
      document.getElementById('savePermissionDetail').addEventListener('click', function () {
        // 这里添加保存逻辑
        alert('权限设置已保存！');
        document.getElementById('permissionDetailModal').classList.add('hidden');
        document.getElementById('permissionDetailModal').classList.remove('flex');
      });

      // 初始化图表
      window.addEventListener('load', function () {
        // 权限分布统计图表
        const permissionDistributionCtx = document.getElementById('permissionDistributionChart').getContext('2d');
        new Chart(permissionDistributionCtx, {
          type: 'pie',
          data: {
            labels: ['公开', '部门', '个人'],
            datasets: [
              {
                data: [45, 35, 20],
                backgroundColor: ['rgba(0, 180, 42, 0.7)', 'rgba(22, 93, 255, 0.7)', 'rgba(255, 125, 0, 0.7)'],
                borderWidth: 1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'bottom',
              },
            },
          },
        });

        // 权限申请趋势图表
        const permissionTrendCtx = document.getElementById('permissionTrendChart').getContext('2d');
        new Chart(permissionTrendCtx, {
          type: 'line',
          data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [
              {
                label: '权限申请数量',
                data: [12, 19, 15, 28, 32, 25],
                fill: false,
                borderColor: 'rgba(22, 93, 255, 0.7)',
                tension: 0.1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
              },
            },
          },
        });
      });
    </script>
    <script>document.getElementById('searchButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/searchButton', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('tableViewButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/tableViewButton', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('cardViewButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/cardViewButton', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('compactViewButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/compactViewButton', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('batchSetPermissionButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/batchSetPermissionButton', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('exportPermissionReportButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/exportPermissionReportButton', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('editPermissionBtn1').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/editPermissionBtn1', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('logPermissionBtn1').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/logPermissionBtn1', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('editPermissionBtn2').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/editPermissionBtn2', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('logPermissionBtn2').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/logPermissionBtn2', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('editPermissionBtn3').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/editPermissionBtn3', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('logPermissionBtn3').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/logPermissionBtn3', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('editPermissionBtn4').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/editPermissionBtn4', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('logPermissionBtn4').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/logPermissionBtn4', {
      method: 'GET'
    })
    // 不做处理
  });
document.getElementById('resetAllButton').addEventListener('click', function() {
    // 使用GET方式调用http://localhost:5001/test
    fetch('http://127.0.0.1:5000/test/resetAllButton', {
      method: 'GET'
    })
    // 不做处理
  });</script>
  </body>
</html>
