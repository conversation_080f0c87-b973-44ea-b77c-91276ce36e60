<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 运营报告统计分析</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/custom-tabs.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
   <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-chart-line page-title-icon"></i>
      运营报告统计分析
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营报告统计分析</div>
    </div>

    <!-- 选项卡导航 -->
    <div class="tabs">
      <button class="tab-btn active" data-tab="view">查阅情况</button>
      <button class="tab-btn" data-tab="comment">点评情况</button>
      <button class="tab-btn" data-tab="accuracy">精准度打分</button>
      <button class="tab-btn" data-tab="task">优化任务单</button>
    </div>

    <!-- 选项卡内容 -->
    <div class="tab-content active" id="view">
      <!-- 查阅情况查询 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-search"></i> 运营报告查阅情况查询</div>
          <form id="viewSearchForm" style="display: flex; flex-wrap: wrap; gap: 16px;">
            <div style="flex: 1; min-width: 200px;">
              <label for="viewReportName" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告名称</label>
              <input type="text" id="viewReportName" placeholder="请输入运营报告名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="viewReader" style="display: block; margin-bottom: 8px; font-weight: 500;">查阅人</label>
              <input type="text" id="viewReader" placeholder="请输入查阅人" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="viewStartDate" style="display: block; margin-bottom: 8px; font-weight: 500;">开始时间</label>
              <input type="date" id="viewStartDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="viewEndDate" style="display: block; margin-bottom: 8px; font-weight: 500;">结束时间</label>
              <input type="date" id="viewEndDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="viewDept" style="display: block; margin-bottom: 8px; font-weight: 500;">查阅人机构</label>
              <select id="viewDept" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="">请选择机构</option>
                <option value="dept1">技术部</option>
                <option value="dept2">市场部</option>
                <option value="dept3">运营部</option>
                <option value="dept4">财务部</option>
              </select>
            </div>
            <div style="align-self: flex-end;">
              <button class="btn search-btn" onclick="searchViewRecords(event)"><i class="fas fa-search"></i> 查询</button>
              <button class="btn reset-btn" onclick="resetViewForm(event)"><i class="fas fa-undo"></i> 重置</button>
              <button class="btn" style="border: 1px solid var(--border-color); margin-left: 12px;" onclick="showViewAdvancedSearch()"><i class="fas fa-filter"></i> 高级筛选</button>
            </div>
          </form>
        </div>
      </div>

      <!-- 查阅情况统计 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 35px;"><i class="fas fa-chart-bar"></i> 运营报告查阅情况统计</div>
          <div class="stat-cards-container" style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 20px;">
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-eye"></i></div>
    <div class="stat-content">
      <div class="stat-title">总查阅次数</div>
      <div class="stat-value">128</div>
      <div class="stat-change">+12% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
    <div class="stat-content">
      <div class="stat-title">被查阅报告数</div>
      <div class="stat-value">24</div>
      <div class="stat-change">+5% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-users"></i></div>
    <div class="stat-content">
      <div class="stat-title">查阅人数</div>
      <div class="stat-value">46</div>
      <div class="stat-change">+8% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-clock"></i></div>
    <div class="stat-content">
      <div class="stat-title">平均阅读时长</div>
      <div class="stat-value">18分钟</div>
      <div class="stat-change">+2分钟 较上月</div>
    </div>
  </div>
</div>

          <div class="chart-container" style="height: 300px;">
            <canvas id="viewChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 查阅记录表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>查阅记录ID</th>
                <th>运营报告名称</th>
                <th>运营报告实例ID</th>
                <th>阅读时间</th>
                <th>阅读人工号</th>
                <th>阅读人机构</th>
                <th>阅读时长(分钟)</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="viewTableBody">
              <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 点评情况选项卡 -->
    <div class="tab-content" id="comment">
      <!-- 点评情况查询 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 35px;"><i class="fas fa-search"></i> 运营报告点评情况查询</div>
          <form id="commentSearchForm" style="display: flex; flex-wrap: wrap; gap: 16px;">
            <div style="flex: 1; min-width: 200px;">
              <label for="commentReportName" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告名称</label>
              <input type="text" id="commentReportName" placeholder="请输入运营报告名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="commenter" style="display: block; margin-bottom: 8px; font-weight: 500;">点评人</label>
              <input type="text" id="commenter" placeholder="请输入点评人" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="commentStartDate" style="display: block; margin-bottom: 8px; font-weight: 500;">开始时间</label>
              <input type="date" id="commentStartDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="commentEndDate" style="display: block; margin-bottom: 8px; font-weight: 500;">结束时间</label>
              <input type="date" id="commentEndDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="commentDept" style="display: block; margin-bottom: 8px; font-weight: 500;">点评人机构</label>
              <select id="commentDept" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="">请选择机构</option>
                <option value="dept1">技术部</option>
                <option value="dept2">市场部</option>
                <option value="dept3">运营部</option>
                <option value="dept4">财务部</option>
              </select>
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="commentOverall" style="display: block; margin-bottom: 8px; font-weight: 500;">整体评价</label>
              <select id="commentOverall" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="">请选择评价</option>
                <option value="good">好评</option>
                <option value="bad">中评</option>
                <option value="bad">差评</option>
              </select>
            </div>
            <div style="align-self: flex-end;">
              <button class="btn search-btn" onclick="searchCommentRecords(event)"><i class="fas fa-search"></i> 查询</button>
              <button class="btn reset-btn" onclick="resetCommentForm(event)"><i class="fas fa-undo"></i> 重置</button>
              <button class="btn" style="border: 1px solid var(--border-color); margin-left: 12px;" onclick="showCommentAdvancedSearch()"><i class="fas fa-filter"></i> 高级筛选</button>
            </div>
          </form>
        </div>
      </div>

      <!-- 点评情况统计 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-chart-pie"></i> 运营报告点评情况统计</div>


          <!-- 数据概览卡片 -->
<div class="stat-cards-container" style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 20px;">
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-comment"></i></div>
    <div class="stat-content">
      <div class="stat-title">总点评数</div>
      <div class="stat-value">76</div>
      <div class="stat-change">+15% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-thumbs-up"></i></div>
    <div class="stat-content">
      <div class="stat-title">好评率</div>
      <div class="stat-value">65%</div>
      <div class="stat-change">+3% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-thumbs-down"></i></div>
    <div class="stat-content">
      <div class="stat-title">差评率</div>
      <div class="stat-value">10%</div>
      <div class="stat-change">-2% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
    <div class="stat-content">
      <div class="stat-title">被点评报告数</div>
      <div class="stat-value">18</div>
      <div class="stat-change">+4 较上月</div>
    </div>
  </div>
</div>


          <div class="chart-container" style="height: 300px;">
            <canvas id="commentChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 点评记录表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>运营报告名称</th>
                <th>运营报告实例ID</th>
                <th>运营报告点评实例ID</th>
                <th>点评人</th>
                <th>点评时间</th>
                <th>点评人机构</th>
                <th>点评内容</th>
                <th>整体评价</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="commentTableBody">
              <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 精准度打分选项卡 -->
    <div class="tab-content" id="accuracy">
      <!-- 精准度打分查询 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-search"></i> 运营报告精准度打分查询</div>
          <form id="accuracySearchForm" style="display: flex; flex-wrap: wrap; gap: 16px;">
            <div style="flex: 1; min-width: 200px;">
              <label for="accuracyReportName" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告名称</label>
              <input type="text" id="accuracyReportName" placeholder="请输入运营报告名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="scorer" style="display: block; margin-bottom: 8px; font-weight: 500;">打分人</label>
              <input type="text" id="scorer" placeholder="请输入打分人" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="accuracyStartDate" style="display: block; margin-bottom: 8px; font-weight: 500;">开始时间</label>
              <input type="date" id="accuracyStartDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="accuracyEndDate" style="display: block; margin-bottom: 8px; font-weight: 500;">结束时间</label>
              <input type="date" id="accuracyEndDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="accuracyDept" style="display: block; margin-bottom: 8px; font-weight: 500;">打分人机构</label>
              <select id="accuracyDept" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="">请选择机构</option>
                <option value="dept1">技术部</option>
                <option value="dept2">市场部</option>
                <option value="dept3">运营部</option>
                <option value="dept4">财务部</option>
              </select>
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="scoreRange" style="display: block; margin-bottom: 8px; font-weight: 500;">分值区间</label>
              <select id="scoreRange" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="">请选择分值区间</option>
                <option value="0-50">0-50分</option>
                <option value="51-70">51-70分</option>
                <option value="71-85">71-85分</option>
                <option value="86-100">86-100分</option>
              </select>
            </div>
            <div style="align-self: flex-end;">
              <button class="btn search-btn" onclick="searchAccuracyRecords(event)"><i class="fas fa-search"></i> 查询</button>
              <button class="btn reset-btn" onclick="resetAccuracyForm(event)"><i class="fas fa-undo"></i> 重置</button>
              <button class="btn" style="border: 1px solid var(--border-color); margin-left: 12px;" onclick="showAccuracyAdvancedSearch()"><i class="fas fa-filter"></i> 高级筛选</button>
            </div>
          </form>
        </div>
      </div>

      <!-- 精准度打分统计 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 35px;"><i class="fas fa-chart-bar"></i> 运营报告精准度打分统计</div>
          <!-- 数据概览卡片 -->
<div class="stat-cards-container" style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 20px;">
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-star"></i></div>
    <div class="stat-content">
      <div class="stat-title">总打分次数</div>
      <div class="stat-value">92</div>
      <div class="stat-change">+18% 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-percentage"></i></div>
    <div class="stat-content">
      <div class="stat-title">平均精准度</div>
      <div class="stat-value">82分</div>
      <div class="stat-change">+4分 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-award"></i></div>
    <div class="stat-content">
      <div class="stat-title">优秀报告数 (≥85分)</div>
      <div class="stat-value">21</div>
      <div class="stat-change">+5 较上月</div>
    </div>
  </div>
  <div class="stat-card">
    <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
    <div class="stat-content">
      <div class="stat-title">被打分报告数</div>
      <div class="stat-value">24</div>
      <div class="stat-change">+3 较上月</div>
    </div>
  </div>
</div>
          <div class="chart-container" style="height: 300px;">
            <canvas id="accuracyChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 精准度打分记录表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>运营报告名称</th>
                <th>运营报告实例ID</th>
                <th>运营报告打分实例ID</th>
                <th>打分人</th>
                <th>打分时间</th>
                <th>打分人机构</th>
                <th>分值</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="accuracyTableBody">
              <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 优化任务单选项卡 -->
    <div class="tab-content" id="task">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div style="font-size: 16px; font-weight: 500;"><i class="fas fa-tasks"></i> 优化任务单管理</div>
        <button class="btn btn-primary" data-modal-target="addTaskModal"><i class="fas fa-plus"></i> 新增任务单</button>
      </div>

      <!-- 优化任务单查询 -->
      <div class="card" style="margin-bottom: 20px;">
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px;"><i class="fas fa-search"></i> 优化任务单查询</div>
          <form id="taskSearchForm" style="display: flex; flex-wrap: wrap; gap: 16px;">
            <div style="flex: 1; min-width: 200px;">
              <label for="taskName" style="display: block; margin-bottom: 8px; font-weight: 500;">任务名称</label>
              <input type="text" id="taskName" placeholder="请输入任务名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="taskReportName" style="display: block; margin-bottom: 8px; font-weight: 500;">运营报告名称</label>
              <input type="text" id="taskReportName" placeholder="请输入运营报告名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="taskStatus" style="display: block; margin-bottom: 8px; font-weight: 500;">任务状态</label>
              <select id="taskStatus" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="">请选择状态</option>
                <option value="pending">待处理</option>
                <option value="processing">处理中</option>
                <option value="completed">已完成</option>
                <option value="canceled">已取消</option>
              </select>
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="taskCreator" style="display: block; margin-bottom: 8px; font-weight: 500;">创建人</label>
              <input type="text" id="taskCreator" placeholder="请输入创建人" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="taskStartDate" style="display: block; margin-bottom: 8px; font-weight: 500;">创建时间开始</label>
              <input type="date" id="taskStartDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="flex: 1; min-width: 200px;">
              <label for="taskEndDate" style="display: block; margin-bottom: 8px; font-weight: 500;">创建时间结束</label>
              <input type="date" id="taskEndDate" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            </div>
            <div style="align-self: flex-end;">
              <button class="btn search-btn" onclick="searchTaskRecords(event)"><i class="fas fa-search"></i> 查询</button>
              <button class="btn reset-btn" onclick="resetTaskForm(event)"><i class="fas fa-undo"></i> 重置</button>
              <button class="btn" style="border: 1px solid var(--border-color); margin-left: 12px;" onclick="showTaskAdvancedSearch()"><i class="fas fa-filter"></i> 高级筛选</button>
            </div>
          </form>
        </div>
      </div>

      <!-- 优化任务单表格 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>任务名称</th>
                <th>运营报告名称</th>
                <th>优化内容</th>
                <th>任务状态</th>
                <th>创建时间</th>
                <th>创建人</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="taskTableBody">
              <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item">4</div>
          <div class="pagination-item">5</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增优化任务单模态框 -->
  <div class="modal" id="addTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增优化任务单</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addTaskForm">
          <div class="form-group">
            <label for="newTaskName">任务名称</label>
            <input type="text" id="newTaskName" name="taskName" required placeholder="请输入任务名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
          </div>
          <div class="form-group">
            <label for="newTaskReportName">运营报告名称</label>
            <select id="newTaskReportName" name="reportName" required style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="">请选择运营报告</option>
              <option value="report1">用户增长分析报告</option>
              <option value="report2">产品销售周报</option>
              <option value="report3">6月运营数据分析</option>
              <option value="report4">活动效果评估报告</option>
            </select>
          </div>
          <div class="form-group">
            <label for="newTaskContent">优化内容</label>
            <textarea id="newTaskContent" name="taskContent" rows="4" required placeholder="请输入优化内容" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" type="submit">创建任务</button>
      </div>
    </div>
  </div>

  <!-- 编辑优化任务单模态框 -->
  <div class="modal" id="editTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑优化任务单</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="editTaskForm">
          <input type="hidden" id="editTaskId">
          <div class="form-group">
            <label for="editTaskName">任务名称</label>
            <input type="text" id="editTaskName" name="taskName" required placeholder="请输入任务名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
          </div>
          <div class="form-group">
            <label for="editTaskReportName">运营报告名称</label>
            <select id="editTaskReportName" name="reportName" required style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="">请选择运营报告</option>
              <option value="report1">用户增长分析报告</option>
              <option value="report2">产品销售周报</option>
              <option value="report3">6月运营数据分析</option>
              <option value="report4">活动效果评估报告</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editTaskContent">优化内容</label>
            <textarea id="editTaskContent" name="taskContent" rows="4" required placeholder="请输入优化内容" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);"></textarea>
          </div>
          <div class="form-group">
            <label for="editTaskStatus">任务状态</label>
            <select id="editTaskStatus" name="taskStatus" required style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="pending">待处理</option>
              <option value="processing">处理中</option>
              <option value="completed">已完成</option>
              <option value="canceled">已取消</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" type="submit">保存修改</button>
      </div>
    </div>
  </div>

  <script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化选项卡切换
      initTabs();

      // 初始化图表
function initCharts() {
  // 查阅情况图表
  const viewCtx = document.getElementById('viewChart').getContext('2d');
  new Chart(viewCtx, {
    type: 'bar',
    data: {
      labels: ['用户增长分析报告', '产品销售周报', '6月运营数据分析', '活动效果评估报告', '客户满意度调查', '市场竞争分析'],
      datasets: [{
        label: '查阅次数',
        data: [12, 8, 5, 15, 9, 7],
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }, {
        label: '平均阅读时长(分钟)',
        data: [15, 20, 25, 18, 12, 16],
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // 点评情况图表
  const commentCtx = document.getElementById('commentChart').getContext('2d');
  new Chart(commentCtx, {
    type: 'pie',
    data: {
      labels: ['好评', '中评', '差评'],
      datasets: [{
        data: [65, 25, 10],
        backgroundColor: [
          'rgba(75, 192, 192, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(255, 99, 132, 0.7)'
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 99, 132, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });

  // 精准度打分图表
  const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
  new Chart(accuracyCtx, {
    type: 'bar',
    data: {
      labels: ['0-50分', '51-70分', '71-85分', '86-100分'],
      datasets: [{
        label: '报告数量',
        data: [2, 5, 12, 21],
        backgroundColor: 'rgba(153, 102, 255, 0.5)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 1
      }, {
        label: '平均分',
        data: [45, 62, 78, 91],
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        borderColor: 'rgba(255, 159, 64, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}


      // 加载模拟数据
      loadMockData();

      // 初始化模态框
      initModals();

      // 初始化图表
      initCharts();
    });

    // 初始化选项卡切换
    function initTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          // 移除所有活动状态
          tabBtns.forEach(b => b.classList.remove('active'));
          tabContents.forEach(c => c.classList.remove('active'));

          // 添加活动状态到当前点击的按钮
          this.classList.add('active');

          // 显示对应的内容
          const tabId = this.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
        });
      });
    }

    // 加载模拟数据
    function loadMockData() {
      // 查阅记录模拟数据
      const viewRecords = [
        { recordId: 'VR001', reportName: '用户增长分析报告', reportInstanceId: 'RI001', readTime: '2023-10-01 09:30', readerId: 'EMP001', dept: '市场部', duration: 15 },
        { recordId: 'VR002', reportName: '产品销售周报', reportInstanceId: 'RI002', readTime: '2023-10-01 10:15', readerId: 'EMP002', dept: '销售部', duration: 20 },
        { recordId: 'VR003', reportName: '6月运营数据分析', reportInstanceId: 'RI003', readTime: '2023-10-01 14:20', readerId: 'EMP003', dept: '运营部', duration: 25 },
        { recordId: 'VR004', reportName: '活动效果评估报告', reportInstanceId: 'RI004', readTime: '2023-10-02 08:45', readerId: 'EMP004', dept: '市场部', duration: 18 },
        { recordId: 'VR005', reportName: '客户满意度调查', reportInstanceId: 'RI005', readTime: '2023-10-02 11:30', readerId: 'EMP005', dept: '客服部', duration: 12 }
      ];

      // 渲染查阅记录表格
      const viewTableBody = document.getElementById('viewTableBody');
      viewRecords.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.recordId}</td>
          <td>${record.reportName}</td>
          <td>${record.reportInstanceId}</td>
          <td>${record.readTime}</td>
          <td>${record.readerId}</td>
          <td>${record.dept}</td>
          <td>${record.duration}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-eye"></i> 查看</button>
          </td>
        `;
        viewTableBody.appendChild(row);
      });

      // 点评记录模拟数据
      const commentRecords = [
        { reportName: '用户增长分析报告', reportInstanceId: 'RI001', commentInstanceId: 'CI001', commenter: '张三', commentTime: '2023-10-01 10:00', dept: '市场部', content: '数据详实，分析到位', overall: '好评' },
        { reportName: '产品销售周报', reportInstanceId: 'RI002', commentInstanceId: 'CI002', commenter: '李四', commentTime: '2023-10-01 11:00', dept: '销售部', content: '希望增加区域对比数据', overall: '中评' },
        { reportName: '6月运营数据分析', reportInstanceId: 'RI003', commentInstanceId: 'CI003', commenter: '王五', commentTime: '2023-10-01 15:00', dept: '运营部', content: '分析逻辑不清晰', overall: '差评' },
        { reportName: '活动效果评估报告', reportInstanceId: 'RI004', commentInstanceId: 'CI004', commenter: '赵六', commentTime: '2023-10-02 09:00', dept: '市场部', content: '建议增加ROI分析', overall: '中评' },
        { reportName: '客户满意度调查', reportInstanceId: 'RI005', commentInstanceId: 'CI005', commenter: '钱七', commentTime: '2023-10-02 12:00', dept: '客服部', content: '非常全面，有参考价值', overall: '好评' }
      ];

      // 渲染点评记录表格
      const commentTableBody = document.getElementById('commentTableBody');
      commentRecords.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.reportName}</td>
          <td>${record.reportInstanceId}</td>
          <td>${record.commentInstanceId}</td>
          <td>${record.commenter}</td>
          <td>${record.commentTime}</td>
          <td>${record.dept}</td>
          <td>${record.content}</td>
          <td>${record.overall}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-eye"></i>查看</button>
          </td>
        `;
        commentTableBody.appendChild(row);
      });

      // 精准度打分记录模拟数据
      const accuracyRecords = [
        { reportName: '用户增长分析报告', reportInstanceId: 'RI001', scoreInstanceId: 'SI001', scorer: '张三', scoreTime: '2023-10-01 10:30', dept: '市场部', score: 92 },
        { reportName: '产品销售周报', reportInstanceId: 'RI002', scoreInstanceId: 'SI002', scorer: '李四', scoreTime: '2023-10-01 11:30', dept: '销售部', score: 85 },
        { reportName: '6月运营数据分析', reportInstanceId: 'RI003', scoreInstanceId: 'SI003', scorer: '王五', scoreTime: '2023-10-01 15:30', dept: '运营部', score: 76 },
        { reportName: '活动效果评估报告', reportInstanceId: 'RI004', scoreInstanceId: 'SI004', scorer: '赵六', scoreTime: '2023-10-02 09:30', dept: '市场部', score: 88 },
        { reportName: '客户满意度调查', reportInstanceId: 'RI005', scoreInstanceId: 'SI005', scorer: '钱七', scoreTime: '2023-10-02 12:30', dept: '客服部', score: 95 }
      ];

      // 渲染精准度打分记录表格
      const accuracyTableBody = document.getElementById('accuracyTableBody');
      accuracyRecords.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.reportName}</td>
          <td>${record.reportInstanceId}</td>
          <td>${record.scoreInstanceId}</td>
          <td>${record.scorer}</td>
          <td>${record.scoreTime}</td>
          <td>${record.dept}</td>
          <td>${record.score}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-eye"></i> 查看</button>
          </td>
        `;
        accuracyTableBody.appendChild(row);
      });

      // 优化任务单模拟数据
      const taskRecords = [
        { name: '用户增长报告优化', reportName: '用户增长分析报告', content: '增加季度对比数据', status: '处理中', createTime: '2023-10-01 11:00', creator: '张三' },
        { name: '销售周报格式调整', reportName: '产品销售周报', content: '调整区域数据展示方式', status: '已完成', createTime: '2023-10-01 14:00', creator: '李四' },
        { name: '运营数据分析优化', reportName: '6月运营数据分析', content: '重构分析逻辑，增加图表展示', status: '待处理', createTime: '2023-10-02 09:00', creator: '王五' }
      ];

      // 渲染优化任务单表格
      const taskTableBody = document.getElementById('taskTableBody');
      taskRecords.forEach(record => {
        row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.name}</td>
          <td>${record.reportName}</td>
          <td>${record.content}</td>
          <td>${record.status}</td>
          <td>${record.createTime}</td>
          <td>${record.creator}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-sm"><i class="fas fa-trash"></i> 删除</button>
          </td>
        `;
        taskTableBody.appendChild(row);
      });
    }

    // 初始化模态框
    function initModals() {
      // 打开新增任务单模态框
      document.querySelector('[data-modal-target="addTaskModal"]').addEventListener('click', function() {
        document.getElementById('addTaskModal').classList.add('show');
      });

      // 关闭模态框
      document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', function() {
          this.closest('.modal').classList.remove('show');
        });
      });

      // 点击模态框外部关闭
      window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
          event.target.classList.remove('show');
        }
      });
    }

    // 查阅情况查询
    function searchViewRecords(event) {
      event.preventDefault();
      // 实际项目中这里会发送请求获取数据
      alert('查阅情况查询功能已触发');
    }

    // 重置查阅情况查询表单
    function resetViewForm(event) {
      event.preventDefault();
      document.getElementById('viewSearchForm').reset();
    }

    // 显示查阅情况高级筛选
    function showViewAdvancedSearch() {
      alert('查阅情况高级筛选功能待实现');
    }

    // 点评情况查询
    function searchCommentRecords(event) {
      event.preventDefault();
      // 实际项目中这里会发送请求获取数据
      alert('点评情况查询功能已触发');
    }

    // 重置点评情况查询表单
    function resetCommentForm(event) {
      event.preventDefault();
      document.getElementById('commentSearchForm').reset();
    }

    // 显示点评情况高级筛选
    function showCommentAdvancedSearch() {
      alert('点评情况高级筛选功能待实现');
    }

    // 精准度打分查询
    function searchAccuracyRecords(event) {
      event.preventDefault();
      // 实际项目中这里会发送请求获取数据
      alert('精准度打分查询功能已触发');
    }

    // 重置精准度打分查询表单
    function resetAccuracyForm(event) {
      event.preventDefault();
      document.getElementById('accuracySearchForm').reset();
    }

    // 显示精准度打分高级筛选
    function showAccuracyAdvancedSearch() {
      alert('精准度打分高级筛选功能待实现');
    }

    // 优化任务单查询
    function searchTaskRecords(event) {
      event.preventDefault();
      // 实际项目中这里会发送请求获取数据
      alert('优化任务单查询功能已触发');
    }

    // 重置优化任务单查询表单
    function resetTaskForm(event) {
      event.preventDefault();
      document.getElementById('taskSearchForm').reset();
    }

    // 显示优化任务单高级筛选
    function showTaskAdvancedSearch() {
      alert('优化任务单高级筛选功能待实现');
    }
  </script>
<div id="notification" style="position: fixed; top: 20px; right: 20px; padding: 15px; border-radius: 4px; color: white; z-index: 1000; display: none;"></div>
</body>
</html>
  <script>
    // 全局模拟数据
    let viewRecords = [];
    let commentRecords = [];
    let accuracyRecords = [];
    let taskRecords = [];

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化选项卡切换
      initTabs();

      // 初始化图表
function initCharts() {
  // 查阅情况图表
  const viewCtx = document.getElementById('viewChart').getContext('2d');
  new Chart(viewCtx, {"type":"bar","data":{"labels":["用户增长分析报告","产品销售周报","6月运营数据分析","活动效果评估报告","客户满意度调查","市场竞争分析"],"datasets":[{"label":"查阅次数","data":[12,8,5,15,9,7],"backgroundColor":"rgba(54, 162, 235, 0.5)","borderColor":"rgba(54, 162, 235, 1)","borderWidth":1},{"label":"平均阅读时长(分钟)","data":[15,20,25,18,12,16],"backgroundColor":"rgba(75, 192, 192, 0.5)","borderColor":"rgba(75, 192, 192, 1)","borderWidth":1}]},"options":{"responsive":true,"maintainAspectRatio":false,"scales":{"y":{"beginAtZero":true}}}});

  // 点评情况图表
  const commentCtx = document.getElementById('commentChart').getContext('2d');
  new Chart(commentCtx, {"type":"pie","data":{"labels":["好评","中评","差评"],"datasets":[{"data":[65,25,10],"backgroundColor":["rgba(75, 192, 192, 0.7)","rgba(255, 206, 86, 0.7)","rgba(255, 99, 132, 0.7)"],"borderColor":["rgba(75, 192, 192, 1)","rgba(255, 206, 86, 1)","rgba(255, 99, 132, 1)"],"borderWidth":1}]},"options":{"responsive":true,"maintainAspectRatio":false}});

  // 精准度打分图表
  const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
  new Chart(accuracyCtx, {"type":"bar","data":{"labels":["0-50分","51-70分","71-85分","86-100分"],"datasets":[{"label":"报告数量","data":[2,5,12,21],"backgroundColor":"rgba(153, 102, 255, 0.5)","borderColor":"rgba(153, 102, 255, 1)","borderWidth":1},{"label":"平均分","data":[45,62,78,91],"backgroundColor":"rgba(255, 159, 64, 0.5)","borderColor":"rgba(255, 159, 64, 1)","borderWidth":1}]},"options":{"responsive":true,"maintainAspectRatio":false,"scales":{"y":{"beginAtZero":true}}}});
}

      // 加载模拟数据
      loadMockData();

      // 初始化模态框
      initModals();

      // 初始化图表
      initCharts();
    });

    // 初始化选项卡切换
    function initTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          // 移除所有活动状态
          tabBtns.forEach(b => b.classList.remove('active'));
          tabContents.forEach(c => c.classList.remove('active'));

          // 添加活动状态到当前点击的按钮
          this.classList.add('active');

          // 显示对应的内容
          const tabId = this.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
        });
      });
    }

    // 加载模拟数据
    function loadMockData() {
      // 查阅记录模拟数据
      viewRecords = [
        { recordId: 'VR001', reportName: '用户增长分析报告', reportInstanceId: 'RI001', readTime: '2023-10-01 09:30', readerId: 'EMP001', dept: '市场部', duration: 15 },
        { recordId: 'VR002', reportName: '产品销售周报', reportInstanceId: 'RI002', readTime: '2023-10-01 10:15', readerId: 'EMP002', dept: '销售部', duration: 20 },
        { recordId: 'VR003', reportName: '6月运营数据分析', reportInstanceId: 'RI003', readTime: '2023-10-01 14:20', readerId: 'EMP003', dept: '运营部', duration: 25 },
        { recordId: 'VR004', reportName: '活动效果评估报告', reportInstanceId: 'RI004', readTime: '2023-10-02 08:45', readerId: 'EMP004', dept: '市场部', duration: 18 },
        { recordId: 'VR005', reportName: '客户满意度调查', reportInstanceId: 'RI005', readTime: '2023-10-02 11:30', readerId: 'EMP005', dept: '客服部', duration: 12 },
        { recordId: 'VR006', reportName: '市场竞争分析', reportInstanceId: 'RI006', readTime: '2023-10-03 09:15', readerId: 'EMP006', dept: '市场部', duration: 30 },
        { recordId: 'VR007', reportName: '用户增长分析报告', reportInstanceId: 'RI001', readTime: '2023-10-03 13:45', readerId: 'EMP007', dept: '运营部', duration: 22 },
        { recordId: 'VR008', reportName: '产品销售周报', reportInstanceId: 'RI002', readTime: '2023-10-04 10:30', readerId: 'EMP008', dept: '销售部', duration: 16 },
        { recordId: 'VR009', reportName: '活动效果评估报告', reportInstanceId: 'RI004', readTime: '2023-10-04 15:20', readerId: 'EMP009', dept: '市场部', duration: 28 },
        { recordId: 'VR010', reportName: '6月运营数据分析', reportInstanceId: 'RI003', readTime: '2023-10-05 09:00', readerId: 'EMP010', dept: '运营部', duration: 19 }
      ];

      // 渲染查阅记录表格
      renderViewTable(viewRecords);

      // 点评记录模拟数据
      commentRecords = [
        { reportName: '用户增长分析报告', reportInstanceId: 'RI001', commentInstanceId: 'CI001', commenter: '张三', commentTime: '2023-10-01 10:00', dept: '市场部', content: '数据详实，分析到位', overall: '好评' },
        { reportName: '产品销售周报', reportInstanceId: 'RI002', commentInstanceId: 'CI002', commenter: '李四', commentTime: '2023-10-01 11:00', dept: '销售部', content: '希望增加区域对比数据', overall: '中评' },
        { reportName: '6月运营数据分析', reportInstanceId: 'RI003', commentInstanceId: 'CI003', commenter: '王五', commentTime: '2023-10-01 15:00', dept: '运营部', content: '分析逻辑不清晰', overall: '差评' },
        { reportName: '活动效果评估报告', reportInstanceId: 'RI004', commentInstanceId: 'CI004', commenter: '赵六', commentTime: '2023-10-02 09:00', dept: '市场部', content: '建议增加ROI分析', overall: '中评' },
        { reportName: '客户满意度调查', reportInstanceId: 'RI005', commentInstanceId: 'CI005', commenter: '钱七', commentTime: '2023-10-02 12:00', dept: '客服部', content: '非常全面，有参考价值', overall: '好评' },
        { reportName: '市场竞争分析', reportInstanceId: 'RI006', commentInstanceId: 'CI006', commenter: '孙八', commentTime: '2023-10-03 11:30', dept: '市场部', content: '竞品数据不够全面', overall: '中评' },
        { reportName: '用户增长分析报告', reportInstanceId: 'RI001', commentInstanceId: 'CI007', commenter: '周九', commentTime: '2023-10-03 14:15', dept: '运营部', content: '增长趋势预测准确', overall: '好评' },
        { reportName: '产品销售周报', reportInstanceId: 'RI002', commentInstanceId: 'CI008', commenter: '吴十', commentTime: '2023-10-04 10:45', dept: '销售部', content: '数据更新不及时', overall: '差评' },
        { reportName: '活动效果评估报告', reportInstanceId: 'RI004', commentInstanceId: 'CI009', commenter: '郑十一', commentTime: '2023-10-04 16:00', dept: '市场部', content: '活动效果超出预期', overall: '好评' },
        { reportName: '6月运营数据分析', reportInstanceId: 'RI003', commentInstanceId: 'CI010', commenter: '王十二', commentTime: '2023-10-05 10:30', dept: '运营部', content: '图表展示不够直观', overall: '中评' }
      ];

      // 渲染点评记录表格
      renderCommentTable(commentRecords);

      // 精准度打分记录模拟数据
      accuracyRecords = [
        { reportName: '用户增长分析报告', reportInstanceId: 'RI001', scoreInstanceId: 'SI001', scorer: '张三', scoreTime: '2023-10-01 10:30', dept: '市场部', score: 92 },
        { reportName: '产品销售周报', reportInstanceId: 'RI002', scoreInstanceId: 'SI002', scorer: '李四', scoreTime: '2023-10-01 11:30', dept: '销售部', score: 85 },
        { reportName: '6月运营数据分析', reportInstanceId: 'RI003', scoreInstanceId: 'SI003', scorer: '王五', scoreTime: '2023-10-01 15:30', dept: '运营部', score: 76 },
        { reportName: '活动效果评估报告', reportInstanceId: 'RI004', scoreInstanceId: 'SI004', scorer: '赵六', scoreTime: '2023-10-02 09:30', dept: '市场部', score: 88 },
        { reportName: '客户满意度调查', reportInstanceId: 'RI005', scoreInstanceId: 'SI005', scorer: '钱七', scoreTime: '2023-10-02 12:30', dept: '客服部', score: 95 },
        { reportName: '市场竞争分析', reportInstanceId: 'RI006', scoreInstanceId: 'SI006', scorer: '孙八', scoreTime: '2023-10-03 12:00', dept: '市场部', score: 78 },
        { reportName: '用户增长分析报告', reportInstanceId: 'RI001', scoreInstanceId: 'SI007', scorer: '周九', scoreTime: '2023-10-03 15:00', dept: '运营部', score: 90 },
        { reportName: '产品销售周报', reportInstanceId: 'RI002', scoreInstanceId: 'SI008', scorer: '吴十', scoreTime: '2023-10-04 11:00', dept: '销售部', score: 82 },
        { reportName: '活动效果评估报告', reportInstanceId: 'RI004', scoreInstanceId: 'SI009', scorer: '郑十一', scoreTime: '2023-10-04 16:30', dept: '市场部', score: 89 },
        { reportName: '6月运营数据分析', reportInstanceId: 'RI003', scoreInstanceId: 'SI010', scorer: '王十二', scoreTime: '2023-10-05 11:00', dept: '运营部', score: 74 }
      ];

      // 渲染精准度打分记录表格
      renderAccuracyTable(accuracyRecords);

      // 优化任务单模拟数据
      taskRecords = [
        { id: 'T001', name: '用户增长报告优化', reportName: '用户增长分析报告', content: '增加季度对比数据', status: '处理中', createTime: '2023-10-01 11:00', creator: '张三' },
        { id: 'T002', name: '销售周报格式调整', reportName: '产品销售周报', content: '调整区域数据展示方式', status: '已完成', createTime: '2023-10-01 14:00', creator: '李四' },
        { id: 'T003', name: '运营数据分析优化', reportName: '6月运营数据分析', content: '重构分析逻辑，增加图表展示', status: '待处理', createTime: '2023-10-02 09:00', creator: '王五' },
        { id: 'T004', name: '活动评估报告优化', reportName: '活动效果评估报告', content: '增加ROI分析模块', status: '处理中', createTime: '2023-10-02 14:30', creator: '赵六' },
        { id: 'T005', name: '满意度调查改进', reportName: '客户满意度调查', content: '增加客户细分维度', status: '已完成', createTime: '2023-10-03 10:00', creator: '钱七' }
      ];

      // 渲染优化任务单表格
      renderTaskTable(taskRecords);
    }

    // 渲染查阅记录表格
    function renderViewTable(data) {
      const viewTableBody = document.getElementById('viewTableBody');
      viewTableBody.innerHTML = '';

      if (data.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="8" style="text-align: center;">暂无数据</td>`;
        viewTableBody.appendChild(emptyRow);
        return;
      }

      data.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.recordId}</td>
          <td>${record.reportName}</td>
          <td>${record.reportInstanceId}</td>
          <td>${record.readTime}</td>
          <td>${record.readerId}</td>
          <td>${record.dept}</td>
          <td>${record.duration}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-eye"></i> 查看</button>
          </td>
        `;
        viewTableBody.appendChild(row);
      });
    }

    // 渲染点评记录表格
    function renderCommentTable(data) {
      const commentTableBody = document.getElementById('commentTableBody');
      commentTableBody.innerHTML = '';

      if (data.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="9" style="text-align: center;">暂无数据</td>`;
        commentTableBody.appendChild(emptyRow);
        return;
      }

      data.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.reportName}</td>
          <td>${record.reportInstanceId}</td>
          <td>${record.commentInstanceId}</td>
          <td>${record.commenter}</td>
          <td>${record.commentTime}</td>
          <td>${record.dept}</td>
          <td>${record.content}</td>
          <td>${record.overall}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-eye"></i> 查看</button>
          </td>
        `;
        commentTableBody.appendChild(row);
      });
    }

    // 渲染精准度打分记录表格
    function renderAccuracyTable(data) {
      const accuracyTableBody = document.getElementById('accuracyTableBody');
      accuracyTableBody.innerHTML = '';

      if (data.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="8" style="text-align: center;">暂无数据</td>`;
        accuracyTableBody.appendChild(emptyRow);
        return;
      }

      data.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.reportName}</td>
          <td>${record.reportInstanceId}</td>
          <td>${record.scoreInstanceId}</td>
          <td>${record.scorer}</td>
          <td>${record.scoreTime}</td>
          <td>${record.dept}</td>
          <td>${record.score}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-eye"></i> 查看</button>
          </td>
        `;
        accuracyTableBody.appendChild(row);
      });
    }

    // 渲染优化任务单表格
    function renderTaskTable(data) {
      const taskTableBody = document.getElementById('taskTableBody');
      taskTableBody.innerHTML = '';

      if (data.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="7" style="text-align: center;">暂无数据</td>`;
        taskTableBody.appendChild(emptyRow);
        return;
      }

      data.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${record.name}</td>
          <td>${record.reportName}</td>
          <td>${record.content}</td>
          <td>${record.status}</td>
          <td>${record.createTime}</td>
          <td>${record.creator}</td>
          <td>
            <button class="btn btn-sm"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-sm"><i class="fas fa-trash"></i> 删除</button>
          </td>
        `;
        taskTableBody.appendChild(row);
      });
    }

    // 初始化模态框
    function initModals() {
      // 打开新增任务单模态框
      document.querySelector('[data-modal-target="addTaskModal"]').addEventListener('click', function() {
        document.getElementById('addTaskModal').classList.add('show');
      });

      // 关闭模态框
      document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', function() {
          this.closest('.modal').classList.remove('show');
        });
      });

      // 点击模态框外部关闭
      window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
          event.target.classList.remove('show');
        }
      });
    }

    // 查阅情况查询
    function searchViewRecords(event) {
      event.preventDefault();

      // 获取查询条件
      const reportName = document.getElementById('viewReportName').value.toLowerCase();
      const reader = document.getElementById('viewReader').value.toLowerCase();
      const startDate = document.getElementById('viewStartDate').value;
      const endDate = document.getElementById('viewEndDate').value;
      const dept = document.getElementById('viewDept').value;

      // 过滤数据
      const filteredData = viewRecords.filter(record => {
        // 检查报告名称
        if (reportName && !record.reportName.toLowerCase().includes(reportName)) {
          return false;
        }

        // 检查查阅人
        if (reader && !record.readerId.toLowerCase().includes(reader) && !record.dept.toLowerCase().includes(reader)) {
          return false;
        }

        // 检查时间范围
        if (startDate && record.readTime.split(' ')[0] < startDate) {
          return false;
        }

        if (endDate && record.readTime.split(' ')[0] > endDate) {
          return false;
        }

        // 检查机构
        if (dept && record.dept !== dept) {
          return false;
        }

        return true;
      });

      // 渲染过滤后的表格
      renderViewTable(filteredData);
    }

    // 重置查阅情况查询表单
    function resetViewForm(event) {
      event.preventDefault();
      document.getElementById('viewSearchForm').reset();
      renderViewTable(viewRecords);
    }

    // 显示查阅情况高级筛选
    function showViewAdvancedSearch() {
      alert('查阅情况高级筛选功能待实现');
    }

    // 点评情况查询
    function searchCommentRecords(event) {
      event.preventDefault();

      // 获取查询条件
      const reportName = document.getElementById('commentReportName').value.toLowerCase();
      const commenter = document.getElementById('commenter').value.toLowerCase();
      const startDate = document.getElementById('commentStartDate').value;
      const endDate = document.getElementById('commentEndDate').value;
      const dept = document.getElementById('commentDept').value;
      const overall = document.getElementById('commentOverall').value;

      // 过滤数据
      const filteredData = commentRecords.filter(record => {
        // 检查报告名称
        if (reportName && !record.reportName.toLowerCase().includes(reportName)) {
          return false;
        }

        // 检查点评人
        if (commenter && !record.commenter.toLowerCase().includes(commenter)) {
          return false;
        }

        // 检查时间范围
        if (startDate && record.commentTime.split(' ')[0] < startDate) {
          return false;
        }

        if (endDate && record.commentTime.split(' ')[0] > endDate) {
          return false;
        }

        // 检查机构
        if (dept && record.dept !== dept) {
          return false;
        }

        // 检查整体评价
        if (overall && record.overall !== overall) {
          return false;
        }

        return true;
      });

      // 渲染过滤后的表格
      renderCommentTable(filteredData);
    }

    // 重置点评情况查询表单
    function resetCommentForm(event) {
      event.preventDefault();
      document.getElementById('commentSearchForm').reset();
      renderCommentTable(commentRecords);
    }

    // 显示点评情况高级筛选
    function showCommentAdvancedSearch() {
      alert('点评情况高级筛选功能待实现');
    }

    // 精准度打分查询
    function searchAccuracyRecords(event) {
      event.preventDefault();

      // 获取查询条件
      const reportName = document.getElementById('accuracyReportName').value.toLowerCase();
      const scorer = document.getElementById('scorer').value.toLowerCase();
      const startDate = document.getElementById('accuracyStartDate').value;
      const endDate = document.getElementById('accuracyEndDate').value;
      const dept = document.getElementById('accuracyDept').value;
      const scoreRange = document.getElementById('scoreRange').value;

      // 过滤数据
      const filteredData = accuracyRecords.filter(record => {
        // 检查报告名称
        if (reportName && !record.reportName.toLowerCase().includes(reportName)) {
          return false;
        }

        // 检查打分人
        if (scorer && !record.scorer.toLowerCase().includes(scorer)) {
          return false;
        }

        // 检查时间范围
        if (startDate && record.scoreTime.split(' ')[0] < startDate) {
          return false;
        }

        if (endDate && record.scoreTime.split(' ')[0] > endDate) {
          return false;
        }

        // 检查机构
        if (dept && record.dept !== dept) {
          return false;
        }

        // 检查分值区间
        if (scoreRange) {
          const [min, max] = scoreRange.split('-').map(Number);
          if (record.score < min || record.score > max) {
            return false;
          }
        }

        return true;
      });

      // 渲染过滤后的表格
      renderAccuracyTable(filteredData);
    }

    // 重置精准度打分查询表单
    function resetAccuracyForm(event) {
      event.preventDefault();
      document.getElementById('accuracySearchForm').reset();
      renderAccuracyTable(accuracyRecords);
    }

    // 显示精准度打分高级筛选
    function showAccuracyAdvancedSearch() {
      alert('精准度打分高级筛选功能待实现');
    }

    // 优化任务单查询
    function searchTaskRecords(event) {
      event.preventDefault();

      // 获取查询条件
      const taskName = document.getElementById('taskName').value.toLowerCase();
      const reportName = document.getElementById('taskReportName').value.toLowerCase();
      const status = document.getElementById('taskStatus').value;
      const creator = document.getElementById('taskCreator').value.toLowerCase();
      const startDate = document.getElementById('taskStartDate').value;
      const endDate = document.getElementById('taskEndDate').value;

      // 过滤数据
      const filteredData = taskRecords.filter(record => {
        // 检查任务名称
        if (taskName && !record.name.toLowerCase().includes(taskName)) {
          return false;
        }

        // 检查报告名称
        if (reportName && !record.reportName.toLowerCase().includes(reportName)) {
          return false;
        }

        // 检查任务状态
        if (status && record.status !== status) {
          return false;
        }

        // 检查创建人
        if (creator && !record.creator.toLowerCase().includes(creator)) {
          return false;
        }

        // 检查时间范围
        if (startDate && record.createTime.split(' ')[0] < startDate) {
          return false;
        }

        if (endDate && record.createTime.split(' ')[0] > endDate) {
          return false;
        }

        return true;
      });

      // 渲染过滤后的表格
      renderTaskTable(filteredData);
    }

    // 重置优化任务单查询表单
    function resetTaskForm(event) {
      event.preventDefault();
      document.getElementById('taskSearchForm').reset();
      renderTaskTable(taskRecords);
    }

    // 显示优化任务单高级筛选
    function showTaskAdvancedSearch() {
      alert('优化任务单高级筛选功能待实现');
    }
  </script>
  <!-- 在原有模态框之后添加查看任务详情模态框 -->
<div class="modal" id="viewTaskModal">
  <div class="modal-content">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-eye"></i> 查看优化任务单详情</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <div class="form-group">
        <label>任务名称</label>
        <div id="viewTaskName" style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;"></div>
      </div>
      <div class="form-group">
        <label>运营报告名称</label>
        <div id="viewTaskReportName" style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;"></div>
      </div>
      <div class="form-group">
        <label>优化内容</label>
        <div id="viewTaskContent" style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9; min-height: 80px;"></div>
      </div>
      <div class="form-group">
        <label>任务状态</label>
        <div id="viewTaskStatus" style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;"></div>
      </div>
      <div class="form-group">
        <label>创建时间</label>
        <div id="viewTaskCreateTime" style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;"></div>
      </div>
      <div class="form-group">
        <label>创建人</label>
        <div id="viewTaskCreator" style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;"></div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-primary" onclick="document.getElementById('viewTaskModal').classList.remove('show')">关闭</button>
    </div>
  </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal" id="deleteTaskModal">
  <div class="modal-content" style="width: 400px;">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-exclamation-triangle"></i> 确认删除</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <p>您确定要删除此优化任务单吗？此操作不可撤销。</p>
      <input type="hidden" id="deleteTaskId">
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('deleteTaskModal').classList.remove('show')">取消</button>
      <button class="btn" style="background-color: #ff4d4f; color: white; border: none;" onclick="confirmDelete()">确认删除</button>
    </div>
  </div>
</div>

<script>
// 原有初始化代码之后添加以下功能代码
document.addEventListener('DOMContentLoaded', function() {
  // 模拟任务数据
  const taskData = [
    {
      id: '1',
      name: '优化用户增长分析指标',
      reportName: '用户增长分析报告',
      content: '增加用户留存率分析维度，补充周环比数据',
      status: 'pending',
      createTime: '2023-06-10',
      creator: '张三'
    },
    {
      id: '2',
      name: '完善销售数据可视化',
      reportName: '产品销售周报',
      content: '增加地区销售分布热力图，优化TOP10产品展示',
      status: 'processing',
      createTime: '2023-06-12',
      creator: '李四'
    },
    {
      id: '3',
      name: '补充活动ROI分析',
      reportName: '活动效果评估报告',
      content: '添加投入产出比计算模块，完善活动成本分析',
      status: 'completed',
      createTime: '2023-06-08',
      creator: '王五'
    }
  ];

  // 渲染任务表格
  renderTaskTable();

  // 渲染任务表格函数
  function renderTaskTable() {
    const tableBody = document.getElementById('taskTableBody');
    tableBody.innerHTML = '';
    
    taskData.forEach(task => {
      const row = document.createElement('tr');
      // 状态标签样式
      let statusLabel = '';
      switch(task.status) {
        case 'pending':
          statusLabel = '<span style="color: #faad14; background: rgba(250, 173, 20, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px;">待处理</span>';
          break;
        case 'processing':
          statusLabel = '<span style="color: #1890ff; background: rgba(24, 144, 255, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px;">处理中</span>';
          break;
        case 'completed':
          statusLabel = '<span style="color: #52c41a; background: rgba(82, 196, 26, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px;">已完成</span>';
          break;
        case 'canceled':
          statusLabel = '<span style="color: #ff4d4f; background: rgba(255, 77, 79, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px;">已取消</span>';
          break;
      }
      
      row.innerHTML = `
        <td>${task.name}</td>
        <td>${task.reportName}</td>
        <td>${task.content}</td>
        <td>${statusLabel}</td>
        <td>${task.createTime}</td>
        <td>${task.creator}</td>
        <td>
          <button class="btn btn-sm" onclick="viewTask('${task.id}')"><i class="fas fa-eye"></i> 查看</button>
          <button class="btn btn-sm" onclick="editTask('${task.id}')"><i class="fas fa-edit"></i> 编辑</button>
          <button class="btn btn-sm" style="background-color: #ff4d4f; color: white; border: none;" onclick="showDeleteConfirm('${task.id}')"><i class="fas fa-trash"></i> 删除</button>
        </td>
      `;
      tableBody.appendChild(row);
    });
  }

  // 查看任务详情
  window.viewTask = function(id) {
    const task = taskData.find(item => item.id === id);
    if (task) {
      document.getElementById('viewTaskName').textContent = task.name;
      document.getElementById('viewTaskReportName').textContent = task.reportName;
      document.getElementById('viewTaskContent').textContent = task.content;
      
      // 状态文本转换
      let statusText = '';
      switch(task.status) {
        case 'pending': statusText = '待处理'; break;
        case 'processing': statusText = '处理中'; break;
        case 'completed': statusText = '已完成'; break;
        case 'canceled': statusText = '已取消'; break;
      }
      document.getElementById('viewTaskStatus').textContent = statusText;
      document.getElementById('viewTaskCreateTime').textContent = task.createTime;
      document.getElementById('viewTaskCreator').textContent = task.creator;
      
      document.getElementById('viewTaskModal').classList.add('show');
    }
  }

  // 编辑任务
  window.editTask = function(id) {
    const task = taskData.find(item => item.id === id);
    if (task) {
      document.getElementById('editTaskId').value = task.id;
      document.getElementById('editTaskName').value = task.name;
      document.getElementById('editTaskReportName').value = task.reportName;
      document.getElementById('editTaskContent').value = task.content;
      document.getElementById('editTaskStatus').value = task.status;
      
      document.getElementById('editTaskModal').classList.add('show');
    }
  }

  // 显示删除确认
  window.showDeleteConfirm = function(id) {
    document.getElementById('deleteTaskId').value = id;
    document.getElementById('deleteTaskModal').classList.add('show');
  }

  // 确认删除
  window.confirmDelete = function() {
    const id = document.getElementById('deleteTaskId').value;
    const index = taskData.findIndex(item => item.id === id);
    if (index !== -1) {
      taskData.splice(index, 1);
      renderTaskTable();
      document.getElementById('deleteTaskModal').classList.remove('show');
      // 这里可以添加删除成功的提示
    }
  }

  // 保存编辑
  document.querySelector('#editTaskForm + .modal-footer .btn-primary').addEventListener('click', function() {
    // 添加表单验证
    const taskName = document.getElementById('editTaskName').value;
    const reportName = document.getElementById('editTaskReportName').value;
    const content = document.getElementById('editTaskContent').value;

    if (!taskName.trim()) {
      alert('请输入任务名称');
      return;
    }
    if (!reportName.trim()) {
      alert('请输入报告名称');
      return;
    }
    if (!content.trim()) {
      alert('请输入优化内容');
      return;
    }

    const id = document.getElementById('editTaskId').value;
    const task = taskData.find(item => item.id === id);
    if (task) {
      task.name = taskName;
      task.reportName = reportName;
      task.content = content;
      task.status = document.getElementById('editTaskStatus').value;
      task.updateTime = new Date().toISOString().split('T')[0]; // 添加更新时间

      renderTaskTable();
      document.getElementById('editTaskModal').classList.remove('show');
      // 添加保存成功提示
      showNotification('任务编辑成功', 'success');
    } else {
      showNotification('未找到该任务', 'error');
    }
  });

  // 通知提示函数
  function showNotification(message, type) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'notification ' + type;
    notification.innerText = message;

    // 设置样式
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.padding = '12px 24px';
    notification.style.borderRadius = '4px';
    notification.style.color = 'white';
    notification.style.fontWeight = 'bold';
    notification.style.zIndex = '1000';
    notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
    notification.style.transition = 'all 0.3s ease';
    notification.style.opacity = '0';
    notification.style.transform = 'translateY(-20px)';

    // 根据类型设置背景色
    if (type === 'success') {
      notification.style.backgroundColor = '#52c41a';
    } else if (type === 'error') {
      notification.style.backgroundColor = '#ff4d4f';
    } else if (type === 'warning') {
      notification.style.backgroundColor = '#faad14';
    } else {
      notification.style.backgroundColor = '#1890ff';
    }

    // 添加到文档
    document.body.appendChild(notification);

    // 显示通知
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateY(0)';
    }, 10);

    // 3秒后隐藏通知
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateY(-20px)';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  // 新增任务
  document.querySelector('#addTaskForm + .modal-footer .btn-primary').addEventListener('click', function() {
    const newTask = {
      id: Date.now().toString(), // 简单生成唯一ID
      name: document.getElementById('newTaskName').value,
      reportName: document.getElementById('newTaskReportName').value,
      content: document.getElementById('newTaskContent').value,
      status: 'pending',
      createTime: new Date().toISOString().split('T')[0],
      creator: '管理员' // 实际应用中应该从登录信息获取
    };
    
    taskData.push(newTask);
    renderTaskTable();
    
    // 重置表单并关闭模态框
    document.getElementById('addTaskForm').reset();
    document.getElementById('addTaskModal').classList.remove('show');
    // 这里可以添加创建成功的提示
  });

  // 关闭模态框的通用处理
  document.querySelectorAll('.modal-close').forEach(btn => {
    btn.addEventListener('click', function() {
      this.closest('.modal').classList.remove('show');
    });
  });
});
</script>
<!-- 在现有模态框后添加图表解读模态框 -->
<div class="modal" id="taskAnalysisModal">
  <div class="modal-content" style="width: 80%; max-width: 1000px;">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-chart-line"></i> 图表解读与报告分析</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <div style="margin-bottom: 20px;">
        <h3 style="margin-bottom: 10px;">报告关键指标分析</h3>
        <div class="chart-container" style="height: 300px; margin-bottom: 20px;">
          <canvas id="reportAnalysisChart"></canvas>
        </div>
      </div>
      
      <div>
        <h3 style="margin-bottom: 10px;">图表解读</h3>
        <div class="analysis-content" style="background-color: #f5f5f5; padding: 15px; border-radius: 4px;">
          <p>1. 从趋势图可以看出，该报告涉及的核心指标在过去6个月呈现稳步上升趋势，环比增长平均保持在8%-12%之间。</p>
          <p>2. 数据波动主要集中在每月第三周，推测与周末效应及月度结算周期有关。</p>
          <p>3. 与行业平均水平相比，本报告指标超出约15%，显示运营效果良好。</p>
          <p>4. 建议重点关注Q3季度的增长拐点，分析其背后的驱动因素。</p>
        </div>
      </div>
      
      <div style="margin-top: 20px;">
        <h3 style="margin-bottom: 10px;">优化建议</h3>
        <div class="suggestions-content" style="background-color: #f0f7ff; padding: 15px; border-radius: 4px; border-left: 4px solid #3498db;">
          <p>1. 基于数据分析，建议调整资源分配，向高增长区域倾斜约20%的资源。</p>
          <p>2. 针对数据波动周期，可提前制定应对策略，平滑每周数据曲线。</p>
          <p>3. 参考优秀指标表现，将成功经验复制到其他业务板块。</p>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('taskAnalysisModal').classList.remove('show')">关闭</button>
    </div>
  </div>
</div>

<script>
// 在原有JavaScript代码后添加以下内容
document.addEventListener('DOMContentLoaded', function() {
  // 初始化任务表格数据（模拟数据）
  initTaskTable();
  
  // 为模态框关闭按钮添加事件
  document.querySelectorAll('.modal-close').forEach(button => {
    button.addEventListener('click', function() {
      this.closest('.modal').classList.remove('show');
    });
  });
});

// 初始化任务表格数据
function initTaskTable() {
  const taskTableBody = document.getElementById('taskTableBody');
  // 模拟任务数据
  const tasks = [
    {
      name: "用户留存率优化",
      reportName: "用户增长分析报告",
      content: "提高新用户7日留存率至45%以上",
      status: "processing",
      createTime: "2023-06-15",
      creator: "张三"
    },
    {
      name: "转化率提升方案",
      reportName: "产品销售周报",
      content: "优化购买流程，提升转化率15%",
      status: "pending",
      createTime: "2023-06-18",
      creator: "李四"
    },
    {
      name: "活动效果优化",
      reportName: "活动效果评估报告",
      content: "分析活动数据，优化活动策略",
      status: "completed",
      createTime: "2023-06-10",
      creator: "王五"
    }
  ];
  
  // 清空表格
  taskTableBody.innerHTML = '';
  
  // 填充表格数据
  tasks.forEach((task, index) => {
    const row = document.createElement('tr');
    
    // 状态样式映射
    const statusMap = {
      'pending': '<span style="color: #f39c12;">待处理</span>',
      'processing': '<span style="color: #3498db;">处理中</span>',
      'completed': '<span style="color: #2ecc71;">已完成</span>',
      'canceled': '<span style="color: #e74c3c;">已取消</span>'
    };
    
    row.innerHTML = `
      <td>${task.name}</td>
      <td>${task.reportName}</td>
      <td>${task.content}</td>
      <td>${statusMap[task.status]}</td>
      <td>${task.createTime}</td>
      <td>${task.creator}</td>
      <td>
        <button class="btn btn-sm" onclick="editTask(${index})"><i class="fas fa-edit"></i> 编辑</button>
        <button class="btn btn-sm" style="margin-left: 5px;" onclick="viewTaskAnalysis('${task.reportName}')"><i class="fas fa-eye"></i> 查看</button>
      </td>
    `;
    
    taskTableBody.appendChild(row);
  });
}

// 查看任务分析
function viewTaskAnalysis(reportName) {
  // 初始化分析图表
  initAnalysisChart(reportName);
  
  // 显示分析模态框
  document.getElementById('taskAnalysisModal').classList.add('show');
}

// 初始化分析图表
function initAnalysisChart(reportName) {
  const ctx = document.getElementById('reportAnalysisChart').getContext('2d');
  
  // 根据不同报告显示不同图表数据
  let chartData, chartTitle;
  
  switch(reportName) {
    case "用户增长分析报告":
      chartTitle = "用户增长趋势分析";
      chartData = {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '新增用户',
          data: [1200, 1900, 1700, 2100, 2400, 2800],
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          tension: 0.3,
          fill: true
        }, {
          label: '活跃用户',
          data: [3200, 3900, 4200, 4700, 5100, 5800],
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.1)',
          tension: 0.3,
          fill: true
        }]
      };
      break;
      
    case "产品销售周报":
      chartTitle = "产品销售数据分析";
      chartData = {
        labels: ['第1周', '第2周', '第3周', '第4周'],
        datasets: [{
          label: '销售额(万元)',
          data: [120, 150, 130, 180],
          backgroundColor: 'rgba(255, 159, 64, 0.7)',
        }]
      };
      break;
      
    default:
      chartTitle = "活动效果分析";
      chartData = {
        labels: ['曝光', '点击', '参与', '转化'],
        datasets: [{
          label: '活动数据',
          data: [15000, 8000, 3500, 1200],
          backgroundColor: [
            'rgba(153, 102, 255, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(255, 159, 64, 0.7)'
          ]
        }]
      };
  }
  
  // 销毁已存在的图表
  if (window.reportChart) {
    window.reportChart.destroy();
  }
  
  // 创建新图表
  window.reportChart = new Chart(ctx, {
    type: reportName === "产品销售周报" ? 'bar' : 
           reportName === "活动效果评估报告" ? 'pie' : 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: chartTitle,
          font: {
            size: 16
          }
        }
      },
      scales: reportName !== "活动效果评估报告" ? {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '数量'
          }
        }
      } : {}
    }
  });
}
</script>
<div class="modal" id="viewDetailModal">
  <div class="modal-content" style="width: 80%; max-width: 800px;">
    <div class="modal-header">
      <div class="modal-title" id="viewDetailTitle"><i class="fas fa-eye"></i> 查看详情</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body" id="viewDetailContent">
      <!-- 详情内容将通过JavaScript动态加载 -->
    </div>
    <div class="modal-footer">
      <button class="btn btn-primary" onclick="document.getElementById('viewDetailModal').classList.remove('show')">关闭</button>
    </div>
  </div>
</div>

// ... existing code ...
<script>
// ... existing code ...

// 查看查阅记录详情
window.viewViewRecord = function(recordId) {
  const record = viewRecords.find(item => item.recordId === recordId);
  if (record) {
    document.getElementById('viewDetailTitle').innerHTML = '<i class="fas fa-eye"></i> 查阅记录详情';
    document.getElementById('viewDetailContent').innerHTML = `
      <div class="form-group">
        <label>记录ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.recordId}</div>
      </div>
      <div class="form-group">
        <label>报告名称</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.reportName}</div>
      </div>
      <div class="form-group">
        <label>报告实例ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.reportInstanceId}</div>
      </div>
      <div class="form-group">
        <label>查阅时间</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.readTime}</div>
      </div>
      <div class="form-group">
        <label>查阅人ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.readerId}</div>
      </div>
      <div class="form-group">
        <label>部门</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.dept}</div>
      </div>
      <div class="form-group">
        <label>阅读时长(分钟)</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.duration}</div>
      </div>
    `;
    document.getElementById('viewDetailModal').classList.add('show');
  }
}

// 查看点评记录详情
window.viewCommentRecord = function(commentInstanceId) {
  const record = commentRecords.find(item => item.commentInstanceId === commentInstanceId);
  if (record) {
    document.getElementById('viewDetailTitle').innerHTML = '<i class="fas fa-eye"></i> 点评记录详情';
    document.getElementById('viewDetailContent').innerHTML = `
      <div class="form-group">
        <label>报告名称</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.reportName}</div>
      </div>
      <div class="form-group">
        <label>报告实例ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.reportInstanceId}</div>
      </div>
      <div class="form-group">
        <label>点评实例ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.commentInstanceId}</div>
      </div>
      <div class="form-group">
        <label>点评人</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.commenter}</div>
      </div>
      <div class="form-group">
        <label>点评时间</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.commentTime}</div>
      </div>
      <div class="form-group">
        <label>部门</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.dept}</div>
      </div>
      <div class="form-group">
        <label>点评内容</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9; min-height: 80px;">${record.content}</div>
      </div>
      <div class="form-group">
        <label>总体评价</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.overall}</div>
      </div>
    `;
    document.getElementById('viewDetailModal').classList.add('show');
  }
}

// 查看精准度打分记录详情
window.viewAccuracyRecord = function(scoreInstanceId) {
  const record = accuracyRecords.find(item => item.scoreInstanceId === scoreInstanceId);
  if (record) {
    document.getElementById('viewDetailTitle').innerHTML = '<i class="fas fa-eye"></i> 精准度打分详情';
    document.getElementById('viewDetailContent').innerHTML = `
      <div class="form-group">
        <label>报告名称</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.reportName}</div>
      </div>
      <div class="form-group">
        <label>报告实例ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.reportInstanceId}</div>
      </div>
      <div class="form-group">
        <label>打分实例ID</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.scoreInstanceId}</div>
      </div>
      <div class="form-group">
        <label>打分人</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.scorer}</div>
      </div>
      <div class="form-group">
        <label>打分时间</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.scoreTime}</div>
      </div>
      <div class="form-group">
        <label>部门</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.dept}</div>
      </div>
      <div class="form-group">
        <label>精准度得分</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">${record.score}</div>
      </div>
      <div class="form-group">
        <label>得分等级</label>
        <div style="padding: 6px 12px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9;">
          ${record.score >= 85 ? '优秀' : record.score >= 70 ? '良好' : record.score >= 60 ? '合格' : '不合格'}
        </div>
      </div>
    `;
    document.getElementById('viewDetailModal').classList.add('show');
  }
}

// 修改渲染查阅记录表格函数，添加查看按钮事件
function renderViewTable(data) {
  const viewTableBody = document.getElementById('viewTableBody');
  viewTableBody.innerHTML = '';

  if (data.length === 0) {
    const emptyRow = document.createElement('tr');
    emptyRow.innerHTML = `<td colspan="8" style="text-align: center;">暂无数据</td>`;
    viewTableBody.appendChild(emptyRow);
    return;
  }

  data.forEach(record => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${record.recordId}</td>
      <td>${record.reportName}</td>
      <td>${record.reportInstanceId}</td>
      <td>${record.readTime}</td>
      <td>${record.readerId}</td>
      <td>${record.dept}</td>
      <td>${record.duration}</td>
      <td>
        <button class="btn btn-sm" onclick="viewViewRecord('${record.recordId}')"><i class="fas fa-eye"></i> 查看</button>
      </td>
    `;
    viewTableBody.appendChild(row);
  });
}

// 修改渲染点评记录表格函数，添加查看按钮事件
function renderCommentTable(data) {
  const commentTableBody = document.getElementById('commentTableBody');
  commentTableBody.innerHTML = '';

  if (data.length === 0) {
    const emptyRow = document.createElement('tr');
    emptyRow.innerHTML = `<td colspan="9" style="text-align: center;">暂无数据</td>`;
    commentTableBody.appendChild(emptyRow);
    return;
  }

  data.forEach(record => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${record.reportName}</td>
      <td>${record.reportInstanceId}</td>
      <td>${record.commentInstanceId}</td>
      <td>${record.commenter}</td>
      <td>${record.commentTime}</td>
      <td>${record.dept}</td>
      <td>${record.content}</td>
      <td>${record.overall}</td>
      <td>
        <button class="btn btn-sm" onclick="viewCommentRecord('${record.commentInstanceId}')"></i> 查看</button>
      </td>
    `;
    commentTableBody.appendChild(row);
  });
}

// 修改渲染精准度打分记录表格函数，添加查看按钮事件
function renderAccuracyTable(data) {
  const accuracyTableBody = document.getElementById('accuracyTableBody');
  accuracyTableBody.innerHTML = '';

  if (data.length === 0) {
    const emptyRow = document.createElement('tr');
    emptyRow.innerHTML = `<td colspan="8" style="text-align: center;">暂无数据</td>`;
    accuracyTableBody.appendChild(emptyRow);
    return;
  }

  data.forEach(record => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${record.reportName}</td>
      <td>${record.reportInstanceId}</td>
      <td>${record.scoreInstanceId}</td>
      <td>${record.scorer}</td>
      <td>${record.scoreTime}</td>
      <td>${record.dept}</td>
      <td>${record.score}</td>
      <td>
        <button class="btn btn-sm" onclick="viewAccuracyRecord('${record.scoreInstanceId}')"><i class="fas fa-eye"></i> 查看</button>
      </td>
    `;
    accuracyTableBody.appendChild(row);
  });
}

// ... existing code ...
</script>

</body>
</html>