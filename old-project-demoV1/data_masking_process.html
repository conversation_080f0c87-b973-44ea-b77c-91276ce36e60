<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 数据脱密处理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      .tab-content {
        display: none;
        padding: 20px 0;
      }
      .tab-content.active {
        display: block;
      }
      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 20px;
      }
      .tab {
        padding: 10px 20px;
        cursor: pointer;
        border-bottom: 3px solid transparent;
      }
      .tab.active {
        border-bottom-color: var(--primary-color);
        color: var(--primary-color);
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">任务完成通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">任务#10086已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">任务告警</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">任务#10087执行失败</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child active" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">管理⻚⾯穿透权限</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-shield-alt page-title-icon"></i>
        数据脱密处理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">五级穿透调度</a></div>
        <div class="breadcrumb-item"><a href="task_scheduling.html" style="text-decoration: none; color: inherit">任务调度</a></div>
        <div class="breadcrumb-item active">数据脱密处理</div>
      </div>

      <!-- 功能标签页 -->
      <div class="tabs">
        <div class="tab active" data-tab="rule">配置脱密规则</div>
        <div class="tab" data-tab="task">执行脱密任务</div>
      </div>

      <!-- 配置脱密规则 -->
      <div class="tab-content active" id="rule">
        <div id="ruleModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增脱密规则配置</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('ruleModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="maskingRuleForm">
                <div class="form-group">
                  <label for="ruleId">
                    规则ID
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <input type="text" id="ruleId" name="ruleId" required placeholder="请输入规则ID" />
                  <button type="button" class="btn btn-info" style="margin-left: 10px" onclick="queryRuleVersion()">查询版本</button>
                </div>

                <div id="versionInfo" style="display: none; margin: 15px 0; padding: 10px; background-color: var(--bg-secondary); border-radius: 4px">
                  <h4>版本号详情</h4>
                  <div class="form-group">
                    <label>当前版本号</label>
                    <input type="text" id="currentVersion" readonly style="background-color: #f5f5f5" />
                  </div>
                  <div class="form-group">
                    <label>创建时间</label>
                    <input type="text" id="createTime" readonly style="background-color: #f5f5f5" />
                  </div>
                  <div class="form-group">
                    <label>创建人</label>
                    <input type="text" id="creator" readonly style="background-color: #f5f5f5" />
                  </div>
                </div>

                <div class="form-group">
                  <label for="ruleName">
                    规则名称
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <input type="text" id="ruleName" name="ruleName" required placeholder="请输入规则名称" />
                </div>

                <div class="form-group">
                  <label for="ruleDescription">规则描述</label>
                  <textarea id="ruleDescription" name="ruleDescription" rows="3" placeholder="请输入规则描述"></textarea>
                </div>

                <div class="form-group">
                  <label for="maskingType">
                    脱密类型
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <select id="maskingType" name="maskingType" required>
                    <option value="">请选择脱密类型</option>
                    <option value="replace">替换</option>
                    <option value="encrypt">加密</option>
                    <option value="hash">哈希</option>
                    <option value="mask">掩码</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="maskingPattern">
                    脱密模式
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <input type="text" id="maskingPattern" name="maskingPattern" required placeholder="请输入脱密模式，如：**** **** **** 1234" />
                </div>

                <div class="form-group">
                  <label for="maskingFields">
                    脱密字段
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <input type="text" id="maskingFields" name="maskingFields" required placeholder="请输入脱密字段，多个字段用逗号分隔" />
                </div>

                <div class="form-group">
                  <label for="dataSource">
                    数据源
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <select id="dataSource" name="dataSource" required>
                    <option value="">请选择数据源</option>
                    <option value="mysql">MySQL</option>
                    <option value="oracle">Oracle</option>
                    <option value="postgresql">PostgreSQL</option>
                    <option value="mongodb">MongoDB</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="status">
                    状态
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <select id="status" name="status" required>
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                  </select>
                </div>

                <div style="display: flex; justify-content: flex-end; margin-top: 20px">
                  <button type="button" class="btn" style="margin-right: 10px" onclick="resetRuleForm()">重置</button>
                  <button type="submit" class="btn btn-primary">保存配置</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">脱密规则列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addRule" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增脱密规则
                </button>
              </div>
            </div>
          </div>
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>规则ID</th>
                  <th>规则名称</th>
                  <th>脱密类型</th>
                  <th>数据源</th>
                  <th>版本号</th>
                  <th>创建时间</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#MR001</td>
                  <td>手机号脱密规则</td>
                  <td>掩码</td>
                  <td>MySQL</td>
                  <td>V1.0</td>
                  <td>2023-07-10 15:30</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>#MR002</td>
                  <td>身份证号脱密规则</td>
                  <td>哈希</td>
                  <td>Oracle</td>
                  <td>V1.1</td>
                  <td>2023-07-12 09:15</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 执行脱密任务 -->
      <div class="tab-content" id="task">
        <div id="taskModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3)">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center">
              <h3 style="margin: 0; font-size: 18px; font-weight: 500">新增脱密任务执行</h3>
              <span class="close-modal" style="cursor: pointer; font-size: 24px" onClick="closeModal('taskModal')">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px">
              <form id="maskingTaskForm">
                <div class="form-group">
                  <label for="taskId">
                    任务ID
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <input type="text" id="taskId" name="taskId" required placeholder="请输入任务ID" />
                  <button type="button" class="btn btn-info" style="margin-left: 10px" onclick="queryTaskInfo()">查询任务</button>
                </div>

                <div id="taskInfo" style="display: none; margin: 15px 0; padding: 10px; background-color: var(--bg-secondary); border-radius: 4px">
                  <h4>任务信息</h4>
                  <div class="form-group">
                    <label>任务名称</label>
                    <input type="text" id="taskName" readonly style="background-color: #f5f5f5" />
                  </div>
                  <div class="form-group">
                    <label>任务描述</label>
                    <textarea id="taskDescription" rows="3" readonly style="background-color: #f5f5f5"></textarea>
                  </div>
                </div>

                <div id="statusInfo" style="display: none; margin: 15px 0; padding: 10px; background-color: var(--bg-secondary); border-radius: 4px">
                  <h4>处理状态详情</h4>
                  <div class="form-group">
                    <label>当前状态</label>
                    <input type="text" id="currentStatus" readonly style="background-color: #f5f5f5" />
                  </div>
                  <div class="form-group">
                    <label>开始时间</label>
                    <input type="text" id="startTime" readonly style="background-color: #f5f5f5" />
                  </div>
                  <div class="form-group">
                    <label>结束时间</label>
                    <input type="text" id="endTime" readonly style="background-color: #f5f5f5" />
                  </div>
                  <div class="form-group">
                    <label>处理进度</label>
                    <div class="progress-bar"><div id="progressBar" class="progress" style="width: 0%; background-color: var(--primary-color)"></div></div>
                    <span id="progressText">0%</span>
                  </div>
                </div>

                <div class="form-group">
                  <label for="ruleIdSelect">
                    脱密规则
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <select id="ruleIdSelect" name="ruleIdSelect" required>
                    <option value="">请选择脱密规则</option>
                    <option value="MR001">手机号脱密规则 (#MR001)</option>
                    <option value="MR002">身份证号脱密规则 (#MR002)</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="dataRange">
                    数据范围
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <select id="dataRange" name="dataRange" required>
                    <option value="">请选择数据范围</option>
                    <option value="all">全部数据</option>
                    <option value="today">今日数据</option>
                    <option value="yesterday">昨日数据</option>
                    <option value="week">本周数据</option>
                    <option value="month">本月数据</option>
                    <option value="custom">自定义范围</option>
                  </select>
                </div>

                <div id="customDateRange" style="display: none; margin-top: 15px">
                  <div class="form-group">
                    <label for="startDate">开始日期</label>
                    <input type="date" id="startDate" name="startDate" />
                  </div>
                  <div class="form-group">
                    <label for="endDate">结束日期</label>
                    <input type="date" id="endDate" name="endDate" />
                  </div>
                </div>

                <div class="form-group">
                  <label for="priority">
                    优先级
                    <span style="color: var(--danger-color)">*</span>
                  </label>
                  <select id="priority" name="priority" required>
                    <option value="">请选择优先级</option>
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="remark">备注</label>
                  <textarea id="remark" name="remark" rows="3" placeholder="请输入备注信息"></textarea>
                </div>

                <div style="display: flex; justify-content: flex-end; margin-top: 20px">
                  <button type="button" class="btn" style="margin-right: 10px" onclick="resetTaskForm()">重置</button>
                  <button type="submit" class="btn btn-primary">执行脱密任务</button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="font-weight: 500; font-size: 16px">脱密任务列表</div>
              <div style="display: flex; gap: 10px">
                <div class="search-box" style="width: 250px">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索留痕..." />
                </div>
                <button id="addTask" class="btn btn-primary" style="height: 40px">
                  <i class="fas fa-plus"></i>
                  新增脱密任务
                </button>
              </div>
            </div>
          </div>
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>任务ID</th>
                  <th>任务名称</th>
                  <th>脱密规则</th>
                  <th>优先级</th>
                  <th>状态</th>
                  <th>开始时间</th>
                  <th>结束时间</th>
                  <th>进度</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>#MT001</td>
                  <td>用户数据脱密处理</td>
                  <td>手机号脱密规则</td>
                  <td><span class="tag tag-warning">中</span></td>
                  <td><span class="tag tag-success">已完成</span></td>
                  <td>2023-07-10 16:00</td>
                  <td>2023-07-10 16:30</td>
                  <td>
                    <div class="progress-bar"><div class="progress" style="width: 100%; background-color: var(--success-color)"></div></div>
                    100%
                  </td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--danger-color)"><i class="fas fa-stop"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>#MT002</td>
                  <td>客户信息脱密处理</td>
                  <td>身份证号脱密规则</td>
                  <td><span class="tag tag-danger">高</span></td>
                  <td><span class="tag tag-info">处理中</span></td>
                  <td>2023-07-12 10:00</td>
                  <td>-</td>
                  <td>
                    <div class="progress-bar"><div class="progress" style="width: 60%; background-color: var(--primary-color)"></div></div>
                    60%
                  </td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--danger-color)"><i class="fas fa-stop"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // 初始化页面组件
      document.addEventListener('DOMContentLoaded', function () {
        // 初始化下拉菜单
        // initDropdown();

        // 初始化标签页切换
        initTabs();

        // 监听数据范围选择变化
        document.getElementById('dataRange').addEventListener('change', function () {
          const customDateRange = document.getElementById('customDateRange');
          if (this.value === 'custom') {
            customDateRange.style.display = 'block';
          } else {
            customDateRange.style.display = 'none';
          }
        });

        const ruleModal = document.getElementById('ruleModal');
        const addRule = document.getElementById('addRule');
        addRule.addEventListener('click', function () {
          ruleModal.style.display = 'flex';
        });

        const taskModal = document.getElementById('taskModal');
        const addTask = document.getElementById('addTask');
        addTask.addEventListener('click', function () {
          taskModal.style.display = 'flex';
        });
      });

      // 初始化标签页切换
      function initTabs() {
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
          tab.addEventListener('click', function () {
            // 移除所有标签页的激活状态
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前标签页的激活状态
            this.classList.add('active');

            // 隐藏所有内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 显示当前内容
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
          });
        });
      }

      // 查询规则版本号详情
      function queryRuleVersion() {
        const ruleId = document.getElementById('ruleId').value;
        if (!ruleId) {
          alert('请输入规则ID');
          return;
        }

        // 模拟从数据库查询规则版本号详情
        setTimeout(function () {
          const versionInfo = document.getElementById('versionInfo');
          versionInfo.style.display = 'block';

          // 模拟数据
          document.getElementById('currentVersion').value = 'V1.2';
          document.getElementById('createTime').value = '2023-07-15 10:30';
          document.getElementById('creator').value = '管理员';

          alert('规则版本号详情查询成功');
        }, 500);
      }

      // 重置规则表单
      function resetRuleForm() {
        document.getElementById('maskingRuleForm').reset();
        document.getElementById('versionInfo').style.display = 'none';
      }

      // 规则表单提交处理
      document.getElementById('maskingRuleForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 表单验证
        const ruleId = document.getElementById('ruleId').value;
        const ruleName = document.getElementById('ruleName').value;
        const maskingType = document.getElementById('maskingType').value;
        const maskingPattern = document.getElementById('maskingPattern').value;
        const maskingFields = document.getElementById('maskingFields').value;
        const dataSource = document.getElementById('dataSource').value;

        if (!ruleId || !ruleName || !maskingType || !maskingPattern || !maskingFields || !dataSource) {
          alert('请填写所有必填字段');
          return;
        }

        // 模拟将配置脱密规则明细存至关系型数据库
        setTimeout(function () {
          alert('配置脱密规则保存成功');
          resetRuleForm();
          closeModal('ruleModal');
        }, 500);
      });

      // 查询任务信息
      function queryTaskInfo() {
        const taskId = document.getElementById('taskId').value;
        if (!taskId) {
          alert('请输入任务ID');
          return;
        }

        // 模拟从数据库查询任务信息
        setTimeout(function () {
          // 显示任务信息
          const taskInfo = document.getElementById('taskInfo');
          taskInfo.style.display = 'block';

          // 模拟任务数据
          document.getElementById('taskName').value = '用户数据脱敏处理';
          document.getElementById('taskDescription').value = '对系统中的用户敏感数据进行脱密处理，包括手机号、身份证号等信息。';

          // 显示处理状态详情
          const statusInfo = document.getElementById('statusInfo');
          statusInfo.style.display = 'block';

          // 模拟状态数据
          document.getElementById('currentStatus').value = '待处理';
          document.getElementById('startTime').value = '';
          document.getElementById('endTime').value = '';
          document.getElementById('progressBar').style.width = '0%';
          document.getElementById('progressText').textContent = '0%';

          alert('任务信息查询成功');
        }, 500);
      }

      // 重置任务表单
      function resetTaskForm() {
        document.getElementById('maskingTaskForm').reset();
        document.getElementById('taskInfo').style.display = 'none';
        document.getElementById('statusInfo').style.display = 'none';
        document.getElementById('customDateRange').style.display = 'none';
      }

      // 任务表单提交处理
      document.getElementById('maskingTaskForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 表单验证
        const taskId = document.getElementById('taskId').value;
        const ruleIdSelect = document.getElementById('ruleIdSelect').value;
        const dataRange = document.getElementById('dataRange').value;
        const priority = document.getElementById('priority').value;

        if (!taskId || !ruleIdSelect || !dataRange || !priority) {
          alert('请填写所有必填字段');
          return;
        }

        if (dataRange === 'custom') {
          const startDate = document.getElementById('startDate').value;
          const endDate = document.getElementById('endDate').value;
          if (!startDate || !endDate) {
            alert('请选择自定义日期范围');
            return;
          }
        }

        // 模拟将执行脱密任务明细存至关系型数据库
        setTimeout(function () {
          // 更新状态信息
          document.getElementById('currentStatus').value = '处理中';
          document.getElementById('startTime').value = new Date().toLocaleString();
          document.getElementById('progressBar').style.width = '30%';
          document.getElementById('progressText').textContent = '30%';

          alert('脱密任务已成功提交，正在执行中...');
          closeModal('taskModal');
          // 模拟进度更新
          simulateProgressUpdate();
        }, 500);
      });

      function closeModal(id) {
        const modal = document.getElementById(id);
        modal.style.display = 'none';
      }

      // 模拟进度更新
      function simulateProgressUpdate() {
        let progress = 30;
        const interval = setInterval(function () {
          progress += Math.floor(Math.random() * 10);
          if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            document.getElementById('currentStatus').value = '已完成';
            document.getElementById('endTime').value = new Date().toLocaleString();
            alert('脱密任务执行完成');
          }
          document.getElementById('progressBar').style.width = progress + '%';
          document.getElementById('progressText').textContent = progress + '%';
        }, 1000);
      }
    </script>
  </body>
</html>
