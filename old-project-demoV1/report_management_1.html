<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 运营报告管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;" data-href="report_management.html">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin_management.html">运营通报管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-generation.html">运营通报生成与审核</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->

   <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
  <!-- 主内容区 -->
  <div class="main-content">
    <!-- 页面切换选项卡已移除 -->

    <!-- 运营报告管理内容 -->
    <div id="reportManagementContent">
    <div class="page-title">
      <i class="fas fa-file-alt page-title-icon"></i>
      运营报告管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营报告管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex; width: 50%;">
        <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" placeholder="搜索报告...">
        </div>
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部模板</option>
            <option value="daily">日报模板</option>
            <option value="weekly">周报模板</option>
            <option value="monthly">月报模板</option>
          </select>
        </div>
        <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="draft">草稿</option>
            <option value="generated">已生成</option>
            <option value="published">已发布</option>
          </select>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-filter"></i> 高级筛选</button>
<button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;" onclick="window.location.href='report_management_neirong2.html'"><i class="fas fa-file-alt"></i> 内容生成</button>
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;" id="dynamicReportBtn"><i class="fas fa-bolt"></i> 动态报告</button>
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;" onclick="window.location.href='report_management_moban.html'"><i class="fas fa-file-alt"></i> 配置管理</button>
          <button class="btn btn-primary" data-modal-target="addReportModal"><i class="fas fa-plus"></i> 新增报告</button>
      </div>
    </div>

    <!-- 报告列表表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>报告名称</th>
              <th>模板名称</th>
              <th>生成周期</th>
              <th>数据范围</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>生成时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>用户增长分析报告</td>
              <td>用户分析模板</td>
              <td>日报</td>
              <td>2023-07-15</td>
              <td><span class="tag tag-success">已发布</span></td>
              <td>2023-07-14 16:30</td>
              <td>2023-07-15 08:00</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>产品销售周报</td>
              <td>销售分析模板</td>
              <td>周报</td>
              <td>2023-07-10至2023-07-16</td>
              <td><span class="tag tag-info">已生成</span></td>
              <td>2023-07-16 09:15</td>
              <td>2023-07-16 10:00</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>6月运营数据分析</td>
              <td>月度分析模板</td>
              <td>月报</td>
              <td>2023-06-01至2023-06-30</td>
              <td><span class="tag tag-warning">草稿</span></td>
              <td>2023-07-01 14:45</td>
              <td>-</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>活动效果评估报告</td>
              <td>活动分析模板</td>
              <td>一次性</td>
              <td>2023-07-01至2023-07-10</td>
              <td><span class="tag tag-success">已发布</span></td>
              <td>2023-07-11 09:20</td>
              <td>2023-07-11 11:30</td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item">4</div>
        <div class="pagination-item">5</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增报告模态框 -->
  <div class="modal" id="addReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addReportForm">
          <div class="form-group">
            <label for="reportName">报告名称</label>
            <input type="text" id="reportName" name="reportName" required placeholder="请输入报告名称">
          </div>
          <div class="form-group">
            <label for="templateName">模板名称</label>
            <select id="templateName" name="templateName" required>
              <option value="">请选择模板</option>
              <option value="user_analysis">用户分析模板</option>
              <option value="sales_analysis">销售分析模板</option>
              <option value="monthly_analysis">月度分析模板</option>
              <option value="activity_analysis">活动分析模板</option>
            </select>
          </div>
          <div class="form-group">
            <label for="generateCycle">生成周期</label>
            <select id="generateCycle" name="generateCycle" required>
              <option value="">请选择生成周期</option>
              <option value="daily">日报</option>
              <option value="weekly">周报</option>
              <option value="monthly">月报</option>
              <option value="once">一次性</option>
            </select>
          </div>
          <div class="form-group">
            <label for="dataRange">数据范围</label>
            <div style="display: flex; gap: 12px;">
              <input type="date" id="startDate" name="startDate" required>
              <input type="date" id="endDate" name="endDate" required>
            </div>
          </div>
          <div class="form-group">
            <label for="reportContent">报告内容配置</label>
            <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; height: 120px;">
              <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                <label style="display: flex; align-items: center;"><input type="checkbox" checked> 用户增长分析</label>
                <label style="display: flex; align-items: center;"><input type="checkbox" checked> 销售业绩分析</label>
                <label style="display: flex; align-items: center;"><input type="checkbox"> 流量来源分析</label>
              </div>
              <div style="display: flex; gap: 12px;">
                <label style="display: flex; align-items: center;"><input type="checkbox" checked> 转化率分析</label>
                <label style="display: flex; align-items: center;"><input type="checkbox"> 竞品分析</label>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="reportDescription">报告描述</label>
            <textarea id="reportDescription" name="reportDescription" rows="3" placeholder="请输入报告描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addReportForm').submit()">生成报告</button>
      </div>
    </div>
  </div>

  <!-- 编辑报告模态框 -->
  <div class="modal" id="editReportModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑运营报告</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑运营报告表单内容与新增表单类似，实际应用中会加载报告的当前配置。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editReportModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>

  <!-- 选项卡样式已移除 -->
  <script src="js/common.js"></script>
  <script>
    // 新增报告表单提交
    document.getElementById('addReportForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addReportForm')) {
        // 模拟提交成功
        alert('运营报告已成功创建，正在生成中！');
        document.getElementById('addReportModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });
  </script>
</body>
</html>