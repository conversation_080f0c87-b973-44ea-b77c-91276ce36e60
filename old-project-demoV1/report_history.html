<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>报表历史记录 - 数智化运营平台</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
      }
      .tab {
        padding: 14px 24px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: var(--transition);
        color: var(--text-secondary);
      }
      .tab.active {
        border-bottom-color: var(--primary-color);
        color: var(--primary-color);
      }
      .tab:hover:not(.active) {
        background-color: var(--bg-color);
        color: var(--text-primary);
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: bold">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: bold">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: bold">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
           <div class="menu-item child " data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child active" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-history page-title-icon"></i>
        报表历史记录
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">自定义报表</a></div>
        <div class="breadcrumb-item active">报表历史记录</div>
      </div>

      <!-- 标签页切换 -->
      <div class="tabs">
        <div class="tab active" data-tab-target="#conditionHistory">条件历史记录</div>
        <div class="tab" data-tab-target="#reportHistory">报表历史记录</div>
      </div>

      <!-- 条件历史记录 -->
      <div id="conditionHistory" class="tab-content active">
        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div>
                <i class="fas fa-filter"></i>
                条件历史记录
              </div>
              <div style="display: flex; gap: 10px">
                <div style="position: relative">
                  <input type="text" placeholder="搜索条件名称..." style="padding: 6px 12px; padding-left: 32px; border-radius: 4px; border: 1px solid var(--border-color)" />
                  <i class="fas fa-search" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: var(--text-tertiary)"></i>
                </div>
                <button class="btn btn-sm btn-primary">
                  <i class="fas fa-download"></i>
                  批量导出
                </button>
              </div>
            </div>
          </div>
          <div class="card-body"  style="margin-top:10px">
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 5%"><input type="checkbox" /></th>
                  <th style="width: 20%">条件名称</th>
                  <th style="width: 15%">创建时间</th>
                  <th style="width: 10%">创建人</th>
                  <th style="width: 25%">条件摘要</th>
                  <th style="width: 15%">使用次数</th>
                  <th style="width: 10%">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>2023年Q3销售数据</td>
                  <td>2023-09-01 14:30</td>
                  <td>管理员</td>
                  <td>时间:2023-07至2023-09, 区域:全国, 行业:零售</td>
                  <td>12次</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs"><i class="fas fa-copy"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-trash"></i></button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>金融行业月度分析</td>
                  <td>2023-08-15 09:45</td>
                  <td>张三</td>
                  <td>时间:2023-08, 区域:华东区, 行业:金融</td>
                  <td>8次</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs"><i class="fas fa-copy"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-trash"></i></button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>全国制造业季度报表</td>
                  <td>2023-07-20 16:20</td>
                  <td>李四</td>
                  <td>时间:2023-Q2, 区域:全国, 行业:制造业</td>
                  <td>5次</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs"><i class="fas fa-copy"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-trash"></i></button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>华北区用户增长分析</td>
                  <td>2023-06-10 11:15</td>
                  <td>王五</td>
                  <td>时间:2023-01至2023-06, 区域:华北区, 指标:用户数</td>
                  <td>3次</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs"><i class="fas fa-copy"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-trash"></i></button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>西南区销售额分析</td>
                  <td>2023-05-28 15:40</td>
                  <td>赵六</td>
                  <td>时间:近6个月, 区域:西南区, 指标:销售额</td>
                  <td>6次</td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs"><i class="fas fa-copy"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-edit"></i></button>
                      <button class="btn btn-xs"><i class="fas fa-trash"></i></button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px">
              <div style="color: var(--text-tertiary)">共12条记录，显示1-5条</div>
              <div class="pagination">
                <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 报表历史记录 -->
      <div id="reportHistory" class="tab-content">
        <div class="card">
          <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div>
                <i class="fas fa-file-alt"></i>
                报表历史记录
              </div>
              <div style="display: flex; gap: 10px">
                <div style="display: flex; gap: 10px">
                  <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                    <option value="all">全部状态</option>
                    <option value="normal">正常</option>
                    <option value="error">生成失败</option>
                  </select>
                </div>
                <button class="btn btn-sm btn-primary">
                  <i class="fas fa-download"></i>
                  批量导出
                </button>
              </div>
            </div>
          </div>
          <div class="card-body" style="margin-top:10px">
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 5%"><input type="checkbox" /></th>
                  <th style="width: 20%">报表名称</th>
                  <th style="width: 15%">生成时间</th>
                  <th style="width: 10%">创建人</th>
                  <th style="width: 10%">文件大小</th>
                  <th style="width: 15%">状态</th>
                  <th style="width: 25%">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>2023年Q3销售数据报表</td>
                  <td>2023-09-05 09:20</td>
                  <td>管理员</td>
                  <td>2.4MB</td>
                  <td><span style="color: var(--success-color); background: rgba(72, 187, 120, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px">正常</span></td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-download"></i>
                        导出
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-history"></i>
                        重新生成
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>金融行业月度分析报表</td>
                  <td>2023-08-20 14:35</td>
                  <td>张三</td>
                  <td>1.8MB</td>
                  <td><span style="color: var(--success-color); background: rgba(72, 187, 120, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px">正常</span></td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-download"></i>
                        导出
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-history"></i>
                        重新生成
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>制造业季度数据报表</td>
                  <td>2023-07-25 11:50</td>
                  <td>李四</td>
                  <td>3.2MB</td>
                  <td><span style="color: var(--danger-color); background: rgba(237, 100, 166, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px">生成失败</span></td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs" disabled>
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-xs" disabled>
                        <i class="fas fa-download"></i>
                        导出
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-history"></i>
                        重新生成
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>华北区用户增长报表</td>
                  <td>2023-06-15 16:25</td>
                  <td>王五</td>
                  <td>1.5MB</td>
                  <td><span style="color: var(--success-color); background: rgba(72, 187, 120, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px">正常</span></td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-download"></i>
                        导出
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-history"></i>
                        重新生成
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td><input type="checkbox" /></td>
                  <td>西南区销售额分析报表</td>
                  <td>2023-05-30 10:10</td>
                  <td>赵六</td>
                  <td>2.1MB</td>
                  <td><span style="color: var(--success-color); background: rgba(72, 187, 120, 0.1); padding: 2px 8px; border-radius: 12px; font-size: 12px">正常</span></td>
                  <td>
                    <div style="display: flex; gap: 5px">
                      <button class="btn btn-xs">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-download"></i>
                        导出
                      </button>
                      <button class="btn btn-xs">
                        <i class="fas fa-history"></i>
                        重新生成
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px">
              <div style="color: var(--text-tertiary)">共23条记录，显示1-5条</div>
              <div class="pagination">
                <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn">4</button>
                <button class="pagination-btn">5</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="js/common.js"></script>
    <script>
      // 标签页切换
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', function () {
          const target = this.getAttribute('data-tab-target');
          const tabContents = document.querySelectorAll('.tab-content');
          const tabs = document.querySelectorAll('.tab');

          // 更新标签状态
          tabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');

          // 更新内容区域
          tabContents.forEach(content => {
            content.classList.remove('active');
            content.style.display = 'none';
          });

          const activeContent = document.querySelector(target);
          activeContent.classList.add('active');
          activeContent.style.display = 'block';
        });
      });

      // 初始化第一个标签内容
      document.querySelector('.tab-content.active').style.display = 'block';

      // 删除确认
      document.querySelectorAll('.fa-trash').forEach(icon => {
        icon.parentElement.addEventListener('click', function () {
          if (confirm('确定要删除该记录吗？删除后无法恢复。')) {
            // 模拟删除操作
            this.closest('tr').style.opacity = '0.5';
            setTimeout(() => {
              this.closest('tr').remove();
              alert('删除成功！');
            }, 500);
          }
        });
      });

      // 批量导出
      document.querySelector('.btn-primary').addEventListener('click', function () {
        // 模拟导出操作
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
        this.disabled = true;

        setTimeout(() => {
          this.innerHTML = '<i class="fas fa-download"></i> 批量导出';
          this.disabled = false;
          alert('导出成功！已将选中的记录导出为Excel文件。');
        }, 1500);
      });
    </script>
  </body>
</html>
