<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 模板编辑</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#165DFF',
              secondary: '#4080FF',
              success: '#00B42A',
              warning: '#FF7D00',
              danger: '#F53F3F',
              info: '#86909C',
              light: '#F2F3F5',
              dark: '#1D2129',
            },
            fontFamily: {
              inter: ['Inter', 'sans-serif'],
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer utilities {
        .content-auto {
          content-visibility: auto;
        }
        .menu-active {
          background-color: rgba(24, 144, 255, 0.1);
          color: var(--primary-color);
          border-left: 4px solid var(--primary-color);
        }
        .btn-primary {
          background-color: var(--primary-color);
          color: white;
          transition: all 200ms;
        }
        .btn-primary:hover {
          background-color: rgba(24, 144, 255, 0.9);
        }
        .btn-secondary {
          background-color: white;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
          transition: all 200ms;
        }
        .btn-secondary:hover {
          background-color: rgba(24, 144, 255, 0.05);
        }
        .panel {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid #f3f4f6;
        }
        .panel-header {
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .panel-body {
          padding: 1rem;
        }
        .diff-add {
          background-color: #ecfdf5;
          color: #065f46;
          border-left: 2px solid #10b981;
          padding: 0.25rem 0.75rem;
        }
        .diff-remove {
          background-color: #fef2f2;
          color: #b91c1c;
          border-left: 2px solid #ef4444;
          padding: 0.25rem 0.75rem;
        }
      }
    </style>
  </head>
  <body class="bg-gray-50 font-inter text-dark">
    <!-- 导航栏 -->
 <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
    <div class="flex overflow-hidden"  style="margin-top: 64px;">
      <!-- 侧边栏 -->
      <!-- 主内容区 -->
      <div class="flex-1 overflow-y-auto p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-500 mb-6">
          <a href="index.html" class="hover:text-primary transition-colors duration-200">首页</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <a href="operation_views.html" class="hover:text-primary transition-colors duration-200">运营视图</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <a href="#" class="hover:text-primary transition-colors duration-200">模板管理</a>
          <i class="fas fa-chevron-right mx-2 text-xs"></i>
          <span class="text-primary">模板编辑</span>
        </div>

        <!-- 页面标题 -->
        <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold text-gray-800 mb-6">模板编辑</h1>

        <!-- 当前编辑模板信息 -->
        <div class="panel mb-6">
          <div class="panel-header">
            <div class="flex items-center">
              <h2 class="text-sm font-medium text-gray-700 mr-2">正在编辑：</h2>
              <span class="text-primary font-medium">全国运营监控大屏</span>
              <span class="ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded">v1.2.0</span>
            </div>
            <div class="flex items-center gap-3">
              <button class="text-sm text-gray-500 hover:text-primary transition-colors duration-200 flex items-center gap-1">
                <i class="fas fa-history"></i>
                <span>版本历史</span>
              </button>
              <button class="text-sm text-gray-500 hover:text-primary transition-colors duration-200 flex items-center gap-1">
                <i class="fas fa-file-code"></i>
                <span>另存为新模板</span>
              </button>
            </div>
          </div>
          <div class="panel-body flex flex-wrap gap-4 items-center">
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500">模板类型:</span>
              <span class="text-xs font-medium text-gray-700">全国模板</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500">行业分类:</span>
              <span class="text-xs font-medium text-gray-700">通用</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500">上次修改时间:</span>
              <span class="text-xs font-medium text-gray-700">2023-05-15 16:32:45</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500">修改人:</span>
              <span class="text-xs font-medium text-gray-700">管理员</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500">状态:</span>
              <span class="text-xs px-1.5 py-0.5 bg-success/10 text-success rounded">已发布</span>
            </div>
          </div>
        </div>

        <!-- 工具栏 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-6 flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center gap-3">
            <button class="px-4 py-2 rounded-lg btn-primary flex items-center gap-2" onclick="createTemplate('template_List.html')">
              <i class="fas fa-save"></i>
              <span>保存修改</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-eye"></i>
              <span>预览</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-code"></i>
              <span>代码模式</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-undo"></i>
              <span>撤销</span>
            </button>
            <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
              <i class="fas fa-redo"></i>
              <span>重做</span>
            </button>
          </div>
          <div class="flex items-center gap-3">
            <div class="relative inline-flex rounded-md shadow-sm" role="group">
              <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-l-lg hover:bg-gray-50 focus:z-10 focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200">直接编辑</button>
              <button type="button" class="px-4 py-2 text-sm font-medium text-primary bg-primary/5 border-t border-b border-gray-200 hover:bg-primary/10 focus:z-10 focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200">批量替换</button>
              <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-r-lg hover:bg-gray-50 focus:z-10 focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200">条件编辑</button>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 左侧：模板搜索和选择 -->
          <div class="lg:col-span-1 space-y-6">
            <!-- 模板搜索 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">模板搜索</h2>
              </div>
              <div class="panel-body space-y-4">
                <div class="relative">
                  <input type="text" placeholder="搜索模板名称或关键词" class="pl-9 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full" />
                  <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">模板类型</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                    <option>全部模板类型</option>
                    <option>全国模板</option>
                    <option>分省模板</option>
                    <option>市级模板</option>
                    <option>区县级模板</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">行业分类</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                    <option>全部行业</option>
                    <option>通用</option>
                    <option>零售行业</option>
                    <option>金融行业</option>
                    <option>制造业</option>
                    <option>服务业</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">模板状态</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                    <option>全部状态</option>
                    <option>草稿</option>
                    <option>已发布</option>
                    <option>已禁用</option>
                  </select>
                </div>
                <div class="flex gap-2">
                  <button class="flex-1 px-4 py-2 rounded-lg btn-primary flex items-center justify-center gap-2 text-sm">
                    <i class="fas fa-search"></i>
                    <span>搜索</span>
                  </button>
                  <button class="px-4 py-2 rounded-lg border border-gray-200 text-gray-700 hover:bg-gray-50 transition-all duration-200 text-sm">重置</button>
                </div>
              </div>
            </div>

            <!-- 最近使用的模板 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">最近使用</h2>
              </div>
              <div class="panel-body">
                <ul class="space-y-2">
                  <li class="p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer border-l-2 border-primary bg-primary/5">
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-primary">全国运营监控大屏</span>
                      <span class="text-xs text-gray-500">v1.2.0</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-0.5">上次编辑: 今天 16:32</div>
                  </li>
                  <li class="p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer">
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-700">省份运营分析大屏</span>
                      <span class="text-xs text-gray-500">v1.1.0</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-0.5">上次编辑: 昨天 14:15</div>
                  </li>
                  <li class="p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer">
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium text-gray-700">城市运营监控大屏</span>
                      <span class="text-xs text-gray-500">v1.0.0</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-0.5">上次编辑: 3天前</div>
                  </li>
                </ul>
              </div>
            </div>

            <!-- 加载进度 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">加载进度</h2>
              </div>
              <div class="panel-body space-y-3">
                <div>
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-xs text-gray-500">模板内容加载</span>
                    <span class="text-xs font-medium text-gray-700">100%</span>
                  </div>
                  <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div class="absolute top-0 left-0 h-full bg-success rounded-full" style="width: 100%"></div>
                  </div>
                </div>
                <div>
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-xs text-gray-500">差异对比分析</span>
                    <span class="text-xs font-medium text-gray-700">100%</span>
                  </div>
                  <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div class="absolute top-0 left-0 h-full bg-success rounded-full" style="width: 100%"></div>
                  </div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500 mt-2">
                  <span>总耗时: 2.5秒</span>
                  <span>完成于: 刚刚</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间：编辑器区域 -->
          <div class="lg:col-span-2 space-y-6">
            <!-- 编辑器工具栏 -->
            <div class="panel">
              <div class="panel-header border-b-0 p-2 bg-gray-50 flex flex-wrap gap-1">
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="段落样式"><i class="fas fa-paragraph"></i></button>
                <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                  <option>正文</option>
                  <option>标题 1</option>
                  <option>标题 2</option>
                  <option>标题 3</option>
                </select>
                <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                  <option>默认字体</option>
                  <option>Arial</option>
                  <option>宋体</option>
                  <option>黑体</option>
                </select>
                <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                  <option>12px</option>
                  <option>14px</option>
                  <option>16px</option>
                  <option>18px</option>
                  <option>20px</option>
                </select>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="粗体"><i class="fas fa-bold"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="斜体"><i class="fas fa-italic"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="下划线"><i class="fas fa-underline"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="删除线"><i class="fas fa-strikethrough"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="左对齐"><i class="fas fa-align-left"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="居中对齐"><i class="fas fa-align-center"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="右对齐"><i class="fas fa-align-right"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="两端对齐"><i class="fas fa-align-justify"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="无序列表"><i class="fas fa-list-ul"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="有序列表"><i class="fas fa-list-ol"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="减少缩进"><i class="fas fa-outdent"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="增加缩进"><i class="fas fa-indent"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入链接"><i class="fas fa-link"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入图片"><i class="fas fa-image"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入表格"><i class="fas fa-table"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="插入代码"><i class="fas fa-code"></i></button>
                <div class="w-px h-6 bg-gray-200 mx-1"></div>
                <button class="p-2 text-primary bg-primary/10 rounded transition-colors duration-200" title="查找替换"><i class="fas fa-search-plus"></i></button>
                <button class="p-2 text-gray-600 hover:bg-gray-100 rounded transition-colors duration-200" title="格式刷"><i class="fas fa-paint-brush"></i></button>
              </div>
              <!-- 编辑器内容区 -->
              <div class="panel-body p-0 min-h-[400px] bg-gray-50 border border-gray-200 rounded-b-lg overflow-hidden">
                <div class="p-4 h-full min-h-[400px]" contenteditable="true" spellcheck="false" class="focus:outline-none prose max-w-none">
                  <h2>全国运营监控大屏</h2>
                  <p>本模板用于展示全国范围内的运营数据监控，包含以下几个主要部分：</p>
                  <ul>
                    <li>实时销售数据概览</li>
                    <li>区域分布热力图</li>
                    <li>用户增长趋势分析</li>
                    <li>热门产品排行榜</li>
                    <li>客户满意度指标</li>
                  </ul>
                  <p>您可以通过拖拽右侧组件库中的元素到画布中来自定义模板内容。</p>
                  <div class="diff-add">
                    <p>新增功能：支持多维度数据对比分析和实时预警功能，可以根据设定的阈值自动触发警报。</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 组件库 -->
            <div class="panel">
              <div class="panel-header">
                <h2 class="text-sm font-medium text-gray-700">组件库</h2>
                <div class="flex items-center gap-2">
                  <input type="text" placeholder="搜索组件" class="px-3 py-1 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200 text-sm w-32" />
                  <button class="p-1 text-gray-500 hover:text-primary"><i class="fas fa-filter"></i></button>
                </div>
              </div>
              <div class="panel-body">
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                  <!-- 图表组件 -->
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-bar text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">柱状图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-line text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">折线图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-pie text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">饼图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-area text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">面积图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-map-marked-alt text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">地图</span>
                  </div>
                  <!-- 数据组件 -->
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-table text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">数据表格</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-tags text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">标签云</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-percentage text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">进度环</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-random text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">关系图</span>
                  </div>
                  <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:border-primary hover:bg-primary/5 transition-all duration-200 cursor-move flex flex-col items-center text-center">
                    <i class="fas fa-chart-pie text-primary text-xl mb-2"></i>
                    <span class="text-xs font-medium text-gray-700">漏斗图</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部：版本对比 -->
        <div class="panel mt-6">
          <div class="panel-header">
            <div class="flex items-center gap-3">
              <h2 class="text-sm font-medium text-gray-700">版本对比</h2>
              <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                <option>v1.2.0 (当前)</option>
                <option>v1.1.0</option>
                <option>v1.0.0</option>
              </select>
              <span class="text-gray-500">vs</span>
              <select class="px-2 py-1 text-sm border border-gray-200 rounded bg-white focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                <option>v1.1.0</option>
                <option>v1.0.0</option>
              </select>
            </div>
            <div class="flex items-center gap-2">
              <button class="text-xs text-gray-500 hover:text-primary transition-colors duration-200 px-2 py-1 border border-gray-200 rounded hover:bg-gray-50">
                <i class="fas fa-copy mr-1"></i>
                复制差异
              </button>
              <button class="text-xs text-gray-500 hover:text-primary transition-colors duration-200 px-2 py-1 border border-gray-200 rounded hover:bg-gray-50">
                <i class="fas fa-download mr-1"></i>
                导出报告
              </button>
            </div>
          </div>
          <div class="panel-body max-h-[300px] overflow-y-auto">
            <div class="space-y-3 border-l-2 border-gray-200 pl-4">
              <div class="relative">
                <div class="absolute -left-[41px] w-4 h-4 rounded-full bg-success border-2 border-white"></div>
                <h3 class="text-sm font-medium text-gray-800 mb-2">新增内容</h3>
                <div class="diff-add mb-2">
                  <p>新增功能：支持多维度数据对比分析和实时预警功能，可以根据设定的阈值自动触发警报。</p>
                </div>
                <div class="text-xs text-gray-500">添加于 2023-05-15 16:32:45 由 管理员</div>
              </div>
              <div class="relative">
                <div class="absolute -left-[41px] w-4 h-4 rounded-full bg-danger border-2 border-white"></div>
                <h3 class="text-sm font-medium text-gray-800 mb-2">删除内容</h3>
                <div class="diff-remove mb-2">
                  <p>移除了旧版的数据导入模块，该功能已迁移至数据融通模块。</p>
                </div>
                <div class="text-xs text-gray-500">删除于 2023-05-15 16:28:12 由 管理员</div>
              </div>
              <div class="relative">
                <div class="absolute -left-[41px] w-4 h-4 rounded-full bg-primary border-2 border-white"></div>
                <h3 class="text-sm font-medium text-gray-800 mb-2">修改内容</h3>
                <div class="diff-remove mb-1">
                  <p>区域分布热力图展示全国各省份的销售数据分布情况。</p>
                </div>
                <div class="diff-add mb-2">
                  <p>区域分布热力图展示全国各省份、城市的销售数据分布情况，并支持下钻查看详细数据。</p>
                </div>
                <div class="text-xs text-gray-500">修改于 2023-05-15 16:25:30 由 管理员</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览模态框 -->
    <div id="previewModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">模板预览</h3>
          <button id="closePreview" class="text-gray-400 hover:text-gray-500 transition-colors duration-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="flex-1 overflow-y-auto p-6">
          <div class="bg-gray-50 p-8 rounded-lg min-h-[400px]">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">全国运营监控大屏</h2>
            <p class="text-gray-600 mb-4">本模板用于展示全国范围内的运营数据监控，包含以下几个主要部分：</p>
            <ul class="list-disc pl-5 mb-4 space-y-1 text-gray-600">
              <li>实时销售数据概览</li>
              <li>区域分布热力图</li>
              <li>用户增长趋势分析</li>
              <li>热门产品排行榜</li>
              <li>客户满意度指标</li>
            </ul>
            <p class="text-gray-600 mb-4">您可以通过拖拽右侧组件库中的元素到画布中来自定义模板内容。</p>
            <p class="text-green-700 mb-4 bg-green-50 p-3 rounded-lg border-l-2 border-green-500">新增功能：支持多维度数据对比分析和实时预警功能，可以根据设定的阈值自动触发警报。</p>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <h3 class="text-lg font-medium text-gray-800 mb-2">销售数据概览</h3>
              <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-bar text-gray-300 text-5xl"></i>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <h3 class="text-lg font-medium text-gray-800 mb-2">区域分布热力图</h3>
              <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-map-marked-alt text-gray-300 text-5xl"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button id="cancelPreview" class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">取消</button>
          <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">发布更新</button>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      function createTemplate(href) {
        window.location.href = href;
      }
      // 预览模态框交互
      document.querySelector('button:has(.fa-eye)').addEventListener('click', function () {
        document.getElementById('previewModal').classList.remove('hidden');
        document.getElementById('previewModal').classList.add('flex');
      });

      document.getElementById('closePreview').addEventListener('click', function () {
        document.getElementById('previewModal').classList.add('hidden');
        document.getElementById('previewModal').classList.remove('flex');
      });

      document.getElementById('cancelPreview').addEventListener('click', function () {
        document.getElementById('previewModal').classList.add('hidden');
        document.getElementById('previewModal').classList.remove('flex');
      });

      // 模拟编辑器保存状态
      setInterval(function () {
        const saveButton = document.querySelector('button:has(.fa-save)');
        saveButton.innerHTML = '<i class="fas fa-save"></i><span>已保存</span>';
        saveButton.classList.add('bg-success');
        saveButton.classList.remove('bg-primary');
        setTimeout(function () {
          saveButton.innerHTML = '<i class="fas fa-save"></i><span>保存修改</span>';
          saveButton.classList.remove('bg-success');
          saveButton.classList.add('bg-primary');
        }, 2000);
      }, 30000);
    </script>
  </body>
</html>
