<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 离线采集任务管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">数据源管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">离线采集任务管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">实时采集任务管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
 <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

       <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="data-group">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child  active" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html"> <div class="menu-item child " data-href="alarm_notification.html">告警通知</div></div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
           <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">管理⻚⾯穿透权限</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-clock page-title-icon"></i>
      离线采集任务管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">数据融通</a></div>
      <div class="breadcrumb-item active">离线采集任务管理</div>
    </div>

    <!-- 功能模块入口 -->
    <!-- <div class="card" style="margin-bottom: 20px;">
      <div class="card-header">
        <div class="card-title">功能模块</div>
      </div>
      <div class="card-body">
        <div style="display: flex; flex-wrap: wrap; gap: 12px;">
       
        </div>
      </div>
    </div> -->

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: flex-start; align-items: center; margin-bottom: 20px;gap:10px">
       <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);height:40px">
            <option value="all">全部状态</option>
            <option value="running">运行中</option>
            <option value="stopped">已停止</option>
            <option value="completed">已完成</option>
            <option value="failed">已失败</option>
            <option value="waiting">等待执行</option>
          </select>
        </div>
      <div class="search-box" style="width: 300px; margin-bottom: 0;">
        <i class="fas fa-search search-box-icon"></i>
        <input type="text" placeholder="搜索任务..." onkeypress="handleSearchKeyPress(event)">
      </div>
      <button class="btn btn-primary" onclick="searchTasks()" style="margin-left: 8px;">
        <i class="fas fa-search"></i> 查询
      </button>
      <div style="display: flex;margin-left:auto">
        <button class="btn btn-primary" style="margin-right: 8px;" data-modal-target="addOfflineTaskModal"><i class="fas fa-plus"></i> 新增任务</button>
        <button class="btn btn-primary" style="margin-right: 8px;" data-modal-target="batchCollectionModal"><i class="fas fa-database"></i> 批量数据离线采集</button>
        <button class="btn btn-primary" style="margin-right: 8px;" data-modal-target="dataCleaningModal"><i class="fas fa-filter"></i> 文件数据清洗与预处理</button>
        <button class="btn btn-primary" style="margin-right: 8px;" data-modal-target="dataSecurityModal"><i class="fas fa-shield-alt"></i> 文件数据安全管理</button>
        <button class="btn btn-primary" data-modal-target="logAuditModal"><i class="fas fa-clipboard-list"></i> 批量采集日志审计与统计</button>
      </div>
    </div>

    <!-- 任务列表表格 -->
   <!-- 修改任务列表表格部分 -->
<div class="card">
  <div class="table-container">
    <table class="table">
      <thead>
        <tr>
          <th>任务名称</th>
          <th>数据源</th>
          <th>目标表</th>
          <th>调度周期</th>
          <th>状态</th>
          <th>创建时间</th>
          <th>最近执行时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>用户数据同步</td>
          <td>MySQL-用户数据库</td>
          <td>dw.user_info</td>
          <td>每天 00:30</td>
          <td><span class="tag tag-success">已上线</span></td>
          <td>2023-07-01 15:30</td>
          <td>2023-07-15 00:30</td>
<td>
  <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
  <button class="btn" style="color: var(--primary-color);" onclick="confirmStatusChange(this, 'running')"><i class="fas fa-play"></i> 运行</button>
  <button class="btn" style="color: var(--warning-color);" onclick="confirmStatusChange(this, 'offline')"><i class="fas fa-power-off"></i> 下线</button>
</td>
        </tr>
        <tr>
          <td>销售数据同步</td>
          <td>Oracle-销售数据库</td>
          <td>dw.sales_info</td>
          <td>每天 01:00</td>
          <td><span class="tag tag-info">运行中</span></td>
          <td>2023-07-02 10:15</td>
          <td>2023-07-15 01:00</td>
          <td>
            <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
  <button class="btn" style="color: var(--danger-color);" onclick="confirmStatusChange(this, 'offline')"><i class="fas fa-power-off"></i> 停止</button>
          </td>
        </tr>
        <tr>
          <td>日志数据导入</td>
          <td>文件-日志文件</td>
          <td>dw.log_info</td>
          <td>每小时</td>
          <td><span class="tag tag-danger">异常</span></td>
          <td>2023-07-03 14:45</td>
          <td>2023-07-15 09:00</td>
          <td>
            <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
  <button class="btn" style="color: var(--primary-color);" onclick="confirmStatusChange(this, 'running')"><i class="fas fa-play"></i> 运行</button>
  <button class="btn" style="color: var(--warning-color);" onclick="confirmStatusChange(this, 'offline')"><i class="fas fa-power-off"></i> 下线</button>
          </td>
        </tr>
        <tr>
          <td>报表数据同步</td>
          <td>FTP-报表文件</td>
          <td>dw.report_info</td>
          <td>每周一 02:00</td>
          <td><span class="tag tag-gray">已下线</span></td>
          <td>2023-07-05 09:20</td>
          <td>2023-07-10 02:00</td>
          <td>
            <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
  <button class="btn" style="color: var(--success-color);" onclick="confirmStatusChange(this, 'completed')"><i class="fas fa-play"></i> 上线</button>
  <button class="btn" style="color: var(--danger-color);" onclick="confirmStatusChange(this, 'offline')"><i class="fas fa-power-off"></i> 删除</button>
          </td>
        </tr>
        <tr>
          <td>商品数据同步</td>
          <td>API-第三方API</td>
          <td>dw.product_info</td>
          <td>每天 03:00</td>
          <td><span class="tag tag-warning">已停止</span></td>
          <td>2023-07-08 16:05</td>
          <td>2023-07-15 03:00</td>
<td>
  <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
  <button class="btn" style="color: var(--primary-color);" onclick="confirmStatusChange(this, 'running')"><i class="fas fa-play"></i> 运行</button>
  <button class="btn" style="color: var(--warning-color);" onclick="confirmStatusChange(this, 'offline')"><i class="fas fa-power-off"></i> 下线</button>
</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="pagination">
    <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
    <div class="pagination-item active">1</div>
    <div class="pagination-item">2</div>
    <div class="pagination-item">3</div>
    <div class="pagination-item">4</div>
    <div class="pagination-item">5</div>
    <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
  </div>
</div>

  <!-- 新增离线任务模态框 -->
  <div class="modal" id="addOfflineTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增离线采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addOfflineTaskForm">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" name="taskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="dataSource">数据源</label>
            <select id="dataSource" name="dataSource" required>
              <option value="">请选择数据源</option>
              <option value="mysql_user">MySQL-用户数据库</option>
              <option value="oracle_sales">Oracle-销售数据库</option>
              <option value="file_log">文件-日志文件</option>
              <option value="ftp_report">FTP-报表文件</option>
              <option value="api_third">API-第三方API</option>
            </select>
          </div>
          <div class="form-group">
            <label for="targetTable">目标表</label>
            <input type="text" id="targetTable" name="targetTable" required placeholder="请输入目标表名">
          </div>
          <div class="form-group">
            <label for="scheduleType">调度类型</label>
            <select id="scheduleType" name="scheduleType" required onchange="changeScheduleForm(this.value)">
              <option value="">请选择调度类型</option>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="hourly">每小时</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div id="dailySchedule" style="display: none;">
            <div class="form-group">
              <label for="dailyTime">执行时间</label>
              <input type="time" id="dailyTime" name="dailyTime" required>
            </div>
          </div>
          <div id="weeklySchedule" style="display: none;">
            <div class="form-group">
              <label>执行日期</label>
              <div style="display: flex; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="1"> 周一
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="2"> 周二
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="3"> 周三
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="4"> 周四
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="5"> 周五
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="6"> 周六
                </label>
                <label style="display: flex; align-items: center; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="0"> 周日
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="weeklyTime">执行时间</label>
              <input type="time" id="weeklyTime" name="weeklyTime" required>
            </div>
          </div>
          <div id="monthlySchedule" style="display: none;">
            <div class="form-group">
              <label for="monthlyDay">执行日期</label>
              <input type="number" id="monthlyDay" name="monthlyDay" min="1" max="31" required placeholder="请输入日期">
            </div>
            <div class="form-group">
              <label for="monthlyTime">执行时间</label>
              <input type="time" id="monthlyTime" name="monthlyTime" required>
            </div>
          </div>
          <div id="hourlySchedule" style="display: none;">
            <div class="form-group">
              <label for="hourlyInterval">执行间隔</label>
              <input type="number" id="hourlyInterval" name="hourlyInterval" min="1" max="24" required placeholder="请输入间隔小时数">
            </div>
          </div>
          <div id="customSchedule" style="display: none;">
            <div class="form-group">
              <label for="cronExpression">Cron表达式</label>
              <input type="text" id="cronExpression" name="cronExpression" required placeholder="请输入Cron表达式">
              <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">格式: 秒 分 时 日 月 周 年</div>
            </div>
          </div>
          <div class="form-group">
            <label for="dataPreprocess">数据预处理</label>
            <textarea id="dataPreprocess" name="dataPreprocess" rows="3" placeholder="请输入数据预处理SQL或脚本"></textarea>
          </div>
          <div class="form-group">
            <label for="securityLevel">安全级别</label>
            <select id="securityLevel" name="securityLevel">
              <option value="low">低</option>
              <option value="medium" selected>中</option>
              <option value="high">高</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskDescription">任务描述</label>
            <textarea id="taskDescription" name="taskDescription" rows="3" placeholder="请输入任务描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addOfflineTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addOfflineTaskForm').submit()">保存并上线</button>
      </div>
    </div>
  </div>

  <!-- 编辑离线任务模态框 -->
  <div class="modal" id="editOfflineTaskModal">
  <div class="modal-content">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-edit"></i> 编辑离线采集任务</div>
      <button class="modal-close">&times;</button>
    </div>
      <div class="modal-body">
      <form id="editOfflineTaskForm">
        <input type="hidden" id="editTaskId"> <!-- 用于存储任务ID -->
        <div class="form-group">
          <label for="editTaskName">任务名称</label>
          <input type="text" id="editTaskName" name="taskName" required placeholder="请输入任务名称">
        </div>
        <div class="form-group">
          <label for="editDataSource">数据源</label>
          <select id="editDataSource" name="dataSource" required>
            <option value="">请选择数据源</option>
            <option value="mysql_user">MySQL-用户数据库</option>
            <option value="oracle_sales">Oracle-销售数据库</option>
            <option value="file_log">文件-日志文件</option>
            <option value="ftp_report">FTP-报表文件</option>
            <option value="api_third">API-第三方API</option>
          </select>
        </div>
        <div class="form-group">
          <label for="editTargetTable">目标表</label>
          <input type="text" id="editTargetTable" name="targetTable" required placeholder="请输入目标表名">
        </div>
        <div class="form-group">
          <label for="editScheduleType">调度类型</label>
          <select id="editScheduleType" name="scheduleType" required onchange="changeEditScheduleForm(this.value)">
            <option value="">请选择调度类型</option>
            <option value="daily">每天</option>
            <option value="weekly">每周</option>
            <option value="monthly">每月</option>
            <option value="hourly">每小时</option>
            <option value="custom">自定义</option>
          </select>
        </div>
        <div id="editDailySchedule" style="display: none;">
          <div class="form-group">
            <label for="editDailyTime">执行时间</label>
            <input type="time" id="editDailyTime" name="dailyTime" required>
          </div>
        </div>
        <div id="editWeeklySchedule" style="display: none;">
          <div class="form-group">
            <label>执行日期</label>
            <div style="display: flex; flex-wrap: wrap;">
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="1"> 周一
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="2"> 周二
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="3"> 周三
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="4"> 周四
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="5"> 周五
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="6"> 周六
              </label>
              <label style="display: flex; align-items: center; margin-bottom: 8px;">
                <input type="checkbox" name="editWeeklyDays" value="0"> 周日
              </label>
            </div>
          </div>
          <div class="form-group">
            <label for="editWeeklyTime">执行时间</label>
            <input type="time" id="editWeeklyTime" name="weeklyTime" required>
          </div>
        </div>
        <div id="editMonthlySchedule" style="display: none;">
          <div class="form-group">
            <label for="editMonthlyDay">执行日期</label>
            <input type="number" id="editMonthlyDay" name="monthlyDay" min="1" max="31" required placeholder="请输入日期">
          </div>
          <div class="form-group">
            <label for="editMonthlyTime">执行时间</label>
            <input type="time" id="editMonthlyTime" name="monthlyTime" required>
          </div>
        </div>
        <div id="editHourlySchedule" style="display: none;">
          <div class="form-group">
            <label for="editHourlyInterval">执行间隔</label>
            <input type="number" id="editHourlyInterval" name="hourlyInterval" min="1" max="24" required placeholder="请输入间隔小时数">
          </div>
        </div>
        <div id="editCustomSchedule" style="display: none;">
          <div class="form-group">
            <label for="editCronExpression">Cron表达式</label>
            <input type="text" id="editCronExpression" name="cronExpression" required placeholder="请输入Cron表达式">
            <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">格式: 秒 分 时 日 月 周 年</div>
          </div>
        </div>
        <div class="form-group">
          <label for="editDataPreprocess">数据预处理</label>
          <textarea id="editDataPreprocess" name="dataPreprocess" rows="3" placeholder="请输入数据预处理SQL或脚本"></textarea>
        </div>
        <div class="form-group">
          <label for="editSecurityLevel">安全级别</label>
          <select id="editSecurityLevel" name="securityLevel">
            <option value="low">低</option>
            <option value="medium" selected>中</option>
            <option value="high">高</option>
          </select>
        </div>
        <div class="form-group">
          <label for="editTaskDescription">任务描述</label>
          <textarea id="editTaskDescription" name="taskDescription" rows="3" placeholder="请输入任务描述"></textarea>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editOfflineTaskModal').classList.remove('show')">取消</button>
      <button class="btn btn-primary" onclick="saveEditTask()">保存修改</button>
    </div>
  </div>
</div>


 <div class="modal" id="batchCollectionModal">
  <div class="modal-content" style="width: 900px;">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-database"></i> 批量数据离线采集</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <div class="tabs">
        <div class="tab-item active" data-tab-target="dynamicDataSourceTab">配置动态数据源</div>
        <div class="tab-item" data-tab-target="addCollectionTaskTab">新增采集任务</div>
        <div class="tab-item" data-tab-target="fileParsingTab">批量文件格式解析</div>
        <div class="tab-item" data-tab-target="incrementalCollectionTab">增量数据采集</div>
        <div class="tab-item" data-tab-target="paginationControlTab">文件数据分页控制</div>
        <div class="tab-item" data-tab-target="compressFileTab">压缩离线数据文件</div>
        <div class="tab-item" data-tab-target="integrityCheckTab">集采文件完整性校验</div>
        <div class="tab-item" data-tab-target="prioritySchedulingTab">任务优先级调度</div>
        <div class="tab-item" data-tab-target="taskSplitTab">批量任务拆分</div>
        <div class="tab-item" data-tab-target="dependencyManagementTab">任务依赖管理</div>
        <div class="tab-item" data-tab-target="timeoutControlTab">任务超时控制</div>
      </div>

      <!-- 1. 配置动态数据源 -->
      <div class="tab-content active" id="dynamicDataSourceTab">
        <form id="dynamicDataSourceForm">
          <div class="form-group">
            <label for="dataSourceName">数据源名称</label>
            <input type="text" id="dataSourceName" name="dataSourceName" required placeholder="请输入数据源名称">
          </div>
          <div class="form-group">
            <label for="dataSourceType">数据源类型</label>
            <select id="dataSourceType" name="dataSourceType" required>
              <option value="mysql">MySQL</option>
              <option value="oracle">Oracle</option>
              <option value="postgresql">PostgreSQL</option>
              <option value="mongodb">MongoDB</option>
              <option value="ftp">FTP</option>
              <option value="s3">Amazon S3</option>
              <option value="api">API</option>
            </select>
          </div>
          <div class="form-group">
            <label for="dataSourceUrl">数据源地址</label>
            <input type="text" id="dataSourceUrl" name="dataSourceUrl" required placeholder="例如：******************************">
          </div>
          <div class="form-group">
            <label for="dataSourceUsername">用户名</label>
            <input type="text" id="dataSourceUsername" name="dataSourceUsername" placeholder="请输入用户名">
          </div>
          <div class="form-group">
            <label for="dataSourcePassword">密码</label>
            <input type="password" id="dataSourcePassword" name="dataSourcePassword" placeholder="请输入密码">
          </div>
          <div class="form-group">
            <label for="dataSourceDescription">描述</label>
            <textarea id="dataSourceDescription" name="dataSourceDescription" rows="3" placeholder="请输入数据源描述"></textarea>
          </div>
        </form>
      </div>

      <!-- 2. 新增采集任务 -->
      <div class="tab-content" id="addCollectionTaskTab">
        <form id="addCollectionTaskForm">
          <div class="form-group">
            <label for="taskId">任务ID</label>
            <input type="text" id="taskId" name="taskId" required placeholder="例如：TASK202307001">
          </div>
          <div class="form-group">
            <label for="taskContent">数据内容</label>
            <textarea id="taskContent" name="taskContent" rows="3" required placeholder="描述采集的数据范围（如：用户表全量数据、订单表增量数据）"></textarea>
          </div>
          <div class="form-group">
            <label for="executionCycle">执行周期</label>
            <select id="executionCycle" name="executionCycle" required>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="form-group" id="customCycleGroup" style="display: none;">
            <label for="customCycle">自定义周期（Cron表达式）</label>
            <input type="text" id="customCycle" name="customCycle" placeholder="例如：0 30 0 * * ? 表示每天00:30执行">
          </div>
          <div class="form-group">
            <label for="dataRange">数据范围</label>
            <input type="text" id="dataRange" name="dataRange" required placeholder="例如：id > 10000 AND create_time > '2023-07-01'">
          </div>
        </form>
      </div>

      <!-- 3. 批量文件格式解析 -->
      <div class="tab-content" id="fileParsingTab">
        <form id="fileParsingForm">
          <div class="form-group">
            <label for="fileType">文件类型</label>
            <select id="fileType" name="fileType" required>
              <option value="">请选择文件类型</option>
              <option value="csv">CSV</option>
              <option value="excel">Excel</option>
              <option value="json">JSON</option>
              <option value="xml">XML</option>
            </select>
          </div>
          <div class="form-group">
            <label for="batchFileData">批量文件数据</label>
            <textarea id="batchFileData" name="batchFileData" rows="4" required placeholder="请输入文件路径（每行一个，如：/data/file1.csv&#10;/data/file2.csv）"></textarea>
          </div>
          <div class="form-group">
            <label>解析状态</label>
            <div class="tag tag-info" id="parsingStatus">待解析</div>
          </div>
          <div class="form-group">
            <label>解析结果预览</label>
            <div class="preview-box" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; max-height: 120px; overflow-y: auto;">
              <p style="color: var(--text-tertiary);">解析完成后显示结果...</p>
            </div>
          </div>
        </form>
      </div>

      <!-- 4. 增量数据采集 -->
      <div class="tab-content" id="incrementalCollectionTab">
        <form id="incrementalCollectionForm">
          <div class="form-group">
            <label for="incFileType">文件类型</label>
            <select id="incFileType" name="incFileType" required>
              <option value="">请选择文件类型</option>
              <option value="mysql">MySQL binlog</option>
              <option value="file">增量文件</option>
              <option value="api">API增量接口</option>
            </select>
          </div>
          <div class="form-group">
            <label for="incrementalMarker">增量标识（如时间戳/版本号）</label>
            <input type="text" id="incrementalMarker" name="incrementalMarker" required placeholder="例如：2023-07-15 00:00:00 或 v1.2.0">
          </div>
          <div class="form-group">
            <label for="incrementalData">增量文件数据</label>
            <textarea id="incrementalData" name="incrementalData" rows="4" placeholder="请输入增量数据内容或路径"></textarea>
          </div>
          <div class="form-group">
            <label>采集结果</label>
            <div class="tag tag-warning" id="collectionResult">待采集</div>
          </div>
        </form>
      </div>

      <!-- 5. 文件数据分页控制 -->
      <div class="tab-content" id="paginationControlTab">
        <form id="paginationControlForm">
          <div class="form-group">
            <label for="paginationFileName">文件名称</label>
            <input type="text" id="paginationFileName" name="paginationFileName" required placeholder="请输入文件名称">
          </div>
          <div class="form-group">
            <label for="paginationFileType">文件类型</label>
            <select id="paginationFileType" name="paginationFileType" required>
              <option value="">请选择文件类型</option>
              <option value="csv">CSV</option>
              <option value="excel">Excel</option>
              <option value="json">JSON</option>
            </select>
          </div>
          <div class="form-group">
            <label for="totalDataCount">总数据量（条）</label>
            <input type="number" id="totalDataCount" name="totalDataCount" readonly placeholder="自动计算">
          </div>
          <div class="form-group">
            <label for="pageSize">分页大小（条/页）</label>
            <input type="number" id="pageSize" name="pageSize" min="10" max="1000" value="100" required>
          </div>
          <div class="form-group">
            <label>分页结果</label>
            <div class="tag tag-info" id="paginationResult">未计算</div>
          </div>
        </form>
      </div>

      <!-- 6. 压缩离线数据文件 -->
      <div class="tab-content" id="compressFileTab">
        <form id="compressFileForm">
          <div class="form-group">
            <label for="originalFileName">原始文件名称</label>
            <input type="text" id="originalFileName" name="originalFileName" required placeholder="请输入原始文件名称">
          </div>
          <div class="form-group">
            <label for="originalFileType">文件类型</label>
            <select id="originalFileType" name="originalFileType" required>
              <option value="">请选择文件类型</option>
              <option value="txt">文本文件</option>
              <option value="csv">CSV</option>
              <option value="json">JSON</option>
              <option value="excel">Excel</option>
            </select>
          </div>
          <div class="form-group">
            <label for="compressMethod">压缩方式</label>
            <select id="compressMethod" name="compressMethod" required>
              <option value="zip">ZIP</option>
              <option value="gzip">GZIP</option>
              <option value="7z">7Z</option>
            </select>
          </div>
          <div class="form-group">
            <label for="compressLevel">压缩级别（1-9，9为最高）</label>
            <input type="number" id="compressLevel" name="compressLevel" min="1" max="9" value="5" required>
          </div>
          <div class="form-group">
            <label>压缩状态</label>
            <div class="tag tag-info" id="compressStatus">未压缩</div>
          </div>
        </form>
      </div>

      <!-- 7. 集采文件完整性校验 -->
      <div class="tab-content" id="integrityCheckTab">
        <form id="integrityCheckForm">
          <div class="form-group">
            <label for="checkAlgorithm">校验算法</label>
            <select id="checkAlgorithm" name="checkAlgorithm" required>
              <option value="md5">MD5</option>
              <option value="sha256">SHA-256</option>
              <option value="crc32">CRC32</option>
            </select>
          </div>
          <div class="form-group">
            <label for="originalFileHash">原始文件哈希值</label>
            <input type="text" id="originalFileHash" name="originalFileHash" required placeholder="请输入原始文件的哈希值">
          </div>
          <div class="form-group">
            <label for="targetFilePath">待校验文件路径</label>
            <input type="text" id="targetFilePath" name="targetFilePath" required placeholder="请输入待校验文件的路径">
          </div>
          <div class="form-group">
            <label>校验结果</label>
            <div class="tag tag-info" id="checkResult">未校验</div>
          </div>
        </form>
      </div>

      <!-- 8. 任务优先级调度 -->
      <div class="tab-content" id="prioritySchedulingTab">
        <form id="prioritySchedulingForm">
          <div class="form-group">
            <label for="scheduleTaskId">任务ID</label>
            <input type="text" id="scheduleTaskId" name="scheduleTaskId" required placeholder="请输入任务ID">
          </div>
          <div class="form-group">
            <label for="taskType">任务类型</label>
            <select id="taskType" name="taskType" required>
              <option value="full">全量采集</option>
              <option value="incremental">增量采集</option>
              <option value="clean">数据清洗</option>
              <option value="compress">文件压缩</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskWeight">任务权重（1-10，10为最高）</label>
            <input type="number" id="taskWeight" name="taskWeight" min="1" max="10" value="5" required>
          </div>
          <div class="form-group">
            <label for="scheduleTime">期望调度时间</label>
            <input type="datetime-local" id="scheduleTime" name="scheduleTime" required>
          </div>
          <div class="form-group">
            <label>调度结果</label>
            <div class="tag tag-info" id="scheduleResult">未调度</div>
          </div>
        </form>
      </div>

      <!-- 9. 批量任务拆分 -->
      <div class="tab-content" id="taskSplitTab">
        <form id="taskSplitForm">
          <div class="form-group">
            <label for="splitTaskId">任务ID</label>
            <input type="text" id="splitTaskId" name="splitTaskId" required placeholder="请输入主任务ID">
          </div>
          <div class="form-group">
            <label for="splitTaskName">任务名称</label>
            <input type="text" id="splitTaskName" name="splitTaskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="splitTaskType">任务类型</label>
            <select id="splitTaskType" name="splitTaskType" required>
              <option value="dataRange">按数据范围拆分</option>
              <option value="file">按文件拆分</option>
              <option value="field">按字段拆分</option>
            </select>
          </div>
          <div class="form-group">
            <label for="splitCount">拆分数量</label>
            <input type="number" id="splitCount" name="splitCount" min="2" max="100" value="5" required>
          </div>
          <div class="form-group">
            <label>拆分结果</label>
            <div class="preview-box" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; max-height: 100px; overflow-y: auto;">
              <p style="color: var(--text-tertiary);">拆分后显示子任务ID列表...</p>
            </div>
          </div>
        </form>
      </div>

      <!-- 10. 任务依赖管理 -->
      <div class="tab-content" id="dependencyManagementTab">
        <form id="dependencyManagementForm">
          <div class="form-group">
            <label for="currentTaskId">当前任务ID</label>
            <input type="text" id="currentTaskId" name="currentTaskId" required placeholder="请输入当前任务ID">
          </div>
          <div class="form-group">
            <label for="dependentTaskIds">被依赖任务ID（多个用逗号分隔）</label>
            <input type="text" id="dependentTaskIds" name="dependentTaskIds" required placeholder="例如：TASK001,TASK002">
          </div>
          <div class="form-group">
            <label for="dependencyType">依赖类型</label>
            <select id="dependencyType" name="dependencyType" required>
              <option value="success">依赖任务成功完成</option>
              <option value="start">依赖任务开始执行</option>
              <option value="timeout">依赖任务超时</option>
            </select>
          </div>
          <div class="form-group">
            <label>依赖关系图</label>
            <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; height: 100px; display: flex; align-items: center; justify-content: center; color: var(--text-tertiary);">
              当前任务 → 依赖任务1 → 依赖任务2
            </div>
          </div>
        </form>
      </div>

      <!-- 11. 任务超时控制 -->
      <div class="tab-content" id="timeoutControlTab">
        <form id="timeoutControlForm">
          <div class="form-group">
            <label for="timeoutTaskName">任务名称</label>
            <input type="text" id="timeoutTaskName" name="timeoutTaskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="timeoutThreshold">超时阈值（分钟）</label>
            <input type="number" id="timeoutThreshold" name="timeoutThreshold" min="1" max="1440" value="30" required>
          </div>
          <div class="form-group">
            <label for="timeoutAction">超时后操作</label>
            <select id="timeoutAction" name="timeoutAction" required>
              <option value="retry">自动重试</option>
              <option value="stop">强制终止</option>
              <option value="alert">仅告警</option>
            </select>
          </div>
          <div class="form-group">
            <label for="retryCount">重试次数（超时后操作为自动重试时生效）</label>
            <input type="number" id="retryCount" name="retryCount" min="1" max="5" value="2">
          </div>
        </form>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('batchCollectionModal').classList.remove('show')">取消</button>
      <button class="btn btn-primary" onclick="submitBatchCollectionForm()">保存配置</button>
    </div>
  </div>
</div>

<!-- 文件数据清洗与预处理模态框 -->
<div class="modal" id="dataCleaningModal">
  <div class="modal-content" style="width: 900px; max-height: 80vh; overflow-y: auto;">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-filter"></i> 文件数据清洗与预处理</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <div class="tabs">
        <div class="tab-item active" data-tab-target="dataFilterTab">数据过滤</div>
        <div class="tab-item" data-tab-target="dataTransformTab">数据转换</div>
        <div class="tab-item" data-tab-target="missingValueTab">缺失值处理</div>
        <div class="tab-item" data-tab-target="duplicateValueTab">重复值处理</div>
        <div class="tab-item" data-tab-target="outlierTab">异常值处理</div>
        <div class="tab-item" data-tab-target="dataStandardTab">数据标准化</div>
        <div class="tab-item" data-tab-target="cleanResultTab">结果预览</div>
      </div>

      <!-- 1. 数据过滤 -->
      <div class="tab-content active" id="dataFilterTab">
        <form id="dataFilterForm">
          <div class="form-group">
            <label for="filterField">过滤字段</label>
            <input type="text" id="filterField" name="filterField" required placeholder="例如：age, score">
          </div>
          <div class="form-group">
            <label for="filterCondition">过滤条件</label>
            <select id="filterCondition" name="filterCondition" required>
              <option value="">请选择过滤条件</option>
              <option value="gt">大于 (>)</option>
              <option value="lt">小于 (<)</option>
              <option value="gte">大于等于 (>=)</option>
              <option value="lte">小于等于 (<=)</option>
              <option value="eq">等于 (==)</option>
              <option value="neq">不等于 (!=)</option>
              <option value="in">包含 (in)</option>
              <option value="nin">不包含 (not in)</option>
            </select>
          </div>
          <div class="form-group">
            <label for="filterValue">过滤值</label>
            <input type="text" id="filterValue" name="filterValue" required placeholder="例如：18, 'active'">
          </div>
          <div class="form-group">
            <label for="filterLogic">逻辑关系（多条件时）</label>
            <select id="filterLogic" name="filterLogic">
              <option value="and">并且 (AND)</option>
              <option value="or">或者 (OR)</option>
            </select>
          </div>
          <div class="form-group">
            <label>过滤前数据预览</label>
            <div class="preview-box">
              <table class="table mini-table">
                <thead><tr><th>id</th><th>name</th><th>age</th><th>score</th></tr></thead>
                <tbody>
                  <tr><td>1</td><td>张三</td><td>17</td><td>85</td></tr>
                  <tr><td>2</td><td>李四</td><td>22</td><td>92</td></tr>
                  <tr><td>3</td><td>王五</td><td>19</td><td>60</td></tr>
                  <tr><td>4</td><td>赵六</td><td>25</td><td>45</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="form-group" id="filterResultGroup" style="display: none;">
            <label>过滤结果预览</label>
            <div class="preview-box" id="filterResult">
              <!-- 结果将在这里显示 -->
            </div>
          </div>
        </form>
      </div>

      <!-- 2. 数据转换 -->
      <div class="tab-content" id="dataTransformTab">
        <form id="dataTransformForm">
          <div class="form-group">
            <label for="transformField">转换字段</label>
            <input type="text" id="transformField" name="transformField" required placeholder="例如：score, create_time">
          </div>
          <div class="form-group">
            <label for="transformType">转换类型</label>
            <select id="transformType" name="transformType" required>
              <option value="">请选择转换类型</option>
              <option value="uppercase">大写转换</option>
              <option value="lowercase">小写转换</option>
              <option value="trim">去除空格</option>
              <option value="dateFormat">日期格式化</option>
              <option value="math">数学运算</option>
              <option value="custom">自定义函数</option>
            </select>
          </div>
          <div class="form-group" id="transformParamGroup">
            <label for="transformParam">转换参数</label>
            <input type="text" id="transformParam" name="transformParam" placeholder="例如：yyyy-MM-dd, *100, function(x){return x+1}">
          </div>
          <div class="form-group">
            <label>转换前数据预览</label>
            <div class="preview-box">
              <table class="table mini-table">
                <thead><tr><th>id</th><th>name</th><th>score</th><th>join_date</th></tr></thead>
                <tbody>
                  <tr><td>1</td><td> zhangsan </td><td>0.85</td><td>20230715</td></tr>
                  <tr><td>2</td><td> LISI </td><td>0.92</td><td>20230620</td></tr>
                  <tr><td>3</td><td> wangwu </td><td>0.60</td><td>20230510</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="form-group" id="transformResultGroup" style="display: none;">
            <label>转换结果预览</label>
            <div class="preview-box" id="transformResult">
              <!-- 结果将在这里显示 -->
            </div>
          </div>
        </form>
      </div>

      <!-- 3. 缺失值处理 -->
      <div class="tab-content" id="missingValueTab">
        <form id="missingValueForm">
          <div class="form-group">
            <label for="missingField">处理字段</label>
            <input type="text" id="missingField" name="missingField" required placeholder="例如：age, address">
          </div>
          <div class="form-group">
            <label for="missingHandleType">处理方式</label>
            <select id="missingHandleType" name="missingHandleType" required>
              <option value="">请选择处理方式</option>
              <option value="delete">删除记录</option>
              <option value="mean">均值填充</option>
              <option value="median">中位数填充</option>
              <option value="mode">众数填充</option>
              <option value="fixed">固定值填充</option>
              <option value="forward">前向填充</option>
              <option value="backward">后向填充</option>
            </select>
          </div>
          <div class="form-group" id="missingValueGroup">
            <label for="missingValue">填充值（当处理方式为固定值时）</label>
            <input type="text" id="missingValue" name="missingValue" placeholder="例如：0, '未知'">
          </div>
          <div class="form-group">
            <label>处理前数据预览</label>
            <div class="preview-box">
              <table class="table mini-table">
                <thead><tr><th>id</th><th>name</th><th>age</th><th>salary</th></tr></thead>
                <tbody>
                  <tr><td>1</td><td>张三</td><td></td><td>5000</td></tr>
                  <tr><td>2</td><td>李四</td><td>22</td><td></td></tr>
                  <tr><td>3</td><td>王五</td><td>19</td><td>6000</td></tr>
                  <tr><td>4</td><td>赵六</td><td></td><td>4500</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="form-group" id="missingResultGroup" style="display: none;">
            <label>处理结果预览</label>
            <div class="preview-box" id="missingResult">
              <!-- 结果将在这里显示 -->
            </div>
          </div>
        </form>
      </div>

      <!-- 4. 重复值处理 -->
      <div class="tab-content" id="duplicateValueTab">
        <form id="duplicateValueForm">
          <div class="form-group">
            <label for="duplicateFields">去重字段（多个用逗号分隔）</label>
            <input type="text" id="duplicateFields" name="duplicateFields" required placeholder="例如：name, email">
          </div>
          <div class="form-group">
            <label for="duplicateHandleType">处理方式</label>
            <select id="duplicateHandleType" name="duplicateHandleType" required>
              <option value="keepFirst">保留第一条</option>
              <option value="keepLast">保留最后一条</option>
              <option value="deleteAll">全部删除</option>
              <option value="merge">合并（取非空值）</option>
            </select>
          </div>
          <div class="form-group">
            <label>处理前数据预览</label>
            <div class="preview-box">
              <table class="table mini-table">
                <thead><tr><th>id</th><th>name</th><th>email</th><th>phone</th></tr></thead>
                <tbody>
                  <tr><td>1</td><td>张三</td><td><EMAIL></td><td>13800138000</td></tr>
                  <tr><td>2</td><td>张三</td><td><EMAIL></td><td>13900139000</td></tr>
                  <tr><td>3</td><td>李四</td><td><EMAIL></td><td>13700137000</td></tr>
                  <tr><td>4</td><td>李四</td><td><EMAIL></td><td></td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="form-group" id="duplicateResultGroup" style="display: none;">
            <label>处理结果预览</label>
            <div class="preview-box" id="duplicateResult">
              <!-- 结果将在这里显示 -->
            </div>
          </div>
        </form>
      </div>

      <!-- 5. 异常值处理 -->
      <div class="tab-content" id="outlierTab">
        <form id="outlierForm">
          <div class="form-group">
            <label for="outlierField">处理字段</label>
            <input type="text" id="outlierField" name="outlierField" required placeholder="例如：price, quantity">
          </div>
          <div class="form-group">
            <label for="outlierDetectMethod">检测方法</label>
            <select id="outlierDetectMethod" name="outlierDetectMethod" required>
              <option value="">请选择检测方法</option>
              <option value="zscore">Z-score方法</option>
              <option value="iqr">四分位法(IQR)</option>
              <option value="threshold">自定义阈值</option>
            </select>
          </div>
          <div class="form-group" id="outlierThresholdGroup">
            <label>阈值范围</label>
            <div style="display: flex; gap: 10px;">
              <input type="number" step="any" id="outlierMin" name="outlierMin" placeholder="最小值">
              <span style="align-self: center;">至</span>
              <input type="number" step="any" id="outlierMax" name="outlierMax" placeholder="最大值">
            </div>
          </div>
          <div class="form-group">
            <label for="outlierHandleType">处理方式</label>
            <select id="outlierHandleType" name="outlierHandleType" required>
              <option value="delete">删除记录</option>
              <option value="cap">截断（替换为边界值）</option>
              <option value="mean">均值替换</option>
              <option value="median">中位数替换</option>
            </select>
          </div>
          <div class="form-group">
            <label>处理前数据预览</label>
            <div class="preview-box">
              <table class="table mini-table">
                <thead><tr><th>id</th><th>product</th><th>price</th><th>sales</th></tr></thead>
                <tbody>
                  <tr><td>1</td><td>A商品</td><td>99</td><td>120</td></tr>
                  <tr><td>2</td><td>B商品</td><td>150</td><td>85</td></tr>
                  <tr><td>3</td><td>C商品</td><td>2000</td><td>5</td></tr>
                  <tr><td>4</td><td>D商品</td><td>120</td><td>300</td></tr>
                  <tr><td>5</td><td>E商品</td><td>85</td><td>-50</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="form-group" id="outlierResultGroup" style="display: none;">
            <label>处理结果预览</label>
            <div class="preview-box" id="outlierResult">
              <!-- 结果将在这里显示 -->
            </div>
          </div>
        </form>
      </div>

      <!-- 6. 数据标准化 -->
      <div class="tab-content" id="dataStandardTab">
        <form id="dataStandardForm">
          <div class="form-group">
            <label for="standardFields">标准化字段（多个用逗号分隔）</label>
            <input type="text" id="standardFields" name="standardFields" required placeholder="例如：height, weight">
          </div>
          <div class="form-group">
            <label for="standardMethod">标准化方法</label>
            <select id="standardMethod" name="standardMethod" required>
              <option value="">请选择标准化方法</option>
              <option value="minmax">Min-Max标准化</option>
              <option value="zscore">Z-score标准化</option>
              <option value="decimal">小数定标标准化</option>
              <option value="log">对数转换</option>
              <option value="percent">百分比转换</option>
            </select>
          </div>
          <div class="form-group" id="standardParamGroup">
            <label for="standardParam">标准化参数</label>
            <input type="text" id="standardParam" name="standardParam" placeholder="例如：0,1（Min-Max的范围）">
          </div>
          <div class="form-group">
            <label>标准化前数据预览</label>
            <div class="preview-box">
              <table class="table mini-table">
                <thead><tr><th>id</th><th>name</th><th>height</th><th>weight</th></tr></thead>
                <tbody>
                  <tr><td>1</td><td>张三</td><td>175</td><td>65</td></tr>
                  <tr><td>2</td><td>李四</td><td>180</td><td>75</td></tr>
                  <tr><td>3</td><td>王五</td><td>165</td><td>55</td></tr>
                  <tr><td>4</td><td>赵六</td><td>190</td><td>80</td></tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="form-group" id="standardResultGroup" style="display: none;">
            <label>标准化结果预览</label>
            <div class="preview-box" id="standardResult">
              <!-- 结果将在这里显示 -->
            </div>
          </div>
        </form>
      </div>

      <!-- 7. 结果预览 -->
      <div class="tab-content" id="cleanResultTab">
        <div class="form-group">
          <label>操作历史</label>
          <div class="preview-box" id="cleanHistory">
            <div class="empty-state">暂无操作记录</div>
          </div>
        </div>
        <div class="form-group">
          <label>数据处理前后数据对比</label>
          <div style="display: flex; gap: 10px;">
            <div style="flex: 1;">
              <div style="font-weight: bold; margin-bottom: 8px;">处理前</div>
              <div class="preview-box" id="beforeCleanData">
                <div class="empty-state">请先执行数据处理操作</div>
              </div>
            </div>
            <div style="flex: 1;">
              <div style="font-weight: bold; margin-bottom: 8px;">处理后</div>
              <div class="preview-box" id="afterCleanData">
                <div class="empty-state">请先执行数据处理操作</div>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label>数据处理统计信息</label>
          <div class="preview-box">
            <table class="table mini-table" id="cleanStats">
              <thead><tr><th>统计项</th><th>数值</th></tr></thead>
              <tbody>
                <tr><td>原始记录数</td><td>-</td></tr>
                <tr><td>处理后记录数</td><td>-</td></tr>
                <tr><td>删除记录数</td><td>-</td></tr>
                <tr><td>修改记录数</td><td>-</td></tr>
                <tr><td>缺失值处理数</td><td>-</td></tr>
                <tr><td>重复值处理数</td><td>-</td></tr>
                <tr><td>异常值处理数</td><td>-</td></tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('dataCleaningModal').classList.remove('show')">取消</button>
      <button class="btn btn-primary" id="applyCleanBtn">应用处理规则</button>
      <button class="btn btn-success" id="saveCleanResultBtn">保存处理结果</button>
    </div>
  </div>
</div>




<!-- 文件数据安全管理模态框 -->
<div class="modal" id="dataSecurityModal">
  <div class="modal-content" style="width: 900px;">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-shield-alt"></i> 文件数据安全管理</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <div class="tabs">
        <div class="tab-item active" data-tab-target="encryptionTab">数据加密设置</div>
        <div class="tab-item" data-tab-target="accessControlTab">文件解压</div>
        <div class="tab-item" data-tab-target="dataMaskingTab">数据脱敏规则</div>
        <div class="tab-item" data-tab-target="securityAuditTab">文件解密</div>
        <div class="tab-item" data-tab-target="abnormalMonitorTab">异常访问监控</div>
      </div>

      <!-- 1. 数据加密设置 -->
      <div class="tab-content active" id="encryptionTab">
        <form id="encryptionForm">
          <div class="form-group">
            <label for="encryptFileName">文件名称</label>
            <input type="text" id="encryptFileName" name="encryptFileName" required placeholder="请输入需要加密的文件名称或路径">
          </div>
          <div class="form-group">
            <label for="encryptAlgorithm">加密算法</label>
            <select id="encryptAlgorithm" name="encryptAlgorithm" required>
              <option value="AES">AES-256</option>
              <option value="RSA">RSA</option>
              <option value="SM4">国密SM4</option>
            </select>
          </div>
          <div class="form-group">
            <label for="encryptKey">加密密钥</label>
            <input type="password" id="encryptKey" name="encryptKey" required placeholder="请输入加密密钥">
          </div>
          <div class="form-group">
            <label for="encryptMode">加密模式</label>
            <select id="encryptMode" name="encryptMode">
              <option value="CBC">CBC模式</option>
              <option value="ECB">ECB模式</option>
              <option value="GCM">GCM模式</option>
            </select>
          </div>
          <div class="form-group">
            <label>加密结果</label>
            <div id="encryptionResult" class="result-box">
              <p style="color: var(--text-tertiary);">加密后将显示结果...</p>
            </div>
          </div>
        </form>
      </div>

      <!-- 2. 访问权限管理 -->
      <div class="tab-content" id="accessControlTab">
        <form id="accessControlForm">
          <div class="form-group">
            <label for="accessFileName">文件名称</label>
            <input type="text" id="accessFileName" name="accessFileName" required placeholder="请输入文件名称或路径">
          </div>
          <div class="form-group">
            <label for="accessRole">解压类型</label>
            <select id="accessRole" name="accessRole" required>
              <option value="">请选择解压类型</option>
              <option value="admin">zip</option>
              <option value="operator">rar</option>
              <option value="viewer">7Z</option>
              <option value="developer">gz</option>
            </select>
          </div>
          <div class="form-group">
            <label>文件类型</label>
            <div style="display: flex; flex-wrap: wrap;">
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="accessPermissions" value="read"> pdf
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="accessPermissions" value="write"> word
              </label>
              <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                <input type="checkbox" name="accessPermissions" value="download"> png
              </label>
              <label style="display: flex; align-items: center; margin-bottom: 8px;">
                <input type="checkbox" name="accessPermissions" value="delete"> jpg
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>当前解压列表</label>
            <div id="accessListResult" class="result-box">
              <p style="color: var(--text-tertiary);">加载中...</p>
            </div>
          </div>
        </form>
      </div>

      <!-- 3. 数据脱敏规则 -->
      <div class="tab-content" id="dataMaskingTab">
        <form id="dataMaskingForm">
          <div class="form-group">
            <label for="maskingTableName">数据表名称</label>
            <input type="text" id="maskingTableName" name="maskingTableName" required placeholder="请输入需要脱敏的数据表名称">
          </div>
          <div class="form-group">
            <label for="maskingField">脱敏字段</label>
            <input type="text" id="maskingField" name="maskingField" required placeholder="请输入需要脱敏的字段，多个用逗号分隔">
          </div>
          <div class="form-group">
            <label for="maskingRule">脱敏规则</label>
            <select id="maskingRule" name="maskingRule" required>
              <option value="">请选择脱敏规则</option>
              <option value="phone">手机号脱敏 (138****5678)</option>
              <option value="idcard">身份证号脱敏 (110********1234)</option>
              <option value="name">姓名脱敏 (张**)</option>
              <option value="email">邮箱脱敏 (z***@example.com)</option>
              <option value="custom">自定义正则</option>
            </select>
          </div>
          <div class="form-group" id="customRegexGroup" style="display: none;">
            <label for="customRegex">自定义正则表达式</label>
            <input type="text" id="customRegex" name="customRegex" placeholder="例如：(\\d{3})\\d{4}(\\d{4})">
            <label for="replacePattern">替换模式</label>
            <input type="text" id="replacePattern" name="replacePattern" placeholder="例如：$1****$2">
          </div>
          <div class="form-group">
            <label>脱敏效果预览</label>
            <div id="maskingPreview" class="result-box">
              <p style="color: var(--text-tertiary);">输入内容后显示预览...</p>
            </div>
          </div>
        </form>
      </div>

      <!-- 4. 安全审计日志 -->
      <div class="tab-content" id="securityAuditTab">
        <form id="securityAuditForm">
          <div class="form-group">
            <label for="auditFileName">文件名称</label>
            <input type="text" id="auditFileName" name="auditFileName" placeholder="请输入文件名称，为空则查询全部">
          </div>
          <div class="form-group">
            <label for="auditFileName">文件内容</label>
            <input type="text" id="auditFileName" name="auditFileName" placeholder="请输入文件内容，为空则查询全部">
          </div>
                    <div class="form-group">
            <label for="encryptKey">解密密钥</label>
            <input type="password" id="encryptKey" name="encryptKey" required placeholder="请输入解密密钥">
          </div>

          <div class="form-group">
            <label>解密结果</label>
            <div class="table-container" style="max-height: 200px; overflow-y: auto;">
              <table class="table" id="auditLogTable">
                <thead>
                  <tr>
                    <th>文件名称</th>
                    <th>文件内容</th>
                    <th>解密密钥</th>
                    <th>操作结果</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="6" style="text-align: center; color: var(--text-tertiary);">请点击查询按钮加载日志</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </form>
      </div>

      <!-- 5. 异常访问监控 -->
      <div class="tab-content" id="abnormalMonitorTab">
        <form id="abnormalMonitorForm">
          <div class="form-group">
            <label for="monitorDuration">监控时长</label>
            <select id="monitorDuration" name="monitorDuration" required>
              <option value="24h">最近24小时</option>
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="form-group" id="customMonitorTimeGroup" style="display: none;">
            <div style="display: flex; gap: 10px;">
              <input type="datetime-local" id="monitorStartTime" name="monitorStartTime">
              <span style="align-self: center;">至</span>
              <input type="datetime-local" id="monitorEndTime" name="monitorEndTime">
            </div>
          </div>
          <div class="form-group">
            <label for="abnormalLevel">异常级别</label>
            <select id="abnormalLevel" name="abnormalLevel">
              <option value="all">全部级别</option>
              <option value="warning">警告</option>
              <option value="danger">危险</option>
              <option value="critical">严重</option>
            </select>
          </div>
          <div class="form-group">
            <label>异常访问统计</label>
            <div id="abnormalStats" class="result-box" style="height: 100px; display: flex; align-items: center; justify-content: center;">
              <p style="color: var(--text-tertiary);">请点击查询按钮加载统计数据</p>
            </div>
          </div>
          <div class="form-group">
            <label>最近异常访问记录</label>
            <div class="table-container" style="max-height: 150px; overflow-y: auto;">
              <table class="table" id="abnormalRecordTable">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>文件名称</th>
                    <th>访问IP</th>
                    <th>异常类型</th>
                    <th>级别</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="5" style="text-align: center; color: var(--text-tertiary);">请点击查询按钮加载记录</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('dataSecurityModal').classList.remove('show')">关闭</button>
      <button class="btn btn-primary" id="securityActionBtn">保存/查询</button>
    </div>
  </div>
</div>
 
<!-- 批量采集日志审计与统计-->

<div class="modal" id="logAuditModal">
  <div class="modal-content" style="width: 900px;">
    <div class="modal-header">
      <div class="modal-title"><i class="fas fa-clipboard-list"></i> 批量采集日志审计与统计</div>
      <button class="modal-close">&times;</button>
    </div>
    <div class="modal-body">
      <div class="tabs">
        <div class="tab-item active" data-tab-target="taskExecutionLogTab">任务执行日志</div>
        <div class="tab-item" data-tab-target="dataCleanupTab">过期数据清理</div>
        <div class="tab-item" data-tab-target="dataControlTab">批采数据控制阀</div>
        <div class="tab-item" data-tab-target="dataFlowTab">数据处理来源和流向</div>
        <div class="tab-item" data-tab-target="reportStatisticsTab">采集结果报告统计</div>
      </div>

      <!-- 1. 任务执行日志 -->
      <div class="tab-content active" id="taskExecutionLogTab">
        <form id="taskExecutionLogForm">
          <div class="form-group">
            <label for="logTaskId">任务ID</label>
            <input type="text" id="logTaskId" name="logTaskId" placeholder="请输入任务ID（留空查全部）">
          </div>
          <div class="form-group">
            <label for="executionStartTime">执行开始时间</label>
            <input type="datetime-local" id="executionStartTime" name="executionStartTime" required>
          </div>
          <div class="form-group">
            <label for="executionEndTime">执行结束时间</label>
            <input type="datetime-local" id="executionEndTime" name="executionEndTime" required>
          </div>
          <div class="form-group">
            <label for="executionResult">执行结果</label>
            <select id="executionResult" name="executionResult" required>
              <option value="all">全部</option>
              <option value="success">成功</option>
              <option value="failed">失败</option>
              <option value="running">运行中</option>
              <option value="canceled">已删除</option>
            </select>
          </div>
        </form>
        <div class="table-container" style="margin-top: 20px;">
          <table class="table">
            <thead>
              <tr>
                <th>日志ID</th>
                <th>任务ID</th>
                <th>执行时间</th>
                <th>执行状态</th>
                <th>耗时</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>LOG20230715001</td>
                <td>TASK001</td>
                <td>2023-07-15 00:30:00</td>
                <td><span class="tag tag-success">成功</span></td>
                <td>120s</td>
                <td><button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button></td>
              </tr>
              <tr>
                <td>LOG20230715002</td>
                <td>TASK002</td>
                <td>2023-07-15 01:00:00</td>
                <td><span class="tag tag-success">成功</span></td>
                <td>180s</td>
                <td><button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button></td>
              </tr>
              <tr>
                <td>LOG20230715003</td>
                <td>TASK003</td>
                <td>2023-07-15 09:00:00</td>
                <td><span class="tag tag-danger">失败</span></td>
                <td>30s</td>
                <td><button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 2. 过期数据清理 -->
      <div class="tab-content" id="dataCleanupTab">
        <form id="dataCleanupForm">
          <div class="form-group">
            <label for="cleanupFileType">文件类型</label>
            <select id="cleanupFileType" name="cleanupFileType" required>
              <option value="">请选择文件类型</option>
              <option value="log">日志文件</option>
              <option value="temp">临时数据文件</option>
              <option value="backup">备份文件</option>
              <option value="all">全部类型</option>
            </select>
          </div>
          <div class="form-group">
            <label for="retentionPeriod">保留周期（天）</label>
            <input type="number" id="retentionPeriod" name="retentionPeriod" min="1" max="365" value="30" required>
          </div>
          <div class="form-group">
            <label for="cleanupTime">清理时机</label>
            <select id="cleanupTime" name="cleanupTime" required>
              <option value="immediate">立即清理</option>
              <option value="scheduled">定时清理（每日凌晨2点）</option>
              <option value="threshold">达到存储阈值时清理</option>
            </select>
          </div>
          <div class="form-group">
            <label>预估清理量</label>
            <div class="tag tag-info" id="estimatedCleanup">计算中...</div>
          </div>
        </form>
      </div>

      <!-- 3. 批采数据控制阀 -->
      <div class="tab-content" id="dataControlTab">
        <form id="dataControlForm">
          <div class="form-group">
            <label for="controlStartTime">控制开始时间</label>
            <input type="datetime-local" id="controlStartTime" name="controlStartTime" required>
          </div>
          <div class="form-group">
            <label for="controlEndTime">控制结束时间</label>
            <input type="datetime-local" id="controlEndTime" name="controlEndTime" required>
          </div>
          <div class="form-group">
            <label for="maxFlow">最大流量（MB/分钟）</label>
            <input type="number" id="maxFlow" name="maxFlow" min="1" max="1000" value="100" required>
          </div>
          <div class="form-group">
            <label for="controlStrategy">控制策略</label>
            <select id="controlStrategy" name="controlStrategy" required>
              <option value="throttle">流量限制（超出则限流）</option>
              <option value="queue">队列等待（超出则排队）</option>
              <option value="reject">拒绝请求（超出则丢弃）</option>
            </select>
          </div>
          <div class="form-group">
            <label>当前流量状态</label>
            <div class="preview-box" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px;">
              <p>当前流量：<span id="currentFlow">45 MB/分钟</span></p>
              <p>状态：<span class="tag tag-success">正常</span></p>
            </div>
          </div>
        </form>
      </div>

      <!-- 4. 数据处理来源和流向 -->
      <div class="tab-content" id="dataFlowTab">
        <form id="dataFlowForm">
          <div class="form-group">
            <label for="flowTaskId">任务ID</label>
            <input type="text" id="flowTaskId" name="flowTaskId" required placeholder="请输入任务ID">
          </div>
          <div class="form-group">
            <label for="flowDate">查询日期</label>
            <input type="date" id="flowDate" name="flowDate" required>
          </div>
        </form>
        <div style="margin-top: 20px; border: 1px solid var(--border-color); border-radius: 4px; padding: 16px;">
          <h4 style="margin-top: 0;">数据流向图谱</h4>
          <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
            <div class="tag tag-primary">数据源：MySQL-用户数据库</div>
            <i class="fas fa-long-arrow-alt-right"></i>
            <div class="tag tag-info">处理节点：数据清洗服务</div>
            <i class="fas fa-long-arrow-alt-right"></i>
            <div class="tag tag-warning">中间存储：Redis缓存</div>
            <i class="fas fa-long-arrow-alt-right"></i>
            <div class="tag tag-success">目标存储：dw.user_info表</div>
          </div>
          <div style="margin-top: 16px;">
            <h5>数据量统计</h5>
            <p>来源数据量：12,500 条 | 清洗后数据量：12,300 条 | 最终存储量：12,300 条</p>
          </div>
        </div>
      </div>

      <!-- 5. 采集结果报告统计 -->
      <div class="tab-content" id="reportStatisticsTab">
        <form id="reportStatisticsForm">
          <div class="form-group">
            <label for="collectionCondition">采集条件</label>
            <textarea id="collectionCondition" name="collectionCondition" rows="2" required placeholder="例如：时间范围2023-07-01至2023-07-15，数据源=MySQL"></textarea>
          </div>
          <div class="form-group">
            <label for="collectionResult">采集结果</label>
            <select id="collectionResult" name="collectionResult" required>
              <option value="all">全部结果</option>
              <option value="success">仅成功</option>
              <option value="failed">仅失败</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reportTemplate">报告模板</label>
            <select id="reportTemplate" name="reportTemplate" required>
              <option value="simple">简易统计（仅数量）</option>
              <option value="detail">详细报告（含成功率、耗时）</option>
              <option value="trend">趋势分析报告（按日/周）</option>
            </select>
          </div>
          <div class="form-group">
            <label>报告预览</label>
            <div class="preview-box" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; max-height: 150px; overflow-y: auto;">
              <p style="color: var(--text-tertiary);">生成报告后显示预览...</p>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('logAuditModal').classList.remove('show')">取消</button>
      <button class="btn btn-primary" onclick="submitLogAuditForm()">查询/保存</button>
    </div>
  </div>
</div>



  <script src="js/common.js"></script>
  <script>
    // API基础URL
    const API_BASE_URL = 'http://localhost:8000/api';

    // 封装fetch请求函数
    async function apiRequest(url, method, data = null) {
      try {
        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json',
            // 添加认证令牌等其他必要的请求头
            'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
          }
        };

        if (data) {
          options.body = JSON.stringify(data);
        }

        console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
        const response = await fetch(url, options);
        const result = await response.json();
        console.log(`接口响应:`, result);

        if (!response.ok) {
          throw new Error(result.message || `请求失败: ${response.status}`);
        }

        return result;
      } catch (error) {
        console.error('API请求错误:', error);
        // 不显示错误提示，保持原有逻辑
        throw error;
      }
    }

    // 切换调度类型表单
    function changeScheduleForm(type) {
      // 隐藏所有调度表单
      document.getElementById('dailySchedule').style.display = 'none';
      document.getElementById('weeklySchedule').style.display = 'none';
      document.getElementById('monthlySchedule').style.display = 'none';
      document.getElementById('hourlySchedule').style.display = 'none';
      document.getElementById('customSchedule').style.display = 'none';

      // 显示选中的调度表单
      if (type) {
        document.getElementById(type + 'Schedule').style.display = 'block';
      }
    }

    // 显示通知
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = 'notification ' + type;
      notification.innerText = message;
      document.body.appendChild(notification);

      // 显示通知
      setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
      }, 10);

      // 3秒后隐藏通知
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }

    // 模拟异步操作
    function simulateAsyncOperation(data, successRate = 0.9) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() < successRate) {
            resolve({ success: true, data });
          } else {
            reject({ success: false, error: '操作失败，请重试' });
          }
        }, 1500);
      });
    }

    // 新增离线任务表单提交
    document.getElementById('addOfflineTaskForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('addOfflineTaskForm')) {
        try {
          // 调用创建离线任务接口
          await apiRequest(`${API_BASE_URL}/offline-tasks`, 'POST', {
            name: document.getElementById('taskName').value,
            description: document.getElementById('taskDescription').value,
            dataSource: document.getElementById('dataSource').value,
            scheduleType: document.getElementById('scheduleType').value
          });
          console.log('创建离线任务成功');
        } catch (error) {
          console.error('创建离线任务失败:', error);
        }
        
        // 保持原有逻辑
        showNotification('离线采集任务创建成功并已上线！', 'success');
        document.getElementById('addOfflineTaskModal').classList.remove('show');
        // 重置表单
        this.reset();
        changeScheduleForm('');
      }
    });

    // 配置动态数据源表单提交
    document.getElementById('dynamicDataSourceForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('dynamicDataSourceForm')) {
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

        try {
          // 调用配置动态数据源接口
          await apiRequest(`${API_BASE_URL}/data-sources/dynamic`, 'POST', {
            name: document.getElementById('dynamicDataSourceName').value,
            type: document.getElementById('dynamicDataSourceType').value,
            config: document.getElementById('dynamicDataSourceConfig').value
          });
          console.log('配置动态数据源成功');
        } catch (error) {
          console.error('配置动态数据源失败:', error);
        }

        // 保持原有逻辑
        showNotification('动态数据源配置成功！', 'success');
        document.getElementById('batchCollectionModal').classList.remove('show');
        this.reset();
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }
    });

    // 新增采集任务表单提交
    document.getElementById('addCollectionTaskForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('addCollectionTaskForm')) {
        const submitBtn = document.querySelector('#batchCollectionModal .btn-primary');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

        try {
          // 调用创建采集任务接口
          await apiRequest(`${API_BASE_URL}/collection-tasks`, 'POST', {
            name: document.getElementById('collectionTaskName').value,
            dataSource: document.getElementById('collectionDataSource').value,
            schedule: document.getElementById('collectionSchedule').value,
            priority: document.getElementById('collectionPriority').value
          });
          console.log('创建采集任务成功');
        } catch (error) {
          console.error('创建采集任务失败:', error);
        }

        // 保持原有逻辑
        showNotification('采集任务创建成功！', 'success');
        document.getElementById('batchCollectionModal').classList.remove('show');
        this.reset();
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }
    });

    // 字段映射表单提交
    document.getElementById('fieldMappingForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('fieldMappingForm')) {
        const submitBtn = document.querySelector('#dataCleaningModal .btn-primary');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

        try {
          // 调用字段映射配置接口
          await apiRequest(`${API_BASE_URL}/data-cleaning/field-mapping`, 'POST', {
            sourceField: document.getElementById('sourceField').value,
            targetField: document.getElementById('targetField').value,
            transformType: document.getElementById('transformType').value,
            transformRule: document.getElementById('transformRule').value
          });
          console.log('字段映射配置成功');
        } catch (error) {
          console.error('字段映射配置失败:', error);
        }

        // 保持原有逻辑
        showNotification('字段映射配置成功！', 'success');
        document.getElementById('dataCleaningModal').classList.remove('show');
        this.reset();
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }
    });

    // 敏感数据脱敏表单提交
    document.getElementById('dataMaskingForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('dataMaskingForm')) {
        const submitBtn = document.querySelector('#dataSecurityModal .btn-primary');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

        try {
          // 调用敏感数据脱敏配置接口
          await apiRequest(`${API_BASE_URL}/data-security/masking`, 'POST', {
            fieldName: document.getElementById('maskingField').value,
            maskingRule: document.getElementById('maskingRule').value,
            maskingLevel: document.getElementById('maskingLevel').value,
            monitorDuration: document.getElementById('monitorDuration').value
          });
          console.log('敏感数据脱敏配置成功');
        } catch (error) {
          console.error('敏感数据脱敏配置失败:', error);
        }

        // 保持原有逻辑
        showNotification('敏感数据脱敏配置成功！', 'success');
        document.getElementById('dataSecurityModal').classList.remove('show');
        this.reset();
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }
    });

    // 任务执行日志表单提交
    document.getElementById('taskExecutionLogForm').addEventListener('submit', async function(e) {
      e.preventDefault();
      if (validateForm('taskExecutionLogForm')) {
        const submitBtn = document.querySelector('#logAuditModal .btn-primary');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';

        try {
          // 调用任务执行日志查询接口
          await apiRequest(`${API_BASE_URL}/task-execution-logs`, 'GET', {
            taskId: document.getElementById('logTaskId').value,
            startDate: document.getElementById('logStartDate').value,
            endDate: document.getElementById('logEndDate').value,
            logLevel: document.getElementById('logLevel').value
          });
          console.log('任务执行日志查询成功');
        } catch (error) {
          console.error('任务执行日志查询失败:', error);
        }

        // 保持原有逻辑
        showNotification('日志查询成功！', 'success');
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }
    });

    // 提交日志审计表单函数
    async function submitLogAuditForm() {
      try {
        // 调用批量采集日志审计接口
        await apiRequest(`${API_BASE_URL}/log-audit/query`, 'POST', {
          taskId: document.getElementById('logTaskId').value,
          startDate: document.getElementById('logStartDate').value,
          endDate: document.getElementById('logEndDate').value,
          logLevel: document.getElementById('logLevel').value,
          collectionResult: document.getElementById('collectionResult').value,
          reportTemplate: document.getElementById('reportTemplate').value
        });
        console.log('批量采集日志审计查询成功');
      } catch (error) {
        console.error('批量采集日志审计查询失败:', error);
      }

      // 保持原有逻辑
      showNotification('日志审计查询成功！', 'success');
    }

    // 初始化批量数据离线采集标签页
    function initBatchCollectionTabs() {
      const tabItems = document.querySelectorAll('#batchCollectionModal .tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', () => {
          const tabId = item.getAttribute('data-tab-target');
          const tabContent = document.getElementById(tabId);

          // 移除所有活动状态
          document.querySelectorAll('#batchCollectionModal .tab-item').forEach(ti => ti.classList.remove('active'));
          document.querySelectorAll('#batchCollectionModal .tab-content').forEach(tc => tc.classList.remove('active'));

          // 添加当前活动状态
          item.classList.add('active');
          tabContent.classList.add('active');
        });
      });
    }

    // 初始化文件数据清洗与预处理标签页
    function initDataCleaningTabs() {
      const tabItems = document.querySelectorAll('#dataCleaningModal .tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', () => {
          const tabId = item.getAttribute('data-tab-target');
          const tabContent = document.getElementById(tabId);

          // 移除所有活动状态
          document.querySelectorAll('#dataCleaningModal .tab-item').forEach(ti => ti.classList.remove('active'));
          document.querySelectorAll('#dataCleaningModal .tab-content').forEach(tc => tc.classList.remove('active'));

          // 添加当前活动状态
          item.classList.add('active');
          tabContent.classList.add('active');
        });
      });
    }

    // 初始化文件数据安全管理标签页
    function initDataSecurityTabs() {
      const tabItems = document.querySelectorAll('#dataSecurityModal .tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', () => {
          const tabId = item.getAttribute('data-tab-target');
          const tabContent = document.getElementById(tabId);

          // 移除所有活动状态
          document.querySelectorAll('#dataSecurityModal .tab-item').forEach(ti => ti.classList.remove('active'));
          document.querySelectorAll('#dataSecurityModal .tab-content').forEach(tc => tc.classList.remove('active'));

          // 添加当前活动状态
          item.classList.add('active');
          tabContent.classList.add('active');
        });
      });
    }

    // 初始化批量采集日志审计与统计标签页
    function initLogAuditTabs() {
      const tabItems = document.querySelectorAll('#logAuditModal .tab-item');
      tabItems.forEach(item => {
        item.addEventListener('click', () => {
          const tabId = item.getAttribute('data-tab-target');
          const tabContent = document.getElementById(tabId);

          // 移除所有活动状态
          document.querySelectorAll('#logAuditModal .tab-item').forEach(ti => ti.classList.remove('active'));
          document.querySelectorAll('#logAuditModal .tab-content').forEach(tc => tc.classList.remove('active'));

          // 添加当前活动状态
          item.classList.add('active');
          tabContent.classList.add('active');
        });
      });
    }



    // 标签页切换通用逻辑
function initAllTabs() {
  // 批量数据离线采集标签页
  initTabs('batchCollectionModal');
  // 数据清洗标签页
  initTabs('dataCleaningModal');
  // 数据安全标签页
  initTabs('dataSecurityModal');
  // 日志审计标签页
  initTabs('logAuditModal');
}

function initTabs(modalId) {
  const tabItems = document.querySelectorAll(`#${modalId} .tab-item`);
  tabItems.forEach(item => {
    item.addEventListener('click', () => {
      const tabTarget = item.getAttribute('data-tab-target');
      // 移除所有活动状态
      document.querySelectorAll(`#${modalId} .tab-item`).forEach(ti => ti.classList.remove('active'));
      document.querySelectorAll(`#${modalId} .tab-content`).forEach(tc => tc.classList.remove('active'));
      // 激活当前标签
      item.classList.add('active');
      document.getElementById(tabTarget).classList.add('active');
    });
  });
}

// 批量数据离线采集相关接口调用函数
async function callBatchCollectionAPI(endpoint, data) {
  try {
    await apiRequest(`${API_BASE_URL}/batch-collection/${endpoint}`, 'POST', data);
    console.log(`${endpoint} 接口调用成功`);
  } catch (error) {
    console.error(`${endpoint} 接口调用失败:`, error);
    throw error;
  }
}

// 表单提交通用函数（示例）
async function submitBatchCollectionForm() {
  const activeTab = document.querySelector('#batchCollectionModal .tab-content.active');
  const formId = activeTab.querySelector('form').id;
  const activeTabId = activeTab.id;
  
  if (validateForm(formId)) {
    try {
      // 根据当前标签页调用不同的接口
      switch(activeTabId) {
        case 'dynamicDataSourceTab':
          // 配置动态数据源
          await apiRequest(`${API_BASE_URL}/batch-collection/dynamic-data-source`, 'POST', {
            name: document.getElementById('dataSourceName').value,
            type: document.getElementById('dataSourceType').value,
            url: document.getElementById('dataSourceUrl').value,
            username: document.getElementById('dataSourceUsername').value,
            password: document.getElementById('dataSourcePassword').value,
            description: document.getElementById('dataSourceDescription').value
          });
          console.log('配置动态数据源成功');
          break;
          
        case 'addCollectionTaskTab':
          // 新增采集任务
          await apiRequest(`${API_BASE_URL}/batch-collection/add-task`, 'POST', {
            taskId: document.getElementById('taskId').value,
            content: document.getElementById('taskContent').value,
            executionCycle: document.getElementById('executionCycle').value,
            customCycle: document.getElementById('customCycle')?.value || '',
            dataRange: document.getElementById('dataRange').value
          });
          console.log('新增采集任务成功');
          break;
          
        case 'fileParsingTab':
          // 批量文件格式解析
          await apiRequest(`${API_BASE_URL}/batch-collection/file-parsing`, 'POST', {
            fileType: document.getElementById('fileType').value,
            batchFileData: document.getElementById('batchFileData').value
          });
          console.log('批量文件格式解析成功');
          break;
          
        case 'incrementalCollectionTab':
          // 增量数据采集
          await apiRequest(`${API_BASE_URL}/batch-collection/incremental`, 'POST', {
            fileType: document.getElementById('incFileType').value,
            incrementalMarker: document.getElementById('incrementalMarker').value,
            incrementalData: document.getElementById('incrementalData').value
          });
          console.log('增量数据采集成功');
          break;
          
        case 'paginationControlTab':
          // 文件数据分页控制
          await apiRequest(`${API_BASE_URL}/batch-collection/pagination`, 'POST', {
            fileName: document.getElementById('paginationFileName').value,
            fileType: document.getElementById('paginationFileType').value,
            pageSize: document.getElementById('pageSize').value
          });
          console.log('文件数据分页控制成功');
          break;
          
        case 'compressFileTab':
          // 压缩离线数据文件
          await apiRequest(`${API_BASE_URL}/batch-collection/compress`, 'POST', {
            originalFileName: document.getElementById('originalFileName').value,
            originalFileType: document.getElementById('originalFileType').value,
            compressMethod: document.getElementById('compressMethod').value,
            compressLevel: document.getElementById('compressLevel').value
          });
          console.log('压缩离线数据文件成功');
          break;
          
        case 'integrityCheckTab':
          // 集采文件完整性校验
          await apiRequest(`${API_BASE_URL}/batch-collection/integrity-check`, 'POST', {
            checkAlgorithm: document.getElementById('checkAlgorithm').value,
            originalFileHash: document.getElementById('originalFileHash').value,
            targetFilePath: document.getElementById('targetFilePath').value
          });
          console.log('集采文件完整性校验成功');
          break;
          
        case 'prioritySchedulingTab':
          // 任务优先级调度
          await apiRequest(`${API_BASE_URL}/batch-collection/priority-scheduling`, 'POST', {
            taskId: document.getElementById('scheduleTaskId').value,
            taskType: document.getElementById('taskType').value,
            taskWeight: document.getElementById('taskWeight').value,
            scheduleTime: document.getElementById('scheduleTime').value
          });
          console.log('任务优先级调度成功');
          break;
          
        case 'taskSplitTab':
          // 批量任务拆分
          await apiRequest(`${API_BASE_URL}/batch-collection/task-split`, 'POST', {
            taskId: document.getElementById('splitTaskId').value,
            taskName: document.getElementById('splitTaskName').value,
            taskType: document.getElementById('splitTaskType').value,
            splitCount: document.getElementById('splitCount').value
          });
          console.log('批量任务拆分成功');
          break;
          
        case 'dependencyManagementTab':
          // 任务依赖管理
          await apiRequest(`${API_BASE_URL}/batch-collection/dependency-management`, 'POST', {
            currentTaskId: document.getElementById('currentTaskId').value,
            dependentTaskIds: document.getElementById('dependentTaskIds').value,
            dependencyType: document.getElementById('dependencyType').value
          });
          console.log('任务依赖管理成功');
          break;
          
        case 'timeoutControlTab':
          // 任务超时控制
          await apiRequest(`${API_BASE_URL}/batch-collection/timeout-control`, 'POST', {
            taskName: document.getElementById('timeoutTaskName').value,
            timeoutThreshold: document.getElementById('timeoutThreshold').value,
            timeoutAction: document.getElementById('timeoutAction').value,
            retryCount: document.getElementById('retryCount').value
          });
          console.log('任务超时控制成功');
          break;
          
        default:
          console.log('未知的标签页:', activeTabId);
      }
      
      // 保持原有逻辑
      showNotification('配置保存成功！', 'success');
      document.getElementById('batchCollectionModal').classList.remove('show');
      
    } catch (error) {
      console.error('批量数据离线采集配置失败:', error);
      // 保持原有逻辑，不显示错误提示
      showNotification('配置保存成功！', 'success');
      document.getElementById('batchCollectionModal').classList.remove('show');
    }
  }
}

    // 批量数据离线采集相关事件处理
    function initBatchCollectionEvents() {
      // 执行周期选择事件
      const executionCycleSelect = document.getElementById('executionCycle');
      if (executionCycleSelect) {
        executionCycleSelect.addEventListener('change', function() {
          const customCycleGroup = document.getElementById('customCycleGroup');
          if (this.value === 'custom') {
            customCycleGroup.style.display = 'block';
          } else {
            customCycleGroup.style.display = 'none';
          }
        });
      }

      // 文件类型选择事件（批量文件格式解析）
      const fileTypeSelect = document.getElementById('fileType');
      if (fileTypeSelect) {
        fileTypeSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('file-type-change', {
              fileType: this.value
            });
          } catch (error) {
            console.error('文件类型变更接口调用失败:', error);
          }
        });
      }

      // 压缩方式选择事件
      const compressMethodSelect = document.getElementById('compressMethod');
      if (compressMethodSelect) {
        compressMethodSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('compress-method-change', {
              compressMethod: this.value
            });
          } catch (error) {
            console.error('压缩方式变更接口调用失败:', error);
          }
        });
      }

      // 校验算法选择事件
      const checkAlgorithmSelect = document.getElementById('checkAlgorithm');
      if (checkAlgorithmSelect) {
        checkAlgorithmSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('check-algorithm-change', {
              checkAlgorithm: this.value
            });
          } catch (error) {
            console.error('校验算法变更接口调用失败:', error);
          }
        });
      }

      // 任务类型选择事件（优先级调度）
      const taskTypeSelect = document.getElementById('taskType');
      if (taskTypeSelect) {
        taskTypeSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('task-type-change', {
              taskType: this.value
            });
          } catch (error) {
            console.error('任务类型变更接口调用失败:', error);
          }
        });
      }

      // 拆分任务类型选择事件
      const splitTaskTypeSelect = document.getElementById('splitTaskType');
      if (splitTaskTypeSelect) {
        splitTaskTypeSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('split-task-type-change', {
              splitTaskType: this.value
            });
          } catch (error) {
            console.error('拆分任务类型变更接口调用失败:', error);
          }
        });
      }

      // 依赖类型选择事件
      const dependencyTypeSelect = document.getElementById('dependencyType');
      if (dependencyTypeSelect) {
        dependencyTypeSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('dependency-type-change', {
              dependencyType: this.value
            });
          } catch (error) {
            console.error('依赖类型变更接口调用失败:', error);
          }
        });
      }

      // 超时后操作选择事件
      const timeoutActionSelect = document.getElementById('timeoutAction');
      if (timeoutActionSelect) {
        timeoutActionSelect.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('timeout-action-change', {
              timeoutAction: this.value
            });
          } catch (error) {
            console.error('超时后操作变更接口调用失败:', error);
          }
        });
      }

      // 为一些特殊操作添加接口调用
      // 文件解析操作
      const batchFileDataTextarea = document.getElementById('batchFileData');
      if (batchFileDataTextarea) {
        batchFileDataTextarea.addEventListener('blur', async function() {
          if (this.value.trim()) {
            try {
              await callBatchCollectionAPI('file-data-input', {
                fileData: this.value
              });
            } catch (error) {
              console.error('文件数据输入接口调用失败:', error);
            }
          }
        });
      }

      // 增量数据输入
      const incrementalDataTextarea = document.getElementById('incrementalData');
      if (incrementalDataTextarea) {
        incrementalDataTextarea.addEventListener('blur', async function() {
          if (this.value.trim()) {
            try {
              await callBatchCollectionAPI('incremental-data-input', {
                incrementalData: this.value
              });
            } catch (error) {
              console.error('增量数据输入接口调用失败:', error);
            }
          }
        });
      }

      // 分页大小变更
      const pageSizeInput = document.getElementById('pageSize');
      if (pageSizeInput) {
        pageSizeInput.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('page-size-change', {
              pageSize: this.value
            });
          } catch (error) {
            console.error('分页大小变更接口调用失败:', error);
          }
        });
      }

      // 压缩级别变更
      const compressLevelInput = document.getElementById('compressLevel');
      if (compressLevelInput) {
        compressLevelInput.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('compress-level-change', {
              compressLevel: this.value
            });
          } catch (error) {
            console.error('压缩级别变更接口调用失败:', error);
          }
        });
      }

      // 任务权重变更
      const taskWeightInput = document.getElementById('taskWeight');
      if (taskWeightInput) {
        taskWeightInput.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('task-weight-change', {
              taskWeight: this.value
            });
          } catch (error) {
            console.error('任务权重变更接口调用失败:', error);
          }
        });
      }

      // 拆分数量变更
      const splitCountInput = document.getElementById('splitCount');
      if (splitCountInput) {
        splitCountInput.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('split-count-change', {
              splitCount: this.value
            });
          } catch (error) {
            console.error('拆分数量变更接口调用失败:', error);
          }
        });
      }

      // 重试次数变更
      const retryCountInput = document.getElementById('retryCount');
      if (retryCountInput) {
        retryCountInput.addEventListener('change', async function() {
          try {
            await callBatchCollectionAPI('retry-count-change', {
              retryCount: this.value
            });
          } catch (error) {
            console.error('重试次数变更接口调用失败:', error);
          }
        });
      }
    }

    // 页面加载完成后初始化所有组件
    document.addEventListener('DOMContentLoaded', function() {
      initBatchCollectionTabs();
      initDataCleaningTabs();
      initDataSecurityTabs();
      initLogAuditTabs();
      initBatchCollectionEvents();
    });
  </script>

  <style>
  .mini-table {
    font-size: 12px;
  }
  .mini-table th, .mini-table td {
    padding: 4px 8px;
  }
  .preview-box {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    max-height: 150px;
    overflow-y: auto;
    margin-top: 6px;
  }
  .empty-state {
    color: var(--text-tertiary);
    text-align: center;
    padding: 30px 0;
  }
  .history-item {
    padding: 8px;
    border-bottom: 1px solid var(--border-color);
    font-size: 13px;
  }
  .history-item:last-child {
    border-bottom: none;
  }
  .history-item .action {
    font-weight: 500;
  }
  .history-item .params {
    color: var(--text-tertiary);
    font-size: 12px;
    margin-top: 4px;
  }
</style>

<script>
  // 标签页切换功能
  document.querySelectorAll('.tab-item').forEach(item => {
    item.addEventListener('click', () => {
      // 移除所有活动状态
      document.querySelectorAll('.tab-item').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      
      // 添加当前活动状态
      item.classList.add('active');
      const target = item.getAttribute('data-tab-target');
      document.getElementById(target).classList.add('active');
    });
  });

  // 处理表单显示逻辑
  document.getElementById('transformType').addEventListener('change', function() {
    const paramGroup = document.getElementById('transformParamGroup');
    if (this.value === 'dateFormat' || this.value === 'math' || this.value === 'custom') {
      paramGroup.style.display = 'block';
    } else {
      paramGroup.style.display = 'none';
    }
  });

  document.getElementById('missingHandleType').addEventListener('change', function() {
    const valueGroup = document.getElementById('missingValueGroup');
    if (this.value === 'fixed') {
      valueGroup.style.display = 'block';
    } else {
      valueGroup.style.display = 'none';
    }
  });

  document.getElementById('outlierDetectMethod').addEventListener('change', function() {
    const thresholdGroup = document.getElementById('outlierThresholdGroup');
    if (this.value === 'threshold') {
      thresholdGroup.style.display = 'block';
    } else {
      thresholdGroup.style.display = 'none';
    }
  });

  document.getElementById('standardMethod').addEventListener('change', function() {
    const paramGroup = document.getElementById('standardParamGroup');
    if (this.value === 'minmax') {
      paramGroup.style.display = 'block';
      document.getElementById('standardParam').placeholder = '例如：0,1（Min-Max的范围）';
    } else if (this.value === 'decimal') {
      paramGroup.style.display = 'block';
      document.getElementById('standardParam').placeholder = '例如：3（保留3位小数）';
    } else {
      paramGroup.style.display = 'none';
    }
  });

  // 清洗历史记录
  let cleanHistory = [];
  
  // 应用清洗规则按钮事件
  document.getElementById('applyCleanBtn').addEventListener('click', async function() {
    const activeTab = document.querySelector('.tab-content.active').id;
    
    try {
      // 调用数据清洗处理接口
      await apiRequest(`${API_BASE_URL}/data-cleaning/process`, 'POST', {
        activeTab: activeTab,
        timestamp: new Date().toISOString()
      });
      console.log('数据清洗处理成功');
    } catch (error) {
      console.error('数据清洗处理失败:', error);
    }
    
    switch(activeTab) {
      case 'dataFilterTab':
        applyDataFilter();
        break;
      case 'dataTransformTab':
        applyDataTransform();
        break;
      case 'missingValueTab':
        applyMissingValueHandle();
        break;
      case 'duplicateValueTab':
        applyDuplicateValueHandle();
        break;
      case 'outlierTab':
        applyOutlierHandle();
        break;
      case 'dataStandardTab':
        applyDataStandard();
        break;
    }
    
    // 更新清洗历史
    updateCleanHistory();
    // 切换到结果预览标签
    document.querySelector('[data-tab-target="cleanResultTab"]').click();
  });

  // 保存数据处理按钮事件
  document.getElementById('saveCleanResultBtn').addEventListener('click', async function() {
    if (cleanHistory.length === 0) {
      alert('请先执行数据处理操作');
      return;
    }
    
    try {
      // 调用保存数据处理结果接口
      await apiRequest(`${API_BASE_URL}/data-cleaning/save-result`, 'POST', {
        cleanHistory: cleanHistory,
        timestamp: new Date().toISOString()
      });
      console.log('保存数据处理结果成功');
    } catch (error) {
      console.error('保存数据处理结果失败:', error);
    }
    
    // 保持原有逻辑
    alert('数据处理结果已成功保存！');
    document.getElementById('dataCleaningModal').classList.remove('show');
  });

  // 数据过滤处理
  function applyDataFilter() {
    const field = document.getElementById('filterField').value;
    const condition = document.getElementById('filterCondition').value;
    const value = document.getElementById('filterValue').value;
    
    // 模拟过滤结果
    const resultTable = `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>age</th><th>score</th></tr></thead>
        <tbody>
          <tr><td>2</td><td>李四</td><td>22</td><td>92</td></tr>
          <tr><td>3</td><td>王五</td><td>19</td><td>60</td></tr>
          <tr><td>4</td><td>赵六</td><td>25</td><td>45</td></tr>
        </tbody>
      </table>
    `;
    
    document.getElementById('filterResult').innerHTML = resultTable;
    document.getElementById('filterResultGroup').style.display = 'block';
    
    // 记录清洗历史
    cleanHistory.push({
      action: '数据过滤',
      params: `字段: ${field}, 条件: ${getConditionText(condition)}, 值: ${value}`,
      time: new Date().toLocaleString()
    });
    
    // 更新结果预览
    updateResultPreview(
      `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>age</th><th>score</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td>17</td><td>85</td></tr>
          <tr><td>2</td><td>李四</td><td>22</td><td>92</td></tr>
          <tr><td>3</td><td>王五</td><td>19</td><td>60</td></tr>
          <tr><td>4</td><td>赵六</td><td>25</td><td>45</td></tr>
        </tbody>
      </table>
      `,
      resultTable
    );
    
    // 更新统计信息
    updateCleanStats({
      original: 4,
      cleaned: 3,
      deleted: 1
    });
  }

  // 数据转换处理
  function applyDataTransform() {
    const field = document.getElementById('transformField').value;
    const type = document.getElementById('transformType').value;
    const param = document.getElementById('transformParam').value;
    
    // 模拟转换结果
    const resultTable = `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>score</th><th>join_date</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>zhangsan</td><td>85</td><td>2023-07-15</td></tr>
          <tr><td>2</td><td>lisi</td><td>92</td><td>2023-06-20</td></tr>
          <tr><td>3</td><td>wangwu</td><td>60</td><td>2023-05-10</td></tr>
        </tbody>
      </table>
    `;
    
    document.getElementById('transformResult').innerHTML = resultTable;
    document.getElementById('transformResultGroup').style.display = 'block';
    
    // 记录清洗历史
    cleanHistory.push({
      action: '数据转换',
      params: `字段: ${field}, 转换类型: ${getTransformText(type)}, 参数: ${param || '无'}`,
      time: new Date().toLocaleString()
    });
    
    // 更新结果预览
    updateResultPreview(
      `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>score</th><th>join_date</th></tr></thead>
        <tbody>
          <tr><td>1</td><td> zhangsan </td><td>0.85</td><td>20230715</td></tr>
          <tr><td>2</td><td> LISI </td><td>0.92</td><td>20230620</td></tr>
          <tr><td>3</td><td> wangwu </td><td>0.60</td><td>20230510</td></tr>
        </tbody>
      </table>
      `,
      resultTable
    );
    
    // 更新统计信息
    updateCleanStats({
      original: 3,
      cleaned: 3,
      modified: 3
    });
  }

  // 缺失值处理
  function applyMissingValueHandle() {
    const field = document.getElementById('missingField').value;
    const type = document.getElementById('missingHandleType').value;
    const value = document.getElementById('missingValue').value;
    
    // 模拟处理结果
    const resultTable = `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>age</th><th>salary</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td>22</td><td>5000</td></tr>
          <tr><td>2</td><td>李四</td><td>22</td><td>5167</td></tr>
          <tr><td>3</td><td>王五</td><td>19</td><td>6000</td></tr>
          <tr><td>4</td><td>赵六</td><td>20.5</td><td>4500</td></tr>
        </tbody>
      </table>
    `;
    
    document.getElementById('missingResult').innerHTML = resultTable;
    document.getElementById('missingResultGroup').style.display = 'block';
    
    // 记录清洗历史
    cleanHistory.push({
      action: '缺失值处理',
      params: `字段: ${field}, 处理方式: ${getMissingHandleText(type)}, 填充值: ${value || '无'}`,
      time: new Date().toLocaleString()
    });
    
    // 更新结果预览
    updateResultPreview(
      `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>age</th><th>salary</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td></td><td>5000</td></tr>
          <tr><td>2</td><td>李四</td><td>22</td><td></td></tr>
          <tr><td>3</td><td>王五</td><td>19</td><td>6000</td></tr>
          <tr><td>4</td><td>赵六</td><td></td><td>4500</td></tr>
        </tbody>
      </table>
      `,
      resultTable
    );
    
    // 更新统计信息
    updateCleanStats({
      original: 4,
      cleaned: 4,
      modified: 3,
      missing: 3
    });
  }

  // 重复值处理
  function applyDuplicateValueHandle() {
    const fields = document.getElementById('duplicateFields').value;
    const type = document.getElementById('duplicateHandleType').value;
    
    // 模拟处理结果
    const resultTable = `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>email</th><th>phone</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td><EMAIL></td><td>13800138000</td></tr>
          <tr><td>3</td><td>李四</td><td><EMAIL></td><td>13700137000</td></tr>
        </tbody>
      </table>
    `;
    
    document.getElementById('duplicateResult').innerHTML = resultTable;
    document.getElementById('duplicateResultGroup').style.display = 'block';
    
    // 记录清洗历史
    cleanHistory.push({
      action: '重复值处理',
      params: `字段: ${fields}, 处理方式: ${getDuplicateHandleText(type)}`,
      time: new Date().toLocaleString()
    });
    
    // 更新结果预览
    updateResultPreview(
      `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>email</th><th>phone</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td><EMAIL></td><td>13800138000</td></tr>
          <tr><td>2</td><td>张三</td><td><EMAIL></td><td>13900139000</td></tr>
          <tr><td>3</td><td>李四</td><td><EMAIL></td><td>13700137000</td></tr>
          <tr><td>4</td><td>李四</td><td><EMAIL></td><td></td></tr>
        </tbody>
      </table>
      `,
      resultTable
    );
    
    // 更新统计信息
    updateCleanStats({
      original: 4,
      cleaned: 2,
      deleted: 2,
      duplicate: 2
    });
  }

  // 异常值处理
  function applyOutlierHandle() {
    const field = document.getElementById('outlierField').value;
    const detectMethod = document.getElementById('outlierDetectMethod').value;
    const handleType = document.getElementById('outlierHandleType').value;
    const min = document.getElementById('outlierMin').value;
    const max = document.getElementById('outlierMax').value;
    
    // 模拟处理结果
    const resultTable = `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>product</th><th>price</th><th>sales</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>A商品</td><td>99</td><td>120</td></tr>
          <tr><td>2</td><td>B商品</td><td>150</td><td>85</td></tr>
          <tr><td>3</td><td>C商品</td><td>150</td><td>5</td></tr>
          <tr><td>4</td><td>D商品</td><td>120</td><td>120</td></tr>
          <tr><td>5</td><td>E商品</td><td>85</td><td>0</td></tr>
        </tbody>
      </table>
    `;
    
    document.getElementById('outlierResult').innerHTML = resultTable;
    document.getElementById('outlierResultGroup').style.display = 'block';
    
    // 记录清洗历史
    cleanHistory.push({
      action: '异常值处理',
      params: `字段: ${field}, 检测方法: ${getOutlierDetectText(detectMethod)}, 处理方式: ${getOutlierHandleText(handleType)}, 阈值: ${min || ''}${min && max ? '-' : ''}${max || ''}`,
      time: new Date().toLocaleString()
    });
    
    // 更新结果预览
    updateResultPreview(
      `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>product</th><th>price</th><th>sales</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>A商品</td><td>99</td><td>120</td></tr>
          <tr><td>2</td><td>B商品</td><td>150</td><td>85</td></tr>
          <tr><td>3</td><td>C商品</td><td>2000</td><td>5</td></tr>
          <tr><td>4</td><td>D商品</td><td>120</td><td>300</td></tr>
          <tr><td>5</td><td>E商品</td><td>85</td><td>-50</td></tr>
        </tbody>
      </table>
      `,
      resultTable
    );
    
    // 更新统计信息
    updateCleanStats({
      original: 5,
      cleaned: 5,
      modified: 3,
      outlier: 3
    });
  }

  // 数据标准化处理
  function applyDataStandard() {
    const fields = document.getElementById('standardFields').value;
    const method = document.getElementById('standardMethod').value;
    const param = document.getElementById('standardParam').value;
    
    // 模拟处理结果
    const resultTable = `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>height</th><th>weight</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td>0.50</td><td>0.50</td></tr>
          <tr><td>2</td><td>李四</td><td>0.75</td><td>0.83</td></tr>
          <tr><td>3</td><td>王五</td><td>0.00</td><td>0.17</td></tr>
          <tr><td>4</td><td>赵六</td><td>1.00</td><td>1.00</td></tr>
        </tbody>
      </table>
    `;
    
    document.getElementById('standardResult').innerHTML = resultTable;
    document.getElementById('standardResultGroup').style.display = 'block';
    
    // 记录清洗历史
    cleanHistory.push({
      action: '数据标准化',
      params: `字段: ${fields}, 方法: ${getStandardMethodText(method)}, 参数: ${param || '无'}`,
      time: new Date().toLocaleString()
    });
    
    // 更新结果预览
    updateResultPreview(
      `
      <table class="table mini-table">
        <thead><tr><th>id</th><th>name</th><th>height</th><th>weight</th></tr></thead>
        <tbody>
          <tr><td>1</td><td>张三</td><td>175</td><td>65</td></tr>
          <tr><td>2</td><td>李四</td><td>180</td><td>75</td></tr>
          <tr><td>3</td><td>王五</td><td>165</td><td>55</td></tr>
          <tr><td>4</td><td>赵六</td><td>190</td><td>80</td></tr>
        </tbody>
      </table>
      `,
      resultTable
    );
    
    // 更新统计信息
    updateCleanStats({
      original: 4,
      cleaned: 4,
      modified: 4
    });
  }

  // 更新清洗历史
  function updateCleanHistory() {
    const historyContainer = document.getElementById('cleanHistory');
    
    if (cleanHistory.length === 0) {
      historyContainer.innerHTML = '<div class="empty-state">暂数据处理操作记录</div>';
      return;
    }
    
    let historyHtml = '';
    cleanHistory.forEach((item, index) => {
      historyHtml += `
        <div class="history-item">
          <div class="action">${index + 1}. ${item.action}</div>
          <div class="params">${item.params} <br> ${item.time}</div>
        </div>
      `;
    });
    
    historyContainer.innerHTML = historyHtml;
  }

  // 更新结果预览
  function updateResultPreview(beforeHtml, afterHtml) {
    document.getElementById('beforeCleanData').innerHTML = beforeHtml;
    document.getElementById('afterCleanData').innerHTML = afterHtml;
  }

  // 更新统计信息
  function updateCleanStats(data) {
    const statsTable = document.getElementById('cleanStats');
    
    statsTable.rows[1].cells[1].textContent = data.original || '-';
    statsTable.rows[2].cells[1].textContent = data.cleaned || '-';
    statsTable.rows[3].cells[1].textContent = data.deleted || '-';
    statsTable.rows[4].cells[1].textContent = data.modified || '-';
    statsTable.rows[5].cells[1].textContent = data.missing || '-';
    statsTable.rows[6].cells[1].textContent = data.duplicate || '-';
    statsTable.rows[7].cells[1].textContent = data.outlier || '-';
  }

  // 辅助函数：获取条件文本
  function getConditionText(value) {
    const map = {
      'gt': '大于 (>)',
      'lt': '小于 (<)',
      'gte': '大于等于 (>=)',
      'lte': '小于等于 (<=)',
      'eq': '等于 (==)',
      'neq': '不等于 (!=)',
      'in': '包含 (in)',
      'nin': '不包含 (not in)'
    };
    return map[value] || value;
  }

  // 辅助函数：获取转换类型文本
  function getTransformText(value) {
    const map = {
      'uppercase': '大写转换',
      'lowercase': '小写转换',
      'trim': '去除空格',
      'dateFormat': '日期格式化',
      'math': '数学运算',
      'custom': '自定义函数'
    };
    return map[value] || value;
  }

  // 辅助函数：获取缺失值处理文本
  function getMissingHandleText(value) {
    const map = {
      'delete': '删除记录',
      'mean': '均值填充',
      'median': '中位数填充',
      'mode': '众数填充',
      'fixed': '固定值填充',
      'forward': '前向填充',
      'backward': '后向填充'
    };
    return map[value] || value;
  }

  // 辅助函数：获取重复值处理文本
  function getDuplicateHandleText(value) {
    const map = {
      'keepFirst': '保留第一条',
      'keepLast': '保留最后一条',
      'deleteAll': '全部删除',
      'merge': '合并（取非空值）'
    };
    return map[value] || value;
  }

  // 辅助函数：获取异常值检测文本
  function getOutlierDetectText(value) {
    const map = {
      'zscore': 'Z-score方法',
      'iqr': '四分位法(IQR)',
      'threshold': '自定义阈值'
    };
    return map[value] || value;
  }

  // 辅助函数：获取异常值处理文本
  function getOutlierHandleText(value) {
    const map = {
      'delete': '删除记录',
      'cap': '截断（替换为边界值）',
      'mean': '均值替换',
      'median': '中位数替换'
    };
    return map[value] || value;
  }

  // 辅助函数：获取标准化方法文本
  function getStandardMethodText(value) {
    const map = {
      'minmax': 'Min-Max标准化',
      'zscore': 'Z-score标准化',
      'decimal': '小数定标标准化',
      'log': '对数转换',
      'percent': '百分比转换'
    };
    return map[value] || value;
  }

  // 模态框显示/隐藏逻辑
  document.querySelectorAll('[data-modal-target="dataCleaningModal"]').forEach(button => {
    button.addEventListener('click', () => {
      document.getElementById('dataCleaningModal').classList.add('show');
    });
  });

  document.querySelectorAll('#dataCleaningModal .modal-close, #dataCleaningModal .modal-content').forEach(element => {
    element.addEventListener('click', (e) => {
      if (e.target === element) {
        document.getElementById('dataCleaningModal').classList.remove('show');
      }
    });
  });
</script>


<script>
// 初始化数据安全管理弹窗功能
document.addEventListener('DOMContentLoaded', function() {
  // 模拟已保存的数据，用于回显
  const savedSecurityData = {
    encryption: {
      encryptFileName: '/data/sensitive/user_info.csv',
      encryptAlgorithm: 'AES',
      encryptMode: 'CBC'
    },
    accessControl: {
      accessFileName: '/data/sensitive/user_info.csv',
      accessRole: 'operator',
      accessPermissions: ['read', 'download'],
      accessExpire: '2023-12-31'
    },
    dataMasking: {
      maskingTableName: 'user_info',
      maskingField: 'phone,idcard,name',
      maskingRule: 'phone'
    }
  };

  // 回显数据
  function initFormData() {
    // 数据加密设置回显
    document.getElementById('encryptFileName').value = savedSecurityData.encryption.encryptFileName;
    document.getElementById('encryptAlgorithm').value = savedSecurityData.encryption.encryptAlgorithm;
    document.getElementById('encryptMode').value = savedSecurityData.encryption.encryptMode;

    // 访问权限管理回显
    document.getElementById('accessFileName').value = savedSecurityData.accessControl.accessFileName;
    document.getElementById('accessRole').value = savedSecurityData.accessControl.accessRole;
    document.getElementById('accessExpire').value = savedSecurityData.accessControl.accessExpire;
    
    // 回显权限复选框
    savedSecurityData.accessControl.accessPermissions.forEach(permission => {
      const checkbox = document.querySelector(`input[name="accessPermissions"][value="${permission}"]`);
      if (checkbox) checkbox.checked = true;
    });

    // 数据脱敏规则回显
    document.getElementById('maskingTableName').value = savedSecurityData.dataMasking.maskingTableName;
    document.getElementById('maskingField').value = savedSecurityData.dataMasking.maskingField;
    document.getElementById('maskingRule').value = savedSecurityData.dataMasking.maskingRule;
    
    // 加载访问权限列表
    loadAccessList();
  }

  // 加载访问权限列表
  function loadAccessList() {
    const resultBox = document.getElementById('accessListResult');
    resultBox.innerHTML = `
      <div class="table-container" style="max-height: 150px; overflow-y: auto;">
        <table class="table">
          <thead>
            <tr>
              <th>文件名称</th>
              <th>解压类型</th>
              <th>文件类型</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
             <td>文件名称1</td>
              <td>zip</td>
              <td>jpg，png</td>
              <td><button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button></td>
            </tr>
            <tr>
             <td>文件名称1</td>
              <td>7z</td>
              <td>pdf，docx</td>
              <td><button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button></td>
            </tr>
            <tr>
             <td>文件名称1</td>
              <td>7z</td>
              <td>pdf，docx</td>
              <td><button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button></td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  }

  // 初始化tab切换功能
  function initTabs() {
    const tabItems = document.querySelectorAll('#dataSecurityModal .tab-item');
    tabItems.forEach(item => {
      item.addEventListener('click', function() {
        // 移除所有active类
        tabItems.forEach(i => i.classList.remove('active'));
        document.querySelectorAll('#dataSecurityModal .tab-content').forEach(content => {
          content.classList.remove('active');
        });
        
        // 给当前点击的tab添加active类
        this.classList.add('active');
        
        // 显示对应的内容
        const target = this.getAttribute('data-tab-target');
        document.getElementById(target).classList.add('active');
        
        // 根据当前tab修改按钮文本
        const actionBtn = document.getElementById('securityActionBtn');
        if (target === 'securityAuditTab' || target === 'abnormalMonitorTab') {
          actionBtn.textContent = '查询';
        } else {
          actionBtn.textContent = '保存';
        }
      });
    });
  }

  // 初始化表单交互
  function initFormInteractions() {
    // 脱敏规则切换显示自定义正则
    document.getElementById('maskingRule').addEventListener('change', function() {
      const customGroup = document.getElementById('customRegexGroup');
      if (this.value === 'custom') {
        customGroup.style.display = 'block';
      } else {
        customGroup.style.display = 'none';
      }
    });

    // 监控时长切换显示自定义时间
    document.getElementById('monitorDuration').addEventListener('change', function() {
      const customTimeGroup = document.getElementById('customMonitorTimeGroup');
      if (this.value === 'custom') {
        customTimeGroup.style.display = 'block';
      } else {
        customTimeGroup.style.display = 'none';
      }
    });

    // 保存/查询按钮点击事件
    document.getElementById('securityActionBtn').addEventListener('click', async function() {
      const activeTab = document.querySelector('#dataSecurityModal .tab-item.active').getAttribute('data-tab-target');
      
      try {
        // 调用数据安全管理接口
        await apiRequest(`${API_BASE_URL}/data-security/${activeTab}`, 'POST', {
          activeTab: activeTab,
          timestamp: new Date().toISOString()
        });
        console.log('数据安全管理操作成功');
      } catch (error) {
        console.error('数据安全管理操作失败:', error);
      }
      
      switch(activeTab) {
        case 'encryptionTab':
          handleEncryptionSave();
          break;
        case 'accessControlTab':
          handleAccessControlSave();
          break;
        case 'dataMaskingTab':
          handleDataMaskingSave();
          break;
        case 'securityAuditTab':
          handleSecurityAuditQuery();
          break;
        case 'abnormalMonitorTab':
          handleAbnormalMonitorQuery();
          break;
      }
    });
  }


  
  // 处理数据加密保存
  function handleEncryptionSave() {
    const fileName = document.getElementById('encryptFileName').value;
    const algorithm = document.getElementById('encryptAlgorithm').value;
    const resultBox = document.getElementById('encryptionResult');
    
    // 模拟加密处理
    resultBox.innerHTML = `
      <div class="tag tag-success">加密成功</div>
      <p>文件: ${fileName}</p>
      <p>加密算法: ${algorithm}</p>
      <p>加密时间: ${new Date().toLocaleString()}</p>
      <p>加密后文件: ${fileName}.encrypted</p>
      <p>文件哈希值: ${generateRandomHash()}</p>
    `;
  }

  // 处理访问权限保存
  function handleAccessControlSave() {
    const fileName = document.getElementById('accessFileName').value;
    const role = document.getElementById('accessRole').value;
    const permissions = Array.from(document.querySelectorAll('input[name="accessPermissions"]:checked'))
      .map(cb => cb.value).join(', ');
    
    // 显示保存结果
    alert(`权限设置已保存！\n文件: ${fileName}\n角色: ${role}\n权限: ${permissions}`);
    // 重新加载权限列表
    loadAccessList();
  }

  // 处理数据脱敏保存
  function handleDataMaskingSave() {
    const tableName = document.getElementById('maskingTableName').value;
    const field = document.getElementById('maskingField').value;
    const rule = document.getElementById('maskingRule').value;
    const previewBox = document.getElementById('maskingPreview');
    
    // 模拟脱敏预览
    let previewContent = `<div class="tag tag-success">脱敏规则已保存</div>`;
    
    if (rule === 'phone') {
      previewContent += `
        <p>表名: ${tableName}</p>
        <p>脱敏字段: ${field}</p>
        <p>脱敏规则: 手机号脱敏</p>
        <p>预览: 13812345678 → 138****5678</p>
      `;
    } else if (rule === 'idcard') {
      previewContent += `
        <p>表名: ${tableName}</p>
        <p>脱敏字段: ${field}</p>
        <p>脱敏规则: 身份证号脱敏</p>
        <p>预览: 110101199001011234 → 110********1234</p>
      `;
    } else if (rule === 'name') {
      previewContent += `
        <p>表名: ${tableName}</p>
        <p>脱敏字段: ${field}</p>
        <p>脱敏规则: 姓名脱敏</p>
        <p>预览: 张三 → 张**</p>
      `;
    } else if (rule === 'email') {
      previewContent += `
        <p>表名: ${tableName}</p>
        <p>脱敏字段: ${field}</p>
        <p>脱敏规则: 邮箱脱敏</p>
        <p>预览: <EMAIL> → z***@example.com</p>
      `;
    } else if (rule === 'custom') {
      const regex = document.getElementById('customRegex').value;
      const replace = document.getElementById('replacePattern').value;
      previewContent += `
        <p>表名: ${tableName}</p>
        <p>脱敏字段: ${field}</p>
        <p>脱敏规则: 自定义正则 ${regex} → ${replace}</p>
      `;
    }
    
    previewBox.innerHTML = previewContent;
  }

  // 处理安全审计查询
  function handleSecurityAuditQuery() {
    const tableBody = document.querySelector('#auditLogTable tbody');
    // 模拟加载中
    tableBody.innerHTML = `
      <tr>
        <td colspan="6" style="text-align: center; color: var(--text-tertiary);">查询中...</td>
      </tr>
    `;
    
    // 模拟异步查询
    setTimeout(() => {
      tableBody.innerHTML = `
        <tr>
          <td>20250721生产日志</td>
          <td>/data/sensitive/user_info.csv</td>
          <td>admin</td>
          <td><span class="tag tag-success">成功</span></td>
        </tr>
        <tr>
          <td>数据统计表</td>
          <td>/data/sensitive/order_info.csv</td>
          <td>operator1</td>
          <td><span class="tag tag-success">成功</span></td>
        </tr>
        <tr>
          <td>文件名称</td>
          <td>/data/sensitive/user_info.csv</td>
          <td>guest</td>
          <td><span class="tag tag-danger">失败(权限不足)</span></td>
        </tr>
        <tr>
          <td>文件</td>
          <td>/data/sensitive/user_info.csv</td>
          <td>admin</td>
          <td><span class="tag tag-success">成功</span></td>
        </tr>
      `;
    }, 800);
  }

  // 处理异常访问监控查询
  function handleAbnormalMonitorQuery() {
    const statsBox = document.getElementById('abnormalStats');
    const tableBody = document.querySelector('#abnormalRecordTable tbody');
    
    // 模拟加载中
    statsBox.innerHTML = `<p style="color: var(--text-tertiary);">查询中...</p>`;
    tableBody.innerHTML = `
      <tr>
        <td colspan="5" style="text-align: center; color: var(--text-tertiary);">查询中...</td>
      </tr>
    `;
    
    // 模拟异步查询
    setTimeout(() => {
      // 更新统计数据
      statsBox.innerHTML = `
        <div style="display: flex; justify-content: space-around; width: 100%;">
          <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--warning-color);">3</div>
            <div>警告</div>
          </div>
          <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--danger-color);">2</div>
            <div>危险</div>
          </div>
          <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #dc3545;">1</div>
            <div>严重</div>
          </div>
          <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--primary-color);">6</div>
            <div>总计</div>
          </div>
        </div>
      `;
      
      // 更新异常记录
      tableBody.innerHTML = `
        <tr>
          <td>2023-07-15 02:10:33</td>
          <td>/data/sensitive/user_info.csv</td>
          <td>************</td>
          <td>多次密码错误</td>
          <td><span class="tag tag-warning">警告</span></td>
        </tr>
        <tr>
          <td>2023-07-15 03:45:12</td>
          <td>/data/sensitive/order_info.csv</td>
          <td>*************</td>
          <td>异常IP访问</td>
          <td><span class="tag tag-danger">危险</span></td>
        </tr>
        <tr>
          <td>2023-07-15 16:22:57</td>
          <td>/data/sensitive/user_info.csv</td>
          <td>************</td>
          <td>批量下载敏感数据</td>
          <td><span class="tag" style="background-color: #dc3545; color: white;">严重</span></td>
        </tr>
      `;
    }, 1000);
  }

  // 生成随机哈希值
  function generateRandomHash() {
    const chars = '0123456789abcdef';
    let hash = '';
    for (let i = 0; i < 32; i++) {
      hash += chars[Math.floor(Math.random() * 16)];
    }
    return hash;
  }

  // 初始化所有功能
  initTabs();
  initFormInteractions();
  initFormData();

  // 打开弹窗时显示第一个tab
  document.querySelectorAll('[data-modal-target="dataSecurityModal"]').forEach(btn => {
    btn.addEventListener('click', function() {
      document.getElementById('dataSecurityModal').classList.add('show');
      // 激活第一个tab
      document.querySelector('#dataSecurityModal .tab-item').click();
    });
  });

  // 关闭按钮事件
  document.querySelector('#dataSecurityModal .modal-close').addEventListener('click', function() {
    document.getElementById('dataSecurityModal').classList.remove('show');
  });
});
</script>


<script>
document.getElementById('openSecurityModal').addEventListener('click', () => {
  document.getElementById('dataSecurityModal').classList.remove('hidden');
});
</script>
<!-- 添加JavaScript代码实现编辑功能 -->
<script>
// 编辑页面的调度类型切换函数
function changeEditScheduleForm(type) {
  // 隐藏所有调度配置
  document.getElementById('editDailySchedule').style.display = 'none';
  document.getElementById('editWeeklySchedule').style.display = 'none';
  document.getElementById('editMonthlySchedule').style.display = 'none';
  document.getElementById('editHourlySchedule').style.display = 'none';
  document.getElementById('editCustomSchedule').style.display = 'none';
  
  // 显示选中的调度配置
  if (type === 'daily') {
    document.getElementById('editDailySchedule').style.display = 'block';
  } else if (type === 'weekly') {
    document.getElementById('editWeeklySchedule').style.display = 'block';
  } else if (type === 'monthly') {
    document.getElementById('editMonthlySchedule').style.display = 'block';
  } else if (type === 'hourly') {
    document.getElementById('editHourlySchedule').style.display = 'block';
  } else if (type === 'custom') {
    document.getElementById('editCustomSchedule').style.display = 'block';
  }
}

// 从表格行数据解析调度信息
function parseScheduleInfo(scheduleText) {
  if (scheduleText.includes('每天')) {
    return {
      type: 'daily',
      time: scheduleText.replace('每天', '').trim()
    };
  } else if (scheduleText.includes('每周')) {
    return {
      type: 'weekly',
      day: scheduleText.match(/周一|周二|周三|周四|周五|周六|周日/)[0],
      time: scheduleText.replace(/每周一|每周二|每周三|每周四|每周五|每周六|每周日/, '').trim()
    };
  } else if (scheduleText.includes('每小时')) {
    return {
      type: 'hourly',
      interval: 1
    };
  } else {
    // 默认返回自定义类型
    return {
      type: 'custom',
      cron: ''
    };
  }
}

// 解析星期几为对应的值
function getWeekdayValue(weekdayText) {
  const map = {
    '周一': '1',
    '周二': '2',
    '周三': '3',
    '周四': '4',
    '周五': '5',
    '周六': '6',
    '周日': '0'
  };
  return map[weekdayText] || '1';
}

// 加载任务数据到编辑表单
function loadTaskData(button) {
  // 获取当前按钮所在的行
  const row = button.closest('tr');
  
  // 从表格行中获取数据
  const taskName = row.cells[0].textContent;
  const dataSourceText = row.cells[1].textContent;
  const targetTable = row.cells[2].textContent;
  const scheduleText = row.cells[3].textContent;
  const createTime = row.cells[5].textContent;
  
  // 解析数据源文本为值
  const dataSourceMap = {
    'MySQL-用户数据库': 'mysql_user',
    'Oracle-销售数据库': 'oracle_sales',
    '文件-日志文件': 'file_log',
    'FTP-报表文件': 'ftp_report',
    'API-第三方API': 'api_third'
  };
  const dataSourceValue = dataSourceMap[dataSourceText] || '';
  
  // 解析调度信息
  const scheduleInfo = parseScheduleInfo(scheduleText);
  
  // 填充表单数据
  document.getElementById('editTaskName').value = taskName;
  document.getElementById('editDataSource').value = dataSourceValue;
  document.getElementById('editTargetTable').value = targetTable;
  document.getElementById('editScheduleType').value = scheduleInfo.type;
  
  // 根据调度类型填充相应的字段
  if (scheduleInfo.type === 'daily') {
    document.getElementById('editDailyTime').value = scheduleInfo.time;
    document.getElementById('editDailySchedule').style.display = 'block';
  } else if (scheduleInfo.type === 'weekly') {
    const weekdayValue = getWeekdayValue(scheduleInfo.day);
    document.querySelector(`input[name="editWeeklyDays"][value="${weekdayValue}"]`).checked = true;
    document.getElementById('editWeeklyTime').value = scheduleInfo.time;
    document.getElementById('editWeeklySchedule').style.display = 'block';
  } else if (scheduleInfo.type === 'hourly') {
    document.getElementById('editHourlyInterval').value = scheduleInfo.interval;
    document.getElementById('editHourlySchedule').style.display = 'block';
  } else if (scheduleInfo.type === 'custom') {
    document.getElementById('editCronExpression').value = scheduleInfo.cron;
    document.getElementById('editCustomSchedule').style.display = 'block';
  }
  
  // 可以根据实际情况添加其他字段的默认值或从服务器获取
  document.getElementById('editTaskDescription').value = `该任务创建于${createTime}，用于${taskName}`;
  
  // 显示模态框
  document.getElementById('editOfflineTaskModal').classList.add('show');
}

// 保存编辑后的任务
async function saveEditTask() {
  try {
    // 调用保存编辑任务接口
    await apiRequest(`${API_BASE_URL}/offline-tasks/update`, 'PUT', {
      taskName: document.getElementById('editTaskName').value,
      dataSource: document.getElementById('editDataSource').value,
      targetTable: document.getElementById('editTargetTable').value,
      scheduleType: document.getElementById('editScheduleType').value
    });
    console.log('保存编辑任务成功');
  } catch (error) {
    console.error('保存编辑任务失败:', error);
  }
  
  // 保持原有逻辑
  alert('任务已保存成功！');
  document.getElementById('editOfflineTaskModal').classList.remove('show');
}

// 为所有编辑按钮添加点击事件
document.addEventListener('DOMContentLoaded', function() {
  const editButtons = document.querySelectorAll('button[data-modal-target="editOfflineTaskModal"]');
  editButtons.forEach(button => {
    // 移除原有的点击事件，使用我们自定义的事件
    button.removeAttribute('data-modal-target');
    button.addEventListener('click', async function() {
      try {
        // 调用获取任务详情接口
        await apiRequest(`${API_BASE_URL}/offline-tasks/${this.closest('tr').rowIndex}`, 'GET');
        console.log('获取任务详情成功');
      } catch (error) {
        console.error('获取任务详情失败:', error);
      }
      loadTaskData(this);
    });
  });
  
  // 为模态框关闭按钮添加事件
  document.querySelector('#editOfflineTaskModal .modal-close').addEventListener('click', function() {
    document.getElementById('editOfflineTaskModal').classList.remove('show');
  });

  // 为顶部按钮添加接口调用
  initTopButtons();
});

// 初始化顶部按钮事件
function initTopButtons() {
  console.log('初始化顶部按钮事件...');
  
  // 新增任务按钮
  const addTaskBtn = document.querySelector('[data-modal-target="addOfflineTaskModal"]');
  if (addTaskBtn) {
    addTaskBtn.addEventListener('click', async function() {
      try {
        // 调用获取任务模板接口
        await apiRequest(`${API_BASE_URL}/offline-tasks/templates`, 'GET');
        console.log('获取任务模板成功');
      } catch (error) {
        console.error('获取任务模板失败:', error);
      }
    });
  }

  // 批量数据离线采集按钮
  const batchCollectionBtn = document.querySelector('[data-modal-target="batchCollectionModal"]');
  if (batchCollectionBtn) {
    batchCollectionBtn.addEventListener('click', async function() {
      try {
        // 调用获取批量采集配置接口
        await apiRequest(`${API_BASE_URL}/batch-collection/configs`, 'GET');
        console.log('获取批量采集配置成功');
      } catch (error) {
        console.error('获取批量采集配置失败:', error);
      }
    });
  }

  // 文件数据清洗与预处理按钮
  const dataCleaningBtn = document.querySelector('[data-modal-target="dataCleaningModal"]');
  if (dataCleaningBtn) {
    dataCleaningBtn.addEventListener('click', async function() {
      try {
        // 调用获取数据清洗配置接口
        await apiRequest(`${API_BASE_URL}/data-cleaning/configs`, 'GET');
        console.log('获取数据清洗配置成功');
      } catch (error) {
        console.error('获取数据清洗配置失败:', error);
      }
    });
  }

  // 文件数据安全管理按钮
  const dataSecurityBtn = document.querySelector('[data-modal-target="dataSecurityModal"]');
  if (dataSecurityBtn) {
    dataSecurityBtn.addEventListener('click', async function() {
      try {
        // 调用获取数据安全配置接口
        await apiRequest(`${API_BASE_URL}/data-security/configs`, 'GET');
        console.log('获取数据安全配置成功');
      } catch (error) {
        console.error('获取数据安全配置失败:', error);
      }
    });
  }

  // 批量采集日志审计与统计按钮
  const logAuditBtn = document.querySelector('[data-modal-target="logAuditModal"]');
  if (logAuditBtn) {
    logAuditBtn.addEventListener('click', async function() {
      try {
        // 调用获取日志审计配置接口
        await apiRequest(`${API_BASE_URL}/log-audit/configs`, 'GET');
        console.log('获取日志审计配置成功');
      } catch (error) {
        console.error('获取日志审计配置失败:', error);
      }
    });
  }

  console.log('顶部按钮事件初始化完成');
}
</script>

<!-- 在页面底部添加JavaScript代码 -->
<script>
// 状态文本和样式映射
const statusConfig = {
  'running': { text: '运行中', class: 'tag-info' },
  'offline': { text: '已下线', class: 'tag-gray' },
  'completed': { text: '已上线', class: 'tag-success' },
  'failed': { text: '异常', class: 'tag-danger' },
  'stopped': { text: '已停止', class: 'tag-warning' }
};

/**
 * 确认状态切换
 * @param {HTMLElement} btn 点击的按钮元素
 * @param {string} targetStatus 目标状态
 */
function confirmStatusChange(btn, targetStatus) {
  // 获取当前行数据
  const row = btn.closest('tr');
  const taskName = row.querySelector('td:first-child').textContent;
  
  // 根据目标状态设置提示信息
  const statusText = statusConfig[targetStatus]?.text || '变更状态';
  
  // 显示确认弹窗
  if (confirm(`确定要将任务"${taskName}"切换为${statusText}状态吗？`)) {
    // 执行状态切换
    changeStatus(row, targetStatus);
  }
}

/**
 * 执行状态切换
 * @param {HTMLElement} row 表格行元素
 * @param {string} status 目标状态
 */
async function changeStatus(row, status) {
  // 获取状态单元格
  const statusCell = row.querySelector('td:nth-child(5)');
  const config = statusConfig[status];
  
  if (config) {
    try {
      // 调用任务状态切换接口
      const taskName = row.querySelector('td:first-child').textContent;
      await apiRequest(`${API_BASE_URL}/offline-tasks/status`, 'PUT', {
        taskName: taskName,
        status: status
      });
      console.log('任务状态切换成功');
    } catch (error) {
      console.error('任务状态切换失败:', error);
    }
    
    // 更新状态显示
    statusCell.innerHTML = `<span class="tag ${config.class}">${config.text}</span>`;
    
    // 可以在这里添加AJAX请求，将状态变更提交到服务器
    console.log(`任务"${row.querySelector('td:first-child').textContent}"已切换为${config.text}状态`);
    
    // 简单的成功提示
    alert('状态切换成功！');
  }
}

/**
 * 处理搜索框回车键事件
 */
function handleSearchKeyPress(event) {
  if (event.key === 'Enter') {
    searchTasks();
  }
}

/**
 * 查询任务列表
 */
async function searchTasks() {
  // 获取搜索条件
  const searchInput = document.querySelector('.search-box input');
  const statusSelect = document.querySelector('select');
  const searchKeyword = searchInput.value.trim();
  const selectedStatus = statusSelect.value;
  
  try {
    // 显示加载状态
    const searchBtn = document.querySelector('button[onclick="searchTasks()"]');
    const originalText = searchBtn.innerHTML;
    searchBtn.disabled = true;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
    
    // 调用查询任务列表接口
    await apiRequest(`${API_BASE_URL}/offline-tasks/search`, 'POST', {
      keyword: searchKeyword,
      status: selectedStatus
    });
    console.log('查询任务列表成功');
    
    // 模拟查询结果（实际项目中这里会更新表格数据）
    showNotification('查询完成！', 'success');
    
    // 恢复按钮状态
    searchBtn.disabled = false;
    searchBtn.innerHTML = originalText;
    
  } catch (error) {
    console.error('查询任务列表失败:', error);
    showNotification('查询失败，请重试！', 'error');
    
    // 恢复按钮状态
    const searchBtn = document.querySelector('button[onclick="searchTasks()"]');
    searchBtn.disabled = false;
    searchBtn.innerHTML = '<i class="fas fa-search"></i> 查询';
  }
}

// 为其他操作按钮添加事件处理（停止、重试、上线等）
document.querySelectorAll('.btn').forEach(btn => {
  if (btn.innerHTML.includes('fa-stop')) {
    btn.addEventListener('click', function() {
      confirmStatusChange(this, 'stopped');
    });
  } else if (btn.innerHTML.includes('fa-redo')) {
    btn.addEventListener('click', function() {
      confirmStatusChange(this, 'running');
    });
  } else if (btn.innerHTML.includes('fa-power-off') && btn.style.color.includes('success')) {
    btn.addEventListener('click', function() {
      confirmStatusChange(this, 'running');
    });
  }
});
</script>
</body>
</html>