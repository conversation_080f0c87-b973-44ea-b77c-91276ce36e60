<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 告警通知管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
   <style>
    /* 基础样式复用 */
    .btn-purple {
      background-color: var(--primary-color);
      color: white;
      border: none;
    }
    .btn-purple:hover {
      background-color: var(--primary-color);
    }
    .add-btn-container {
      margin-bottom: 16px;
      text-align: right;
    }
    .add-btn {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
    }
    .add-btn i {
      margin-right: 8px;
    }
    .tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
    .tag-success {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .tag-danger {
      background-color: #ffebee;
      color: #c62828;
    }
    .tag-warning {
      background-color: #fff8e1;
      color: #ff8f00;
    }
    .tag-info {
      background-color: #e3f2fd;
      color: #1565c0;
    }
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
      align-items: center;
      justify-content: center;
    }
    .modal-content {
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 12px;
    }
    .progress-bar {
      height: 8px;
      background-color: #eee;
      border-radius: 4px;
      width: 100px;
    }
    .progress {
      height: 100%;
      border-radius: 4px;
    }

    /* 分页样式（按图片要求设计） */
    .pagination-tabs {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin: 16px 0;
    }
    .pagination-tab {
      padding: 10px 20px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: 2px solid transparent;
      margin-right: 4px;
      font-weight: 500;
    }
    .pagination-tab.active {
      border-bottom: 2px solid var(--primary-color);
      color: var(--primary-color);
    }
    .pagination-tab:hover {
      background-color: #f5f5f5;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
   </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务完成通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10086已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务告警</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10087执行失败</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" >
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child  active" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child " data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child " data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group"  style="display: none">
            <div class="menu-item child " data-href="2222.html">管理⻚⾯穿透权限</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-bell-ring page-title-icon"></i>
      告警通知管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item"><a href="task_scheduling.html" style="text-decoration: none; color: inherit;">任务调度</a></div>
      <div class="breadcrumb-item active">告警通知</div>
    </div>

    <div class="pagination-tabs">
      <div class="tab-btn pagination-tab active" data-tab-target="notification-channel">通知渠道配置</div>
      <div class="tab-btn pagination-tab" data-tab-target="notification-template">通知内容模板</div>
      <div class="tab-btn pagination-tab" data-tab-target="notification-task">通知任务管理</div>
      <div class="tab-btn pagination-tab"  data-tab-target="notification-history">通知历史记录</div>
    </div>
    <!-- 标签页内容 -->
    <div class="tab-content active" id="notification-channel">
      <!-- 按钮已移动到搜索栏旁边 -->
      

        <!-- 新增通知渠道配置弹窗 -->
        <div id="channelModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
          <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
            <div class="modal-header" style="padding: 16px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
              <h3 style="margin: 0; font-weight: 500; font-size: 16px;">新增通知渠道配置</h3>
              <button id="closeModalBtn" class="close-btn" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" style="padding: 16px;">
              <form id="addChannelForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                  <div>
                    <label for="channelName">渠道名称 <span style="color: red;">*</span></label>
                    <input type="text" id="channelName" placeholder="请输入渠道名称" required>
                  </div>
                  <div>
                    <label for="channelType">渠道类型 <span style="color: red;">*</span></label>
                    <select id="channelType" required>
                      <option value="">请选择渠道类型</option>
                      <option value="email">邮件</option>
                      <option value="sms">短信</option>
                      <option value="wechat">微信</option>
                      <option value="dingtalk">钉钉</option>
                    </select>
                  </div>
                  <div>
                    <label for="templateId">通知内容模板 <span style="color: red;">*</span></label>
                    <select id="templateId" required>
                      <option value="">请选择模板</option>
                      <option value="temp1">系统告警通知模板</option>
                      <option value="temp2">任务失败通知模板</option>
                      <option value="temp3">任务完成通知模板</option>
                    </select>
                  </div>
                  <div>
                    <label for="priority">优先级 <span style="color: red;">*</span></label>
                    <select id="priority" required>
                      <option value="">请选择优先级</option>
                      <option value="high">高</option>
                      <option value="medium">中</option>
                      <option value="low">低</option>
                    </select>
                  </div>
                  <div>
                    <label for="recipient">接收人 <span style="color: red;">*</span></label>
                    <input type="text" id="recipient" placeholder="请输入接收人" required>
                  </div>
                  <div>
                    <label for="status">配置状态 <span style="color: red;">*</span></label>
                    <select id="status" required>
                      <option value="">请选择状态</option>
                      <option value="active">启用</option>
                      <option value="inactive">禁用</option>
                    </select>
                  </div>
                  <div style="grid-column: span 2;">
                    <label for="description">描述</label>
                    <textarea id="description" rows="4" placeholder="请输入描述信息"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
                  <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('addChannelForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存配置</button>
                </div>
              </form>
            </div>
          </div>
        </div>

      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">通知渠道配置列表</div>
            <div style="display: flex; gap: 12px;">
              <div class="search-box" style="width: 250px;">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="搜索渠道...">
              </div>
              <button id="addChannelBtn" class="btn btn-primary" style="height:40px"><i class="fas fa-plus"></i> 新增通知渠道配置</button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>渠道ID</th>
                  <th>渠道名称</th>
                  <th>渠道类型</th>
                  <th>模板名称</th>
                  <th>优先级</th>
                  <th>接收人</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>NC001</td>
                  <td>系统告警邮件通知</td>
                  <td>邮件</td>
                  <td>系统告警通知模板</td>
                  <td><span class="tag tag-danger">高</span></td>
                  <td><EMAIL></td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>2023-05-15 10:30:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>NC002</td>
                  <td>任务状态短信通知</td>
                  <td>短信</td>
                  <td>任务状态通知模板</td>
                  <td><span class="tag tag-warning">中</span></td>
                  <td>13800138000</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>2023-05-16 14:20:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>NC003</td>
                  <td>微信告警通知</td>
                  <td>微信</td>
                  <td>系统告警通知模板</td>
                  <td><span class="tag tag-danger">高</span></td>
                  <td>wechat_user1</td>
                  <td><span class="tag tag-warning">禁用</span></td>
                  <td>2023-05-17 09:15:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="notification-template">
      <!-- 新增通知内容模板弹窗 -->
      <div id="templateModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
        <div class="modal-content" style="background-color: white; border-radius: 8px; width: 700px; max-width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
          <div class="modal-header" style="padding: 16px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-weight: 500; font-size: 16px;">新增通知内容模板</h3>
            <button id="closeTemplateModalBtn" class="close-btn" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
          </div>
          <div class="modal-body" style="padding: 16px;">
            <form id="addTemplateForm">
              <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div>
                  <label for="templateCode">模板ID <span style="color: red;">*</span></label>
                  <input type="text" id="templateCode" placeholder="请输入模板ID" required>
                </div>
                <div>
                  <label for="templateName">模板名称 <span style="color: red;">*</span></label>
                  <input type="text" id="templateName" placeholder="请输入模板名称" required>
                </div>
                <div>
                  <label for="templateVersion">版本 <span style="color: red;">*</span></label>
                  <input type="text" id="templateVersion" placeholder="请输入版本号" required>
                </div>
                <div>
                  <label for="templateType">模板类型 <span style="color: red;">*</span></label>
                  <select id="templateType" required>
                    <option value="">请选择模板类型</option>
                    <option value="alarm">告警通知</option>
                    <option value="task">任务通知</option>
                    <option value="system">系统通知</option>
                  </select>
                </div>
                <div style="grid-column: span 2;">
                  <label for="templateContent">模板内容 <span style="color: red;">*</span></label>
                  <textarea id="templateContent" rows="6" placeholder="请输入模板内容，支持变量: {eventId}, {eventName}, {eventTime}, {eventLevel}, {eventDesc}" required></textarea>
                </div>
                <div style="grid-column: span 2;">
                  <label for="templateFields">包含字段 <span style="color: red;">*</span></label>
                  <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 8px;">
                    <label style="display: flex; align-items: center;">
                      <input type="checkbox" name="fields" value="eventId"> 事件ID
                    </label>
                    <label style="display: flex; align-items: center;">
                      <input type="checkbox" name="fields" value="eventName"> 事件名称
                    </label>
                    <label style="display: flex; align-items: center;">
                      <input type="checkbox" name="fields" value="eventTime"> 事件时间
                    </label>
                    <label style="display: flex; align-items: center;">
                      <input type="checkbox" name="fields" value="eventLevel"> 事件等级
                    </label>
                    <label style="display: flex; align-items: center;">
                      <input type="checkbox" name="fields" value="eventDesc"> 事件描述
                    </label>
                    <label style="display: flex; align-items: center;">
                      <input type="checkbox" name="fields" value="actionSuggestion"> 处理建议
                    </label>
                  </div>
                </div>
              </div>
              <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
                <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('addTemplateForm')">重置</button>
                <button type="submit" class="btn btn-primary">保存模板</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">通知内容模板列表</div>
            <div style="display: flex; gap: 12px;">
              <div class="search-box" style="width: 250px;">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="搜索模板...">
              </div>
              <button id="addTemplateBtn" class="btn btn-primary" style="height:40px"><i class="fas fa-plus"></i> 新增通知内容模板</button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>模板ID</th>
                  <th>模板名称</th>
                  <th>版本</th>
                  <th>模板类型</th>
                  <th>创建时间</th>
                  <th>创建人</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>TMP001</td>
                  <td>系统告警通知模板</td>
                  <td>1.0</td>
                  <td>告警通知</td>
                  <td>2023-05-10 15:45:00</td>
                  <td>管理员</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>TMP002</td>
                  <td>任务状态通知模板</td>
                  <td>1.1</td>
                  <td>任务通知</td>
                  <td>2023-05-12 09:30:00</td>
                  <td>管理员</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>TMP003</td>
                  <td>系统通知模板</td>
                  <td>1.0</td>
                  <td>系统通知</td>
                  <td>2023-05-14 16:20:00</td>
                  <td>管理员</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="notification-task">
      <!-- 新增通知任务管理弹窗 -->
        <div id="taskModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); justify-content: center; align-items: center; z-index: 1000;">
          <div style="background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); width: 800px; max-width: 90%; max-height: 90vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; border-bottom: 1px solid #eee;">
              <div style="font-weight: 600; font-size: 18px;">新增通知任务管理</div>
              <button id="closeTaskModalBtn" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div style="padding: 16px;">
              <form id="addTaskForm">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                  <div>
                    <label for="taskId">通知任务ID <span style="color: red;">*</span></label>
                    <input type="text" id="taskId" placeholder="请输入通知任务ID" required>
                  </div>
                  <div>
                    <label for="taskName">任务名称 <span style="color: red;">*</span></label>
                    <input type="text" id="taskName" placeholder="请输入任务名称" required>
                  </div>
                  <div>
                    <label for="channelId">通知渠道 <span style="color: red;">*</span></label>
                    <select id="channelId" required>
                      <option value="">请选择通知渠道</option>
                      <option value="NC001">系统告警邮件通知</option>
                      <option value="NC002">任务状态短信通知</option>
                      <option value="NC003">微信告警通知</option>
                    </select>
                  </div>
                  <div>
                    <label for="taskStatus">任务状态 <span style="color: red;">*</span></label>
                    <select id="taskStatus" required>
                      <option value="">请选择任务状态</option>
                      <option value="pending">待执行</option>
                      <option value="processing">执行中</option>
                      <option value="completed">已完成</option>
                      <option value="failed">执行失败</option>
                    </select>
                  </div>
                  <div style="grid-column: span 2;">
                    <label for="taskContent">通知内容 <span style="color: red;">*</span></label>
                    <textarea id="taskContent" rows="4" placeholder="请输入通知内容" required></textarea>
                  </div>
                  <div style="grid-column: span 2;">
                    <label for="taskLog">任务日志</label>
                    <textarea id="taskLog" rows="3" placeholder="请输入任务日志"></textarea>
                  </div>
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
                  <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('addTaskForm')">重置</button>
                  <button type="submit" class="btn btn-primary">保存任务</button>
                </div>
              </form>
            </div>
          </div>
        </div>

      <div class="card">
        <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div style="font-weight: 500; font-size: 16px;">通知任务管理列表</div>
              <div style="display: flex; gap: 12px;">
                <div class="search-box" style="width: 250px;">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" placeholder="搜索任务...">
                </div>
                <button id="addTaskBtn" class="btn btn-primary" style="height:40px"><i class="fas fa-plus"></i> 新增通知任务管理</button>
              </div>
            </div>
          </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>任务ID</th>
                  <th>任务名称</th>
                  <th>通知渠道</th>
                  <th>任务状态</th>
                  <th>创建时间</th>
                  <th>执行时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>NT001</td>
                  <td>系统告警通知任务</td>
                  <td>系统告警邮件通知</td>
                  <td><span class="tag tag-success">已完成</span></td>
                  <td>2023-05-18 09:00:00</td>
                  <td>2023-05-18 09:05:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>NT002</td>
                  <td>任务失败通知任务</td>
                  <td>任务状态短信通知</td>
                  <td><span class="tag tag-success">已完成</span></td>
                  <td>2023-05-18 10:30:00</td>
                  <td>2023-05-18 10:35:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>NT003</td>
                  <td>系统异常通知任务</td>
                  <td>微信告警通知</td>
                  <td><span class="tag tag-danger">执行失败</span></td>
                  <td>2023-05-18 14:15:00</td>
                  <td>2023-05-18 14:20:00</td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    <button class="btn btn-sm btn-warning"><i class="fas fa-redo"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tab-content" id="notification-history">
      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">通知历史记录查询</div>
            <div style="display: flex;">
              <div class="search-box" style="width: 250px; margin-right: 12px;">
                <i class="fas fa-search search-box-icon"></i>
                <input type="text" placeholder="搜索事件ID...">
              </div>
              <div style="margin-right: 12px;">
                <input type="date" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              </div>
              <button class="btn btn-primary" style="height:35px"><i class="fas fa-search"></i> 查询</button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>事件ID</th>
                  <th>通知任务ID</th>
                  <th>通知渠道</th>
                  <th>通知时间</th>
                  <th>状态</th>
                  <th>接收人</th>
                  <th>失败原因</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>E001</td>
                  <td>NT001</td>
                  <td>邮件</td>
                  <td>2023-05-18 09:05:00</td>
                  <td><span class="tag tag-success">成功</span></td>
                  <td><EMAIL></td>
                  <td>-</td>
                  <td>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>E002</td>
                  <td>NT002</td>
                  <td>短信</td>
                  <td>2023-05-18 10:35:00</td>
                  <td><span class="tag tag-success">成功</span></td>
                  <td>13800138000</td>
                  <td>-</td>
                  <td>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>E003</td>
                  <td>NT003</td>
                  <td>微信</td>
                  <td>2023-05-18 14:20:00</td>
                  <td><span class="tag tag-danger">失败</span></td>
                  <td>wechat_user1</td>
                  <td>微信接口调用失败</td>
                  <td>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                    <button class="btn btn-sm btn-warning"><i class="fas fa-redo"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 标签页切换功能
    document.addEventListener('DOMContentLoaded', function() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');
      const addChannelBtn = document.getElementById('addChannelBtn');
      const channelModal = document.getElementById('channelModal');
      const closeModalBtn = document.getElementById('closeModalBtn');
      const addTemplateBtn = document.getElementById('addTemplateBtn');
      const templateModal = document.getElementById('templateModal');
      const closeTemplateModalBtn = document.getElementById('closeTemplateModalBtn');
      const addTaskBtn = document.getElementById('addTaskBtn');
      const taskModal = document.getElementById('taskModal');
      const closeTaskModalBtn = document.getElementById('closeTaskModalBtn');

      // 标签页切换
      tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          const target = btn.getAttribute('data-tab-target');

          // 切换按钮状态
          tabBtns.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');

          // 切换内容显示
          tabContents.forEach(content => {
            content.classList.remove('active');
            if (content.id === target) {
              content.classList.add('active');
            }
          });
        });
      });

      // 刷新按钮点击事件
      document.querySelectorAll('.btn-warning .fa-redo').forEach(btn => {
        btn.closest('button').addEventListener('click', function() {
          if (confirm('是否重新执行此任务？')) {
            // 模拟重新执行操作
            setTimeout(function() {
              alert('任务已重新执行成功！');
            }, 500);
          } else {
            alert('已取消重新执行任务');
          }
        });
      });

      // 弹窗功能
      // 渠道配置弹窗
      if (addChannelBtn && channelModal && closeModalBtn) {
        // 打开弹窗
        addChannelBtn.addEventListener('click', () => {
          channelModal.style.display = 'flex';
        });

        // 关闭弹窗
        closeModalBtn.addEventListener('click', () => {
          channelModal.style.display = 'none';
        });

        // 点击弹窗外部关闭
        window.addEventListener('click', (e) => {
          if (e.target === channelModal) {
            channelModal.style.display = 'none';
          }
        });
      }

      // 模板配置弹窗
      if (addTemplateBtn && templateModal && closeTemplateModalBtn) {
        // 打开弹窗
        addTemplateBtn.addEventListener('click', () => {
          templateModal.style.display = 'flex';
        });

        // 关闭弹窗
        closeTemplateModalBtn.addEventListener('click', () => {
          templateModal.style.display = 'none';
        });

        // 点击弹窗外部关闭
        window.addEventListener('click', (e) => {
          if (e.target === templateModal) {
            templateModal.style.display = 'none';
          }
        });
      }

      // 通知任务配置弹窗
      if (addTaskBtn && taskModal && closeTaskModalBtn) {
        // 打开弹窗
        addTaskBtn.addEventListener('click', () => {
          taskModal.style.display = 'flex';
        });

        // 关闭弹窗
        closeTaskModalBtn.addEventListener('click', () => {
          taskModal.style.display = 'none';
        });

        // 点击弹窗外部关闭
        window.addEventListener('click', (e) => {
          if (e.target === taskModal) {
            taskModal.style.display = 'none';
          }
        });
      }

      // 表单提交处理
        document.getElementById('addChannelForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // 模拟保存操作
            setTimeout(function() {
              // 显示成功提示
              alert('通知渠道配置已保存!');
              // 关闭弹窗
              if (channelModal) {
                channelModal.style.display = 'none';
              }
              // 重置表单
              document.getElementById('addChannelForm').reset();
            }, 500);
          });

          // 通知内容模板表单提交处理
          document.getElementById('addTemplateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // 模拟保存操作
            setTimeout(function() {
              // 显示成功提示
              alert('通知内容模板已保存!');
              // 关闭弹窗
              if (templateModal) {
                templateModal.style.display = 'none';
              }
              // 重置表单
              document.getElementById('addTemplateForm').reset();
            }, 500);
          });

      document.getElementById('addTaskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // 模拟保存操作
        setTimeout(function() {
          // 显示成功提示
          alert('通知任务已保存!');
          // 关闭弹窗
          if (taskModal) {
            taskModal.style.display = 'none';
          }
          // 重置表单
          document.getElementById('addTaskForm').reset();
        }, 500);
      });
    });

    // 重置表单函数
    function resetForm(formId) {
      document.getElementById(formId).reset();
    }
  </script>
</body>
</html>