<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 智能问数</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      .query-container {
        display: flex;
        height: calc(100vh - 112px);
      }
      .query-sidebar {
        width: 300px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 16px;
        margin-right: 24px;
        display: flex;
        flex-direction: column;
      }
      .query-history {
        flex: 1;
        overflow-y: auto;
        margin-top: 16px;
      }
      .query-history-item {
        padding: 12px;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        transition: background-color 0.3s;
      }
      .query-history-item:hover {
        background-color: var(--hover-color);
      }
      .query-history-item.active {
        background-color: rgba(24, 144, 255, 0.1);
        color: var(--primary-color);
      }
      .query-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .query-result {
        flex: 1;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
        overflow-y: auto;
        margin-bottom: 24px;
      }
      .query-input-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
      }
      .query-input {
        width: 100%;
        height: 100px;
        padding: 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        resize: none;
        margin-bottom: 16px;
      }
      .suggested-questions {
        margin-top: 16px;
      }
      .suggested-question {
        display: inline-block;
        padding: 6px 12px;
        background-color: var(--hover-color);
        border-radius: 16px;
        font-size: 14px;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
      }
      .suggested-question:hover {
        background-color: rgba(24, 144, 255, 0.1);
        color: var(--primary-color);
      }
      .result-stat {
        display: flex;
        margin-bottom: 20px;
      }
      .result-stat-item {
        flex: 1;
        text-align: center;
        padding: 16px;
        background-color: rgba(24, 144, 255, 0.05);
        border-radius: 8px;
        margin-right: 16px;
      }
      .result-stat-item:last-child {
        margin-right: 0;
      }
      .result-stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 4px;
      }
      .result-stat-label {
        font-size: 14px;
        color: var(--text-secondary);
      }
      .chart-container-small {
        height: 250px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child active" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child" data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child" data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child" data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="2222.html">管理⻚⾯穿透权限</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-robot page-title-icon"></i>
        运营视图 - 智能问数
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit">运营视图</a></div>
        <div class="breadcrumb-item active">智能问数</div>
      </div>

      <!-- 智能问数容器 -->
      <div class="query-container">
        <!-- 侧边栏：问题历史和建议 -->
        <div class="query-sidebar">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px">
            <i class="fas fa-history"></i>
            问数历史
          </div>
          <div class="query-history">
            <div class="query-history-item active">
              <div style="font-weight: 500; margin-bottom: 4px">全国各地区销售额分布</div>
              <div style="font-size: 12px; color: var(--text-tertiary)">今天 14:30</div>
            </div>
            <div class="query-history-item">
              <div style="font-weight: 500; margin-bottom: 4px">近三个月的销售趋势</div>
              <div style="font-size: 12px; color: var(--text-tertiary)">昨天 09:15</div>
            </div>
            <div class="query-history-item">
              <div style="font-weight: 500; margin-bottom: 4px">各产品类别的销售占比</div>
              <div style="font-size: 12px; color: var(--text-tertiary)">昨天 08:40</div>
            </div>
            <div class="query-history-item">
              <div style="font-weight: 500; margin-bottom: 4px">VIP客户的消费分析</div>
              <div style="font-size: 12px; color: var(--text-tertiary)">2023-07-15 16:20</div>
            </div>
            <div class="query-history-item">
              <div style="font-weight: 500; margin-bottom: 4px">各门店的客流量对比</div>
              <div style="font-size: 12px; color: var(--text-tertiary)">2023-07-14 11:30</div>
            </div>
          </div>

          <div style="font-size: 16px; font-weight: 500; margin-top: 24px; margin-bottom: 16px">
            <i class="fas fa-lightbulb"></i>
            推荐问题
          </div>
          <div class="suggested-questions">
            <div class="suggested-question">
              <i class="fas fa-chart-line"></i>
              本月销售业绩完成情况
            </div>
            <div class="suggested-question">
              <i class="fas fa-users"></i>
              新增用户增长分析
            </div>
            <div class="suggested-question">
              <i class="fas fa-shopping-cart"></i>
              购物车放弃率分析
            </div>
            <div class="suggested-question">
              <i class="fas fa-percentage"></i>
              各地区毛利率对比
            </div>
            <div class="suggested-question">
              <i class="fas fa-truck"></i>
              物流配送时效分析
            </div>
            <div class="suggested-question">
              <i class="fas fa-star"></i>
              客户满意度趋势
            </div>
          </div>
        </div>

        <!-- 主内容区：结果展示和问题输入 -->
        <div class="query-content">
          <!-- 结果展示区 -->
          <div class="query-result">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
              <div style="font-size: 18px; font-weight: 600">全国各地区销售额分布</div>
              <div style="display: flex">
                <button class="btn" style="border: 1px solid var(--border-color); margin-right: 8px">
                  <i class="fas fa-download"></i>
                  导出
                </button>
                <button class="btn" style="border: 1px solid var(--border-color); margin-right: 8px">
                  <i class="fas fa-share-alt"></i>
                  分享
                </button>
                <button class="btn" style="border: 1px solid var(--border-color)">
                  <i class="fas fa-expand"></i>
                  全屏
                </button>
              </div>
            </div>

            <!-- 统计信息 -->
            <div class="result-stat">
              <div class="result-stat-item">
                <div class="result-stat-value">¥12.6M</div>
                <div class="result-stat-label">总销售额</div>
              </div>
              <div class="result-stat-item">
                <div class="result-stat-value">12.5%</div>
                <div class="result-stat-label">同比增长</div>
              </div>
              <div class="result-stat-item">
                <div class="result-stat-value">华东</div>
                <div class="result-stat-label">最高销售区域</div>
              </div>
              <div class="result-stat-item">
                <div class="result-stat-value">324</div>
                <div class="result-stat-label">销售单量</div>
              </div>
            </div>

            <!-- 图表展示 -->
            <div class="chart-container" style="height: 400px; background-color: #f9f9f9; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; justify-content: center">
              <div style="text-align: center">
                <i class="fas fa-chart-bar" style="font-size: 48px; color: var(--primary-color); margin-bottom: 16px"></i>
                <div style="font-size: 18px; color: var(--text-secondary)">地区销售分布图</div>
                <div style="font-size: 14px; color: var(--text-tertiary); margin-top: 8px">此处将显示全国各地区的销售额分布图表</div>
              </div>
            </div>

            <!-- 表格数据 -->
            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>地区</th>
                    <th>销售额 (万元)</th>
                    <th>同比增长</th>
                    <th>销售单量</th>
                    <th>客单价 (元)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>华东地区</td>
                    <td>4,258</td>
                    <td><span class="tag tag-success">+15.2%</span></td>
                    <td>108</td>
                    <td>39,426</td>
                  </tr>
                  <tr>
                    <td>华南地区</td>
                    <td>3,126</td>
                    <td><span class="tag tag-success">+10.8%</span></td>
                    <td>82</td>
                    <td>38,122</td>
                  </tr>
                  <tr>
                    <td>华北地区</td>
                    <td>2,485</td>
                    <td><span class="tag tag-success">+8.3%</span></td>
                    <td>64</td>
                    <td>38,828</td>
                  </tr>
                  <tr>
                    <td>西南地区</td>
                    <td>1,562</td>
                    <td><span class="tag tag-success">+18.5%</span></td>
                    <td>42</td>
                    <td>37,190</td>
                  </tr>
                  <tr>
                    <td>西北地区</td>
                    <td>894</td>
                    <td><span class="tag tag-warning">+2.1%</span></td>
                    <td>20</td>
                    <td>44,700</td>
                  </tr>
                  <tr>
                    <td>东北地区</td>
                    <td>375</td>
                    <td><span class="tag tag-danger">-3.2%</span></td>
                    <td>8</td>
                    <td>46,875</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 问题输入区 -->
          <div class="query-input-container">
            <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px">
              <i class="fas fa-comment-dots"></i>
              输入您的问题
            </div>
            <textarea class="query-input" placeholder="请输入您想要查询的问题，例如：'全国各地区的销售额是多少？'或'近三个月的销售趋势如何？'"></textarea>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="display: flex">
                <button class="btn" style="border: 1px solid var(--border-color); margin-right: 8px">
                  <i class="fas fa-magic"></i>
                  智能生成
                </button>
                <button class="btn" style="border: 1px solid var(--border-color)">
                  <i class="fas fa-sliders-h"></i>
                  高级设置
                </button>
              </div>
              <button class="btn btn-primary" style="padding: 8px 24px">
                <i class="fas fa-paper-plane"></i>
                提交问题
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // API基础URL
      const API_BASE_URL = 'http://localhost:8000/api';

      // 封装fetch请求函数
      async function apiRequest(url, method, data = null) {
        try {
          const options = {
            method: method,
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer ' + localStorage.getItem('token') || '',
            },
          };

          if (data) {
            options.body = JSON.stringify(data);
          }

          console.log(`调用接口: ${url}, 方法: ${method}, 数据:`, data);
          const response = await fetch(url, options);
          const result = await response.json();
          console.log(`接口响应:`, result);

          if (!response.ok) {
            throw new Error(result.message || `请求失败: ${response.status}`);
          }

          return result;
        } catch (error) {
          console.error('API请求错误:', error);
          throw error;
        }
      }

      // 显示通知
      function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = 'notification ' + type;
        notification.innerText = message;
        document.body.appendChild(notification);

        // 显示通知
        setTimeout(() => {
          notification.style.opacity = '1';
          notification.style.transform = 'translateY(0)';
        }, 10);

        // 3秒后隐藏通知
        setTimeout(() => {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(-20px)';
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }

      // 智能问数页面特定的交互逻辑
      document.addEventListener('DOMContentLoaded', function () {
        // 问题历史点击事件
        const historyItems = document.querySelectorAll('.query-history-item');
        historyItems.forEach(item => {
          item.addEventListener('click', async () => {
            try {
              // 调用获取历史问题接口
              await apiRequest(`${API_BASE_URL}/intelligent-query/history`, 'POST', {
                historyId: item.getAttribute('data-id') || 'default',
              });
              console.log('获取历史问题成功');
            } catch (error) {
              console.error('获取历史问题失败:', error);
            }

            // 保持原有逻辑
            historyItems.forEach(hi => hi.classList.remove('active'));
            item.classList.add('active');
            // 这里可以添加加载历史问题结果的逻辑
          });
        });

        // 推荐问题点击事件
        const suggestedQuestions = document.querySelectorAll('.suggested-question');
        suggestedQuestions.forEach(question => {
          question.addEventListener('click', async () => {
            const questionText = question.textContent.trim();

            try {
              // 调用推荐问题接口
              await apiRequest(`${API_BASE_URL}/intelligent-query/suggest`, 'POST', {
                question: questionText,
              });
              console.log('推荐问题点击成功');
            } catch (error) {
              console.error('推荐问题点击失败:', error);
            }

            // 保持原有逻辑
            document.querySelector('.query-input').value = questionText;
          });
        });

        // 提交问题按钮点击事件
        document.querySelector('.btn-primary').addEventListener('click', async () => {
          const question = document.querySelector('.query-input').value.trim();
          if (question) {
            try {
              // 调用提交问题接口
              await apiRequest(`${API_BASE_URL}/intelligent-query/submit`, 'POST', {
                question: question,
              });
              console.log('提交问题成功');
            } catch (error) {
              console.error('提交问题失败:', error);
            }

            // 保持原有逻辑
            alert('问题已提交：' + question);
            // 这里可以添加实际提交问题到后端的逻辑
          } else {
            alert('请输入问题内容');
          }
        });

        // 智能生成按钮点击事件
        document.querySelector('button[style*="magic"]').addEventListener('click', async () => {
          try {
            // 调用智能生成接口
            await apiRequest(`${API_BASE_URL}/intelligent-query/generate`, 'POST', {
              timestamp: new Date().toISOString(),
            });
            console.log('智能生成成功');
          } catch (error) {
            console.error('智能生成失败:', error);
          }

          // 保持原有逻辑
          alert('智能生成功能已触发');
        });

        // 高级设置按钮点击事件
        document.querySelector('button[style*="sliders-h"]').addEventListener('click', async () => {
          try {
            // 调用高级设置接口
            await apiRequest(`${API_BASE_URL}/intelligent-query/advanced-settings`, 'POST', {
              timestamp: new Date().toISOString(),
            });
            console.log('高级设置成功');
          } catch (error) {
            console.error('高级设置失败:', error);
          }

          // 保持原有逻辑
          alert('高级设置功能已触发');
        });
      });
    </script>
  </body>
</html>
