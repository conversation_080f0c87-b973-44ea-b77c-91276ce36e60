/* 选项卡容器样式 */
.tabs {
  display: flex;
  width: 100%;
  margin-bottom: 20px;
  border-bottom: 2px solid var(--primary-color);
}

/* 选项卡按钮样式 */
.tab-btn {
  flex: 1; /* 均匀分配宽度 */
  padding: 15px 0; /* 增加内边距，加高按钮 */
  font-size: 16px; /* 增大字体 */
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  background-color: var(--card-bg);
  border: none;
  border-radius: 6px 6px 0 0;
  transition: all 0.3s;
  color: var(--text-secondary);
}

/* 选项卡按钮悬停效果 */
.tab-btn:hover {
  background-color: var(--hover-color);
  color: var(--primary-color);
}

/* 选项卡按钮激活状态 */
.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
}

/* 查询和重置按钮样式优化 */
.search-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.search-btn:hover {
  background-color: var(--secondary-color);
}

.reset-btn {
  background-color: var(--border-color);
  color: var(--text-primary);
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.reset-btn:hover {
  background-color: #d9d9d9;
}

/* 数据概览卡片样式 */
.data-overview {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.data-card {
  flex: 1 1 200px;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-card-title {
  font-size: 14px;
  color: var(--text-tertiary);
  margin-bottom: 8px;
}

.data-card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color);
}

.data-card-trend {
  margin-top: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--danger-color);
}