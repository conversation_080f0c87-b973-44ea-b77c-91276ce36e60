/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

:root {
  --primary-color: #1890ff;
  --secondary-color: #0050b3;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #ff4d4f;
  --info-color: #1890ff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e8e8e8;
  --hover-color: #f0f0f0;
}

body {
  background-color: var(--bg-color);
  color: var(--text-primary);
  line-height: 1.5;
  zoom: 0.8; /* 缩放为80% */
}

/* 布局组件 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 卡片组件 */
.card {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

/* 按钮组件 */
.btn {
  display: inline-block;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-info {
  background-color: var(--info-color);
  color: white;
}

/* 表单组件 */
.form-group {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

input,
select,
textarea {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: border-color 0.3s;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 导航栏 */
.navbar {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 20px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
}

/* 侧边栏 */
.sidebar {
  width: 224px;
  background-color: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 64px;
  bottom: 0;
  left: 0;
  padding: 20px 0;
  overflow-y: auto;
  z-index: 999;
}

.menu-item {
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.menu-item:hover {
  background-color: var(--hover-color);
}

.menu-item.active {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
  border-right: 3px solid var(--primary-color);
}

.menu-icon {
  margin-right: 12px;
  font-size: 16px;
}

/* 主内容区 */
.main-content {
  margin-top: 64px;
  margin-left: 224px;
  padding: 24px;
  min-height: calc(100vh - 64px);
}

/* 标题样式 */
.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}

.page-title-icon {
  margin-right: 12px;
  color: var(--primary-color);
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  font-weight: 600;
  background-color: #f7f7f7;
}

.table tr:hover {
  background-color: var(--hover-color);
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.tag-danger {
  background-color: rgba(255, 77, 79, 0.1);
  color: var(--danger-color);
}

.tag-info {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--info-color);
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 12px;
  margin-bottom: 20px;
}

.search-box input {
  border: none;
  outline: none;
  flex: 1;
  padding: 6px 0;
}

.search-box-icon {
  color: var(--text-tertiary);
  margin-right: 8px;
}

/* 栅格系统 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.col {
  padding: 0 10px;
  margin-bottom: 20px;
}

.col-12 {
  width: 100%;
}

.col-6 {
  width: 50%;
}

.col-4 {
  width: 33.333%;
}

.col-3 {
  width: 25%;
}

/* 统计卡片 */
.stat-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(24, 144, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: var(--primary-color);
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 时间选择器 */
.date-picker {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.date-picker label {
  margin-right: 12px;
  margin-bottom: 0;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 400px;
  margin-bottom: 20px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.pagination-item {
  padding: 6px 12px;
  margin: 0 4px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-item:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.pagination-item.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 登录页面 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: var(--bg-color);
}

.login-card {
  width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-logo {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 32px;
}

.login-form .form-group {
  margin-bottom: 24px;
}

.login-btn {
  width: 100%;
  padding: 10px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 64px;
  }

  .main-content {
    margin-left: 64px;
  }

  .menu-text {
    display: none;
  }

  .col-6,
  .col-4,
  .col-3 {
    width: 100%;
  }
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(24, 144, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 提示框 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* 模态框 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-tertiary);
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.modal-footer .btn {
  margin-left: 12px;
}

/* 步骤条 */
.steps {
  display: flex;
  margin-bottom: 32px;
}

.step {
  flex: 1;
  text-align: center;
  position: relative;
}

.step:not(:last-child):after {
  content: '';
  position: absolute;
  top: 12px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: var(--border-color);
  z-index: 1;
}

.step.active:not(:last-child):after {
  background-color: var(--primary-color);
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--border-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 8px;
  position: relative;
  z-index: 2;
}

.step.active .step-icon {
  background-color: var(--primary-color);
}

.step-title {
  font-size: 14px;
  font-weight: 500;
}

.step.active .step-title {
  color: var(--primary-color);
}

.step-description {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-top: 4px;
}

/* 开关按钮 */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: '';
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(24px);
}

/* 进度条 */
.progress {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
  transition: width 0.3s;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.breadcrumb-item {
  color: var(--text-secondary);
}

.breadcrumb-item:not(:last-child):after {
  content: '>';
  margin: 0 8px;
  color: var(--text-tertiary);
}

.breadcrumb-item.active {
  color: var(--text-primary);
}

/* 标签页 */
.tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-item {
  display: inline-block;
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* 下拉菜单 */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 8px 0;
  min-width: 160px;
  display: none;
  z-index: 100;
}

.dropdown.show .dropdown-menu {
  display: block;
}

.dropdown-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background-color: var(--hover-color);
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background-color: var(--danger-color);
  border-radius: 10px;
}

.badge-primary {
  background-color: var(--primary-color);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-warning {
  background-color: var(--warning-color);
}

.badge-info {
  background-color: var(--info-color);
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-text {
  height: 16px;
  margin-bottom: 8px;
}

.skeleton-title {
  height: 24px;
  margin-bottom: 16px;
  width: 60%;
}

.skeleton-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-bottom: 16px;
}

.skeleton-image {
  height: 200px;
  margin-bottom: 16px;
}

/* 滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #40a9ff;
    --secondary-color: #096dd9;
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-tertiary: #a0a0a0;
    --bg-color: #141414;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --hover-color: #2d2d2d;
  }
}

.modal-wrapper {
  z-index: 1000000 !important;
}

.form-group label, .text-sm{
  white-space: nowrap;       /* 禁止换行 */
}
