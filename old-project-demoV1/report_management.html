<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 运营报告管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      /* 发布确认弹窗样式 */
      .confirm-modal {
        display: none;
        position: fixed;
        z-index: 1001;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .confirm-modal.show {
        display: block;
      }

      .confirm-content {
        background-color: #fff;
        margin: 15% auto;
        padding: 20px;
        border-radius: 8px;
        width: 500px;
        position: relative;
        animation: modalFade 0.3s;
      }

      .confirm-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color);
      }

      .confirm-title {
        font-size: 18px;
        font-weight: 500;
      }

      .confirm-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
      }

      .confirm-body {
        margin-bottom: 20px;
      }

      .form-row {
        display: flex;
        margin-bottom: 12px;
      }

      .form-group {
        flex: 1;
        margin-right: 12px;
      }

      .form-group:last-child {
        margin-right: 0;
      }

      .form-group label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        font-size: 14px;
      }

      .form-group input,
      .form-group select {
        width: 100%;
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid var(--border-color);
      }

      .confirm-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }

      @keyframes modalFade {
        from {
          opacity: 0;
          transform: translateY(-30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 成功提示样式 */
      .success-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #4caf50;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1002;
        opacity: 0;
        transition: opacity 0.3s;
      }

      .success-toast.show {
        opacity: 1;
      }

      /* 错误提示样式 */
      .error-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #f44336;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1002;
        opacity: 0;
        transition: opacity 0.3s;
      }

      .error-toast.show {
        opacity: 1;
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营通报管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-file-alt page-title-icon"></i>
        运营报告管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">智能洞察分析</a></div>
        <div class="breadcrumb-item active">运营报告管理</div>
      </div>

      <!-- 搜索和操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; height: 20px">
        <div style="display: flex; width: 50%"></div>
        <div style="display: flex">
          <button class="btn btn-primary" data-modal-target="addReportModal" style="padding: 7px 10px; font-size: 12px">
            <i class="fas fa-plus"></i>
            新增报告
          </button>
          <button class="btn btn-secondary" style="margin-left: 12px; padding: 7px 10px; font-size: 12px" onclick="window.location.href='report_management_neirong2.html'" title="内容生成">
            <i class="fas fa-file-alt"></i>
            内容生成
          </button>
          <button class="btn btn-secondary" style="margin-left: 12px; padding: 7px 10px; font-size: 12px" onclick="window.location.href='report_management_dongtai2.html'" title="动态报告">
            <i class="fas fa-bolt"></i>
            动态报告
          </button>
          <button class="btn btn-secondary" style="margin-left: 12px; padding: 7px 10px; font-size: 12px" onclick="window.location.href='report_management_moban.html'" title="配置管理">
            <i class="fas fa-file-alt"></i>
            配置管理
          </button>
          <button class="btn btn-secondary" style="margin-left: 12px; padding: 7px 10px; font-size: 12px" onclick="window.location.href='report_management_2NeiRongGeShi.html'" title="内容格式管理">
            <i class="fas fa-cogs"></i>
            内容格式管理
          </button>
          <button class="btn btn-secondary" style="margin-left: 12px; padding: 7px 10px; font-size: 12px" onclick="window.location.href='YunYingBaoGaoYingYong.html'" title="运营报告应用">
            <i class="fas fa-rocket"></i>
            运营报告应用
          </button>
          <button class="btn btn-secondary" style="margin-left: 12px; margin-right: 12px; padding: 7px 10px; font-size: 12px" onclick="window.location.href='yunYingBaoGaoTongJiFenXi.html'" title="运营报告分析统计">
            <i class="fas fa-chart-bar"></i>
            运营报告分析统计
          </button>
        </div>
      </div>

      <!-- 运营报告模板查询区域 -->
      <div class="card" style="margin-bottom: 20px">
        <div style="padding: 16px">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 16px">
            <i class="fas fa-search"></i>
            运营报告模板查询
          </div>
          <div style="display: flex; flex-wrap: wrap; gap: 16px">
            <div style="flex: 1; min-width: 200px">
              <label for="templateCode" style="display: block; margin-bottom: 8px; font-weight: 500">模板编码</label>
              <input type="text" id="templateCode" placeholder="请输入模板编码" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
            </div>
            <div style="flex: 1; min-width: 200px">
              <label for="templateNameQuery" style="display: block; margin-bottom: 8px; font-weight: 500">模板名称</label>
              <input type="text" id="templateNameQuery" placeholder="请输入模板名称" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)" />
            </div>
            <div style="flex: 1; min-width: 200px">
              <label for="templateType" style="display: block; margin-bottom: 8px; font-weight: 500">模板类型</label>
              <select id="templateType" style="width: 100%; padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                <option value="all">全部类型</option>
                <option value="daily">日报模板</option>
                <option value="weekly">周报模板</option>
                <option value="monthly">月报模板</option>
                <option value="once">一次性模板</option>
                <option value="activity">活动模板</option>
              </select>
            </div>
            <div style="align-self: flex-end">
              <button class="btn search-btn btn-primary" onclick="searchTemplate()"  style="height:35px">
                <i class="fas fa-search"></i>
                查询
              </button>
              <button class="btn reset-btn btn-primary" onclick="resetTemplateSearch()"  style="height:35px">
                <i class="fas fa-undo"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 查询结果表格 -->
      <div class="card" id="templateSearchResult">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>模板编码</th>
                <th>模板名称</th>
                <th>模板描述</th>
                <th>模板类型</th>
                <th>创建人</th>
                <th>创建时间</th>
                <th>最近修改时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="templateSearchResultBody">
              <!-- 查询结果将在这里动态生成 -->
            </tbody>
          </table>
        </div>
      </div>

      <!-- 报告列表表格 -->
      <!-- <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>报告名称（编码）</th>
              <th>模板名称</th>
              <th>生成周期</th>
              <th>数据范围</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>生成时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>用户增长分析报告</td>
              <td>用户分析模板</td>
              <td>日报</td>
              <td>2023-07-15</td>
              <td><span class="tag tag-success">已发布</span></td>
              <td>2023-07-14 16:30</td>
              <td>2023-07-15 08:00</td>
              <td>
                <button class="btn publish-btn" data-id="1" style="color: var(--primary-color);"><i class="fas fa-paper-plane"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn delete-btn" data-id="1" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>产品销售周报</td>
              <td>销售分析模板</td>
              <td>周报</td>
              <td>2023-07-10至2023-07-16</td>
              <td><span class="tag tag-info">已生成</span></td>
              <td>2023-07-16 09:15</td>
              <td>2023-07-16 10:00</td>
              <td>
                <button class="btn publish-btn" data-id="2" style="color: var(--primary-color);"><i class="fas fa-paper-plane"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn delete-btn" data-id="2" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>6月运营数据分析</td>
              <td>月度分析模板</td>
              <td>月报</td>
              <td>2023-06-01至2023-06-30</td>
              <td><span class="tag tag-warning">草稿</span></td>
              <td>2023-07-01 14:45</td>
              <td>-</td>
              <td>
                <button class="btn publish-btn" data-id="3" style="color: var(--primary-color);"><i class="fas fa-paper-plane"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn delete-btn" data-id="3" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>活动效果评估报告</td>
              <td>活动分析模板</td>
              <td>一次性</td>
              <td>2023-07-01至2023-07-10</td>
              <td><span class="tag tag-success">已发布</span></td>
              <td>2023-07-11 09:20</td>
              <td>2023-07-11 11:30</td>
              <td>
                <button class="btn publish-btn" data-id="4" style="color: var(--primary-color);"><i class="fas fa-paper-plane"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editReportModal"><i class="fas fa-edit"></i></button>
                <button class="btn delete-btn" data-id="4" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item">4</div>
        <div class="pagination-item">5</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div> -->
    </div>

    <!-- 新增报告模态框 -->
    <div class="modal" id="addReportModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus"></i>
            模板详情
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addReportForm">
            <div class="form-group">
              <label for="reportName">报告名称 (编码)</label>
              <input type="text" id="reportName" name="reportName" required placeholder="用户分析模板" />
            </div>
            <div class="form-group">
              <label for="templateName">模板名称</label>
              <select id="templateName" name="templateName" required>
                <option value="">用户分析模板</option>
                <option value="user_analysis">用户分析模板 (user_analysis)</option>
                <option value="sales_analysis">销售分析模板 (sales_analysis)</option>
                <option value="monthly_analysis">月度分析模板 (monthly_analysis)</option>
                <option value="activity_analysis">活动分析模板 (activity_analysis)</option>
              </select>
            </div>
            <div class="form-group">
              <label for="generateCycle">日报</label>
              <select id="generateCycle" name="generateCycle" required>
                <option value="">请选择生成周期</option>
                <option value="daily">日报</option>
                <option value="weekly">周报</option>
                <option value="monthly">月报</option>
                <option value="once">一次性</option>
              </select>
            </div>
            <div class="form-group">
              <label for="dataRange">数据范围</label>
              <div style="display: flex; gap: 12px">
                <input type="date" id="startDate" name="startDate" required />
                <input type="date" id="endDate" name="endDate" required />
              </div>
            </div>

            <div class="form-group">
              <label for="reportDescription">报告描述</label>
              <textarea id="reportDescription" name="reportDescription" rows="3" placeholder="用于分析用户增长、活跃度等指标的模板"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addReportModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" type="submit">生成报告</button>
        </div>
      </div>
    </div>

    <!-- 编辑报告模态框 -->
    <div class="modal" id="editReportModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-edit"></i>
            编辑运营报告
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="editReportForm">
            <input type="hidden" id="editReportId" />
            <div class="form-group">
              <label for="editReportName">报告名称 (编码)</label>
              <input type="text" id="editReportName" name="reportName" required placeholder="请输入报告名称" />
            </div>
            <div class="form-group">
              <label for="editTemplateName">模板名称</label>
              <select id="editTemplateName" name="templateName" required>
                <option value="">请选择模板</option>
                <option value="user_analysis">用户分析模板 (user_analysis)</option>
                <option value="sales_analysis">销售分析模板 (sales_analysis)</option>
                <option value="monthly_analysis">月度分析模板 (monthly_analysis)</option>
                <option value="activity_analysis">活动分析模板 (activity_analysis)</option>
              </select>
            </div>
            <div class="form-group">
              <label for="editGenerateCycle">生成周期</label>
              <select id="editGenerateCycle" name="generateCycle" required>
                <option value="">请选择生成周期</option>
                <option value="daily">日报</option>
                <option value="weekly">周报</option>
                <option value="monthly">月报</option>
                <option value="once">一次性</option>
              </select>
            </div>
            <div class="form-group">
              <label for="editDataRange">数据范围</label>
              <div style="display: flex; gap: 12px">
                <input type="date" id="editStartDate" name="startDate" required />
                <input type="date" id="editEndDate" name="endDate" required />
              </div>
            </div>
            <div class="form-group">
              <label for="editReportContent">报告内容配置</label>
              <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 12px; height: 120px">
                <div style="display: flex; gap: 12px; margin-bottom: 12px">
                  <label style="display: flex; align-items: center">
                    <input type="checkbox" id="editUserGrowth" name="userGrowth" />
                    用户增长分析
                  </label>
                  <label style="display: flex; align-items: center">
                    <input type="checkbox" id="editSalesPerformance" name="salesPerformance" />
                    销售业绩分析
                  </label>
                  <label style="display: flex; align-items: center">
                    <input type="checkbox" id="editTrafficSource" name="trafficSource" />
                    流量来源分析
                  </label>
                </div>
                <div style="display: flex; gap: 12px">
                  <label style="display: flex; align-items: center">
                    <input type="checkbox" id="editConversionRate" name="conversionRate" />
                    转化率分析
                  </label>
                  <label style="display: flex; align-items: center">
                    <input type="checkbox" id="editCompetitorAnalysis" name="competitorAnalysis" />
                    竞品分析
                  </label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="editReportDescription">报告描述</label>
              <textarea id="editReportDescription" name="reportDescription" rows="3" placeholder="请输入报告描述"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('editReportModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="submitEditReport()">保存修改</button>
        </div>
      </div>
    </div>

    <!-- 发布确认弹窗 -->
    <div class="confirm-modal" id="publishConfirmModal">
      <div class="confirm-content">
        <div class="confirm-header">
          <div class="confirm-title">
            <i class="fas fa-paper-plane"></i>
            确认发布报告
          </div>
          <button class="confirm-close">&times;</button>
        </div>
        <div class="confirm-body">
          <input type="hidden" id="publishReportId" />
          <div class="form-row">
            <div class="form-group">
              <label for="publishTemplateCode">模板编码</label>
              <input type="text" id="publishTemplateCode" readonly />
            </div>
            <div class="form-group">
              <label for="publishTemplateName">模板名称</label>
              <input type="text" id="publishTemplateName" readonly />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="publishTime">发布时间</label>
              <input type="datetime-local" id="publishTime" required />
            </div>
            <div class="form-group">
              <label for="publishRegion">发布地域</label>
              <select id="publishRegion" required>
                <option value="">请选择地域</option>
                <option value="national">全国</option>
                <option value="north">华北地区</option>
                <option value="east">华东地区</option>
                <option value="south">华南地区</option>
                <option value="west">西部地区</option>
                <option value="central">中部地区</option>
              </select>
            </div>
          </div>
        </div>
        <div class="confirm-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" id="cancelPublish">取消</button>
          <button class="btn btn-primary" id="confirmPublish">确认发布</button>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="confirm-modal" id="deleteConfirmModal">
      <div class="confirm-content">
        <div class="confirm-header">
          <div class="confirm-title">
            <i class="fas fa-trash"></i>
            确认删除报告
          </div>
          <button class="confirm-close">&times;</button>
        </div>
        <div class="confirm-body">
          <input type="hidden" id="deleteReportId" />
          <div class="form-group">
            <label for="deleteTemplateCode">请输入模板编码进行验证</label>
            <input type="text" id="deleteTemplateCode" required placeholder="请输入模板编码" />
          </div>
          <div style="color: #f44336; font-size: 14px; margin-top: 8px">注意：删除操作不可逆，请谨慎操作！</div>
        </div>
        <div class="confirm-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" id="cancelDelete">取消</button>
          <button class="btn btn-primary" id="confirmDelete">确认删除</button>
        </div>
      </div>
    </div>

    <!-- 成功提示框 -->
    <div class="success-toast" id="publishSuccessToast">发布成功！</div>
    <div class="success-toast" id="deleteSuccessToast">删除成功！</div>
    <div class="success-toast" id="searchSuccessToast">查询成功！</div>

    <!-- 错误提示框 -->
    <div class="error-toast" id="deleteErrorToast"></div>
    <div class="error-toast" id="searchErrorToast"></div>

    <script src="js/common.js"></script>
    <script>
      // 报告数据（模拟）
      let reportData = [
        { id: 1, name: '用户增长分析报告', template: 'user_analysis', templateText: '用户分析模板', cycle: 'daily', cycleText: '日报', startDate: '2023-07-15', endDate: '2023-07-15', dataRange: '2023-07-15', status: 'published', statusText: '已发布', createTime: '2023-07-14 16:30', generateTime: '2023-07-15 08:00', content: { userGrowth: true, salesPerformance: true, trafficSource: false, conversionRate: true, competitorAnalysis: false }, description: '用户增长情况分析报告' },
        { id: 2, name: '产品销售周报', template: 'sales_analysis', templateText: '销售分析模板', cycle: 'weekly', cycleText: '周报', startDate: '2023-07-10', endDate: '2023-07-16', dataRange: '2023-07-10至2023-07-16', status: 'generated', statusText: '已生成', createTime: '2023-07-16 09:15', generateTime: '2023-07-16 10:00', content: { userGrowth: false, salesPerformance: true, trafficSource: true, conversionRate: true, competitorAnalysis: true }, description: '产品销售周分析报告' },
        { id: 3, name: '6月运营数据分析', template: 'monthly_analysis', templateText: '月度分析模板', cycle: 'monthly', cycleText: '月报', startDate: '2023-06-01', endDate: '2023-06-30', dataRange: '2023-06-01至2023-06-30', status: 'draft', statusText: '草稿', createTime: '2023-07-01 14:45', generateTime: '-', content: { userGrowth: true, salesPerformance: true, trafficSource: true, conversionRate: false, competitorAnalysis: false }, description: '6月份运营数据分析报告' },
        { id: 4, name: '活动效果评估报告', template: 'activity_analysis', templateText: '活动分析模板', cycle: 'once', cycleText: '一次性', startDate: '2023-07-01', endDate: '2023-07-10', dataRange: '2023-07-01至2023-07-10', status: 'published', statusText: '已发布', createTime: '2023-07-11 09:20', generateTime: '2023-07-11 11:30', content: { userGrowth: true, salesPerformance: false, trafficSource: true, conversionRate: true, competitorAnalysis: true }, description: '促销活动效果评估报告' },
      ];

      // 模板数据（模拟）
      let templateData = [
        { code: 'user_analysis', name: '用户分析模板', description: '用于分析用户增长、活跃度等指标的模板', type: 'daily', creator: '管理员', createTime: '2023-06-01', lastModifyTime: '2023-06-15', isDeleted: false },
        { code: 'sales_analysis', name: '销售分析模板', description: '用于分析产品销售情况的模板', type: 'weekly', creator: '管理员', createTime: '2023-06-05', lastModifyTime: '2023-06-20', isDeleted: false },
        { code: 'monthly_analysis', name: '月度分析模板', description: '用于月度运营数据分析的模板', type: 'monthly', creator: '管理员', createTime: '2023-05-20', lastModifyTime: '2023-06-25', isDeleted: false },
        { code: 'activity_analysis', name: '活动分析模板', description: '用于活动效果评估的模板', type: 'activity', creator: '管理员', createTime: '2023-06-10', lastModifyTime: '2023-07-01', isDeleted: false },
      ];

      // 发布记录存储数组
      let publishRecords = JSON.parse(localStorage.getItem('publishRecords')) || [];

      // 删除记录存储数组
      let deleteRecords = JSON.parse(localStorage.getItem('deleteRecords')) || [];

      // 页面加载完成后初始化事件监听
      document.addEventListener('DOMContentLoaded', function () {
        searchTemplate();
        // 为所有发布按钮添加点击事件
        const publishButtons = document.querySelectorAll('.publish-btn');
        publishButtons.forEach(button => {
          button.addEventListener('click', showPublishConfirm);
        });

        // 为所有删除按钮添加点击事件
        const deleteButtons = document.querySelectorAll('.delete-btn');
        deleteButtons.forEach(button => {
          button.addEventListener('click', showDeleteConfirm);
        });

        // 取消发布按钮事件
        document.getElementById('cancelPublish').addEventListener('click', function () {
          document.getElementById('publishConfirmModal').classList.remove('show');
        });

        // 确认发布按钮事件
        document.getElementById('confirmPublish').addEventListener('click', confirmPublish);

        // 关闭发布弹窗按钮事件
        document.querySelector('#publishConfirmModal .confirm-close').addEventListener('click', function () {
          document.getElementById('publishConfirmModal').classList.remove('show');
        });

        // 取消删除按钮事件
        document.getElementById('cancelDelete').addEventListener('click', function () {
          document.getElementById('deleteConfirmModal').classList.remove('show');
          document.getElementById('deleteTemplateCode').value = '';
        });

        // 确认删除按钮事件
        document.getElementById('confirmDelete').addEventListener('click', confirmDelete);

        // 关闭删除弹窗按钮事件
        document.querySelector('#deleteConfirmModal .confirm-close').addEventListener('click', function () {
          document.getElementById('deleteConfirmModal').classList.remove('show');
          document.getElementById('deleteTemplateCode').value = '';
        });
      });

      // 显示发布确认弹窗
      function showPublishConfirm() {
        const reportId = parseInt(this.getAttribute('data-id'));
        const report = reportData.find(item => item.id === reportId);

        if (report) {
          // 填充弹窗数据
          document.getElementById('publishReportId').value = report.id;
          document.getElementById('publishTemplateCode').value = report.template;
          document.getElementById('publishTemplateName').value = report.templateText;

          // 设置默认发布时间为当前时间
          const now = new Date();
          const dateTimeValue = now.toISOString().slice(0, 16);
          document.getElementById('publishTime').value = dateTimeValue;

          // 显示弹窗
          document.getElementById('publishConfirmModal').classList.add('show');
        }
      }

      // 确认发布处理
      function confirmPublish() {
        const reportId = parseInt(document.getElementById('publishReportId').value);
        const publishTime = document.getElementById('publishTime').value;
        const publishRegion = document.getElementById('publishRegion').value;

        // 简单校验
        if (!publishTime || !publishRegion) {
          alert('请填写完整的发布信息');
          return;
        }

        // 获取报告数据
        const reportIndex = reportData.findIndex(item => item.id === reportId);
        if (reportIndex !== -1) {
          // 更新报告状态为已发布
          reportData[reportIndex].status = 'published';
          reportData[reportIndex].statusText = '已发布';

          // 更新表格中的状态显示
          const rows = document.querySelector('.table tbody').rows;
          for (let i = 0; i < rows.length; i++) {
            const publishBtn = rows[i].querySelector('.publish-btn');
            if (publishBtn && parseInt(publishBtn.getAttribute('data-id')) === reportId) {
              const statusCell = rows[i].cells[4];
              statusCell.innerHTML = '<span class="tag tag-success">已发布</span>';
              break;
            }
          }

          // 保存发布记录
          const newRecord = {
            templateCode: reportData[reportIndex].template,
            templateName: reportData[reportIndex].templateText,
            publishTime: publishTime,
            region: publishRegion,
            publisher: '管理员', // 当前登录用户
            operateTime: new Date().toISOString(),
          };

          publishRecords.push(newRecord);
          localStorage.setItem('publishRecords', JSON.stringify(publishRecords));

          // 隐藏弹窗
          document.getElementById('publishConfirmModal').classList.remove('show');

          // 显示成功提示
          const toast = document.getElementById('publishSuccessToast');
          toast.classList.add('show');
          setTimeout(() => {
            toast.classList.remove('show');
          }, 3000);
        }
      }

      // 显示删除确认弹窗
      function showDeleteConfirm() {
        const reportId = parseInt(this.getAttribute('data-id'));
        document.getElementById('deleteReportId').value = reportId;
        document.getElementById('deleteTemplateCode').value = '';
        document.getElementById('deleteConfirmModal').classList.add('show');
      }

      // 确认删除处理
      function confirmDelete() {
        const reportId = parseInt(document.getElementById('deleteReportId').value);
        const templateCode = document.getElementById('deleteTemplateCode').value.trim();

        // 获取报告数据
        const reportIndex = reportData.findIndex(item => item.id === reportId);
        if (reportIndex === -1) {
          showErrorToast('报告不存在');
          return;
        }

        const report = reportData[reportIndex];

        // 1. 删除校验：验证模板编码是否存在
        const template = templateData.find(t => t.code === templateCode);
        if (!template) {
          showErrorToast('模板编码不存在');
          return;
        }

        // 验证模板编码是否与报告使用的模板一致
        if (templateCode !== report.template) {
          showErrorToast('模板编码与报告使用的模板不匹配');
          return;
        }

        // 验证模板是否已被关联的报告引用
        const relatedReports = reportData.filter(r => r.template === templateCode && r.id !== reportId);
        if (relatedReports.length > 0) {
          showErrorToast('该模板已关联生成其他报告，禁止删除');
          return;
        }

        // 2. 执行删除：软删除，标记删除状态
        const templateIndex = templateData.findIndex(t => t.code === templateCode);
        if (templateIndex !== -1) {
          templateData[templateIndex].isDeleted = true;
        }

        // 从表格中移除该报告行
        const rows = document.querySelector('.table tbody').rows;
        for (let i = 0; i < rows.length; i++) {
          const deleteBtn = rows[i].querySelector('.delete-btn');
          if (deleteBtn && parseInt(deleteBtn.getAttribute('data-id')) === reportId) {
            rows[i].parentNode.removeChild(rows[i]);
            break;
          }
        }

        // 从数据中移除该报告
        reportData.splice(reportIndex, 1);

        // 3. 信息记录：保存删除相关信息
        const deleteTime = new Date().toISOString(); // 精确到秒的系统时间
        const newDeleteRecord = {
          templateCode: templateCode,
          templateName: template.name,
          deletedBy: '管理员', // 当前操作用户账号
          deleteTime: deleteTime,
        };

        deleteRecords.push(newDeleteRecord);
        localStorage.setItem('deleteRecords', JSON.stringify(deleteRecords));

        // 隐藏弹窗
        document.getElementById('deleteConfirmModal').classList.remove('show');

        // 显示成功提示
        const toast = document.getElementById('deleteSuccessToast');
        toast.classList.add('show');
        setTimeout(() => {
          toast.classList.remove('show');
        }, 3000);
      }

      // 显示错误提示
      function showErrorToast(message) {
        const toast = document.getElementById('deleteErrorToast');
        toast.textContent = message;
        toast.classList.add('show');
        setTimeout(() => {
          toast.classList.remove('show');
        }, 3000);
      }

      // 搜索模板功能实现
      function searchTemplate() {
        // 获取查询条件
        const templateCode = document.getElementById('templateCode').value.trim().toLowerCase();
        const templateName = document.getElementById('templateNameQuery').value.trim().toLowerCase();
        const templateType = document.getElementById('templateType').value;

        // 过滤数据
        let filteredTemplates = templateData.filter(template => {
          // 过滤已删除的模板
          if (template.isDeleted) return false;

          // 按编码过滤
          if (templateCode && !template.code.toLowerCase().includes(templateCode)) {
            return false;
          }

          // 按名称过滤
          if (templateName && !template.name.toLowerCase().includes(templateName)) {
            return false;
          }

          // 按类型过滤
          if (templateType !== 'all' && template.type !== templateType) {
            return false;
          }

          return true;
        });

        // 获取结果表格体
        const resultBody = document.getElementById('templateSearchResultBody');

        // 清空现有结果
        resultBody.innerHTML = '';

        // 显示结果
        if (filteredTemplates.length > 0) {
          // 遍历过滤后的模板数据，创建表格行
          filteredTemplates.forEach(template => {
            const row = document.createElement('tr');

            // 转换模板类型为中文显示
            let typeText = '';
            switch (template.type) {
              case 'daily':
                typeText = '日报模板';
                break;
              case 'weekly':
                typeText = '周报模板';
                break;
              case 'monthly':
                typeText = '月报模板';
                break;
              case 'once':
                typeText = '一次性模板';
                break;
              case 'activity':
                typeText = '活动模板';
                break;
              default:
                typeText = '未知类型';
            }

            row.innerHTML = `
            <td>${template.code}</td>
            <td>${template.name}</td>
            <td>${template.description}</td>
            <td>${typeText}</td>
            <td>${template.creator}</td>
            <td>${template.createTime}</td>
            <td>${template.lastModifyTime}</td>
            <td>
              <button class="btn" style="color: var(--primary-color);" onclick="useTemplate('${template.code}')"><i class="fas fa-plus-circle"></i> 显示详情</button>
            </td>
          `;

            resultBody.appendChild(row);
          });

          // 显示查询结果表格
          document.getElementById('templateSearchResult').style.display = 'block';

          // 显示成功提示
          const toast = document.getElementById('searchSuccessToast');
          toast.textContent = `查询成功，找到 ${filteredTemplates.length} 条记录`;
          toast.classList.add('show');
          setTimeout(() => {
            toast.classList.remove('show');
          }, 3000);
        } else {
          // 没有找到结果
          const row = document.createElement('tr');
          row.innerHTML = `<td colspan="8" style="text-align: center; padding: 20px;">没有找到匹配的模板</td>`;
          resultBody.appendChild(row);

          // 显示查询结果表格
          document.getElementById('templateSearchResult').style.display = 'block';

          // 显示错误提示
          const toast = document.getElementById('searchErrorToast');
          toast.textContent = '没有找到匹配的模板';
          toast.classList.add('show');
          setTimeout(() => {
            toast.classList.remove('show');
          }, 3000);
        }
      }

      // 使用模板功能
      function useTemplate(templateCode) {
        // 找到对应的模板
        const template = templateData.find(t => t.code === templateCode);
        if (template) {
          // 打开新增报告模态框
          const modal = document.getElementById('addReportModal');
          modal.classList.add('show');

          // 自动选择对应的模板
          document.getElementById('templateName').value = template.code;
        }
      }

      // 重置查询条件
      function resetTemplateSearch() {
        document.getElementById('templateCode').value = '';
        document.getElementById('templateNameQuery').value = '';
        document.getElementById('templateType').value = 'all';

        // 隐藏查询结果
        searchTemplate();
      }

      // 编辑报告提交
      function submitEditReport() {
        document.getElementById('editReportModal').classList.remove('show');

        // 显示成功提示
        const toast = document.getElementById('searchSuccessToast');
        toast.textContent = '报告编辑成功';
        toast.classList.add('show');
        setTimeout(() => {
          toast.classList.remove('show');
        }, 3000);
      }
    </script>
  </body>
</html>
