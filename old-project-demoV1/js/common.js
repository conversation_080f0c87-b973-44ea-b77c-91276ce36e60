// 侧边栏菜单切换
function initSidebar() {
  const menuItems = document.querySelectorAll('.menu-item');
  menuItems.forEach(item => {
    item.addEventListener('click', () => {
      menuItems.forEach(mi => mi.classList.remove('active'));
      item.classList.add('active');

      // 获取data-href属性值并跳转
      const href = item.getAttribute('data-href');
      if (href) {
        window.location.href = href;
      }
    });
  });
}

// 模态框控制
function initModals() {
  const modalTriggers = document.querySelectorAll('[data-modal-target]');
  const modals = document.querySelectorAll('.modal');
  const modalCloses = document.querySelectorAll('.modal-close');
  const modalBackdrops = document.querySelectorAll('.modal');

  modalTriggers.forEach(trigger => {
    trigger.addEventListener('click', () => {
      const modalId = trigger.getAttribute('data-modal-target');
      const modal = document.getElementById(modalId);
      modal.classList.add('show');
    });
  });

  modalCloses.forEach(close => {
    close.addEventListener('click', () => {
      const modal = close.closest('.modal');
      modal.classList.remove('show');
    });
  });

  modalBackdrops.forEach(backdrop => {
    backdrop.addEventListener('click', e => {
      if (e.target === backdrop) {
        backdrop.classList.remove('show');
      }
    });
  });
}

// 标签页切换
function initTabs() {
  const tabItems = document.querySelectorAll('.tab-item');
  tabItems.forEach(item => {
    item.addEventListener('click', () => {
      const tabId = item.getAttribute('data-tab-target');
      const tabContent = document.getElementById(tabId);

      // 移除所有活动状态
      document.querySelectorAll('.tab-item').forEach(ti => ti.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));

      // 添加当前活动状态
      item.classList.add('active');
      tabContent.classList.add('active');
    });
  });
}

// 下拉菜单
function initDropdowns() {
  const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
  dropdownToggles.forEach(toggle => {
    toggle.addEventListener('click', e => {
      e.stopPropagation();
      const dropdown = toggle.closest('.dropdown');
      dropdown.classList.toggle('show');
    });
  });

  // 点击其他地方关闭下拉菜单
  document.addEventListener('click', () => {
    document.querySelectorAll('.dropdown.show').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
  });
}

// 步骤条控制
function initSteps() {
  const stepButtons = document.querySelectorAll('[data-step]');
  stepButtons.forEach(button => {
    button.addEventListener('click', () => {
      const step = parseInt(button.getAttribute('data-step'));
      const steps = document.querySelectorAll('.step');

      steps.forEach((s, index) => {
        if (index < step) {
          s.classList.add('active');
        } else {
          s.classList.remove('active');
        }
      });
    });
  });
}

// 表单验证
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return true;

  let isValid = true;
  const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

  inputs.forEach(input => {
    if (!input.value.trim()) {
      isValid = false;
      input.classList.add('error');
      setTimeout(() => {
        input.classList.remove('error');
      }, 3000);
    }
  });

  return isValid;
}

// 初始化所有组件
// 初始化菜单展开收起功能
function initMenuExpandCollapse() {
  const parentItems = document.querySelectorAll('.menu-item.parent');

  // // 初始化：所有一级菜单默认收起
  // parentItems.forEach(item => {
  //   if (!item.closest('.sub-menu')) {
  //     // 只处理一级菜单
  //     const group = item.getAttribute('data-group');
  //     const subMenu = document.getElementById(`${group}-group`);
  //     const arrow = item.querySelector('.menu-arrow');

  //     subMenu.style.display = 'none';
  //     arrow.classList.remove('fa-chevron-down');
  //     arrow.classList.add('fa-chevron-right');
  //   }
  // });

  // // 初始化：默认展开的菜单选中第一个子菜单项
  // parentItems.forEach(item => {
  //   const group = item.getAttribute('data-group');
  //   const subMenu = document.getElementById(`${group}-group`);
  //   if (subMenu && subMenu.style.display !== 'none') {
  //     const firstChild = subMenu.querySelector('.menu-item.child:first-child');
  //     if (firstChild) {
  //       firstChild.classList.add('active');
  //     }
  //   }
  // });

  parentItems.forEach(item => {
    item.addEventListener('click', function (e) {
      // 阻止事件冒泡，避免触发父级菜单的点击事件
      e.stopPropagation();

      const group = this.getAttribute('data-group');
      const subMenu = document.getElementById(`${group}-group`);
      const arrow = this.querySelector('.menu-arrow');

      console.log("==========",group,subMenu,arrow);

      // 切换当前菜单显示状态
      const isExpanded = subMenu.style.display === 'block';
      console.log("isExpanded",isExpanded);
      // 如果是三级菜单的父项，只处理当前层级
      if (this.closest('.sub-menu')) {
        // 收起当前菜单的其他子菜单
        const siblings = this.parentNode.querySelectorAll('.menu-item.parent');
        siblings.forEach(sibling => {
          if (sibling !== this) {
            const siblingGroup = sibling.getAttribute('data-group');
            const siblingSubMenu = document.getElementById(`${siblingGroup}-group`);
            const siblingArrow = sibling.querySelector('.menu-arrow');

            siblingSubMenu.style.display = 'none';
            siblingArrow.classList.remove('fa-chevron-down');
            siblingArrow.classList.add('fa-chevron-right');
          }
        });
      } else {
        // 收起所有其他一级菜单并移除其子菜单选中状态
        parentItems.forEach(otherItem => {
          if (otherItem !== item && !otherItem.closest('.sub-menu')) {
            const otherGroup = otherItem.getAttribute('data-group');
            const otherSubMenu = document.getElementById(`${otherGroup}-group`);
            const otherArrow = otherItem.querySelector('.menu-arrow');

            otherSubMenu.style.display = 'none';
            otherArrow.classList.remove('fa-chevron-down');
            otherArrow.classList.add('fa-chevron-right');

            // 递归收起所有子菜单
            const subParentItems = otherSubMenu.querySelectorAll('.menu-item.parent');
            subParentItems.forEach(subParent => {
              const subParentGroup = subParent.getAttribute('data-group');
              const subParentSubMenu = document.getElementById(`${subParentGroup}-group`);
              const subParentArrow = subParent.querySelector('.menu-arrow');

              subParentSubMenu.style.display = 'none';
              subParentArrow.classList.remove('fa-chevron-down');
              subParentArrow.classList.add('fa-chevron-right');
            });
          }
        });
      }

      // 设置当前菜单状态
      if (isExpanded) {
        subMenu.style.display = 'none';
        arrow.classList.remove('fa-chevron-down');
        arrow.classList.add('fa-chevron-right');
      } else {
        subMenu.style.display = 'block';
        arrow.classList.remove('fa-chevron-right');
        arrow.classList.add('fa-chevron-down');

        // 选中当前菜单的第一个子菜单项
        const firstChild = subMenu.querySelector('.menu-item.child:first-child');
        if (firstChild) {
          // 移除当前菜单下所有子菜单项的active类
          const children = subMenu.querySelectorAll('.menu-item.child');
          children.forEach(child => child.classList.remove('active'));
          // 给第一个子菜单项添加active类
          firstChild.classList.add('active');
        }
      }
    });
  });
}

// 初始化子菜单点击事件
function initSubMenu() {
  const subMenuItems = document.querySelectorAll('[data-href]');
  subMenuItems.forEach(item => {
    item.addEventListener('click', e => {
      e.stopPropagation(); // 防止事件冒泡到父元素
      const href = item.getAttribute('data-href');
      if (href) {
        window.location.href = href;
      }
    });
  });
}

// 页面导航函数
function navigateToPage(pageUrl) {
  window.location.href = pageUrl;
}

// 面包屑导航刷新效果
function initBreadcrumbRefresh() {
  const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
  breadcrumbItems.forEach(item => {
    item.addEventListener('click', (e) => {
      // 如果是链接，阻止默认行为
      const link = item.querySelector('a');
      if (link) {
        e.preventDefault();
        // 添加旋转动画类
        item.classList.add('refreshing');
        // 1秒后移除动画类并跳转
        setTimeout(() => {
          item.classList.remove('refreshing');
          window.location.href = link.getAttribute('href');
        }, 1000);
      } else if (item.classList.contains('active')) {
        // 为当前激活的项添加刷新动画
        item.classList.add('refreshing');
        // 1秒后移除动画类并刷新页面
        setTimeout(() => {
          item.classList.remove('refreshing');
          location.reload();
        }, 1000);
      }
    });
  });
}


// 初始化所有组件
function initComponents() {
  initSidebar();
  initModals();
  initTabs();
  initDropdowns();
  initSteps();
  initSubMenu();
  initMenuExpandCollapse();

  // 添加表单提交事件
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', e => {
      if (!validateForm(form.id)) {
        e.preventDefault();
        alert('请填写所有必填字段');
      }
    });
  });
}

// 页面加载完成后初始化组件
document.addEventListener('DOMContentLoaded', initComponents);

/**
 * 公共接口调用方法，处理跨域请求
 * @param {string} url - 请求URL
 * @param {string} method - 请求方法 (GET, POST, PUT, DELETE等)
 * @param {object} data - 请求数据
 * @param {object} headers - 请求头
 * @returns {Promise} - 返回Promise对象
 */
function apiRequest(url, method = 'GET', data = null, headers = {}) {
  // 设置默认请求头
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...headers,
  };

  // 配置请求参数
  const options = {
    method,
    headers: defaultHeaders,
    credentials: 'include', // 允许跨域请求携带cookie
    mode: 'cors', // 启用跨域请求
  };

  // 如果有数据且不是GET请求，添加body
  if (data && method !== 'GET') {
    options.body = JSON.stringify(data);
  }

  // 处理GET请求的数据
  if (data && method === 'GET') {
    const queryString = new URLSearchParams(data).toString();
    url = `${url}${url.includes('?') ? '&' : '?'}${queryString}`;
  }

  // var newUrl = 'https://httpbin.org' + url;
var newUrl = 'http:/localhost:8000' + url;
  // fetch('https://httpbin.org/ip')
  //          .then(response => response.json())
  //          .then(data => console.log(data))
  //          .catch(error => console.error('Error:', error));

  // 发送请求
  return fetch(newUrl, options)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .catch(error => {
      console.error('API请求错误:', error);
      // 可以在这里添加全局错误处理逻辑
      throw error;
    });
}
