<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数智化运营平台 - 模板复制</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#4080FF',
                        success: '#00B42A',
                        warning: '#FF7D00',
                        danger: '#F53F3F',
                        info: '#86909C',
                        light: '#F2F3F5',
                        dark: '#1D2129',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        .content-auto {
            content-visibility: auto;
        }
        .menu-active {
            background-color: rgba(22, 93, 255, 0.1);
            color: #165DFF;
            border-left: 4px solid #165DFF;
        }
        .btn-primary {
            background-color: #165DFF;
            color: white;
            transition: all 200ms;
        }
        .btn-primary:hover {
            background-color: rgba(22, 93, 255, 0.9);
        }
        .btn-secondary {
            background-color: white;
            color: #165DFF;
            border: 1px solid #165DFF;
            transition: all 200ms;
        }
        .btn-secondary:hover {
            background-color: rgba(22, 93, 255, 0.05);
        }
        .btn-danger {
            background-color: #F53F3F;
            color: white;
            transition: all 200ms;
        }
        .btn-danger:hover {
            background-color: rgba(245, 63, 63, 0.9);
        }
        .panel {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            border: 1px solid #F3F4F6;
        }
        .panel-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #F3F4F6;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .panel-body {
            padding: 1rem;
        }
        .skeleton {
            animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            background-color: #F3F4F6;
            border-radius: 0.375rem;
        }
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-inter text-dark">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex  overflow-hidden"  style="margin-top: 64px;">
        <!-- 侧边栏 -->
       

        <!-- 主内容区 -->
        <div class="flex-1 overflow-y-auto p-6">
            <!-- 面包屑导航 -->
            <div class="flex items-center text-sm text-gray-500 mb-6">
                <a href="index.html" class="hover:text-primary transition-colors duration-200">首页</a>
                <i class="fas fa-chevron-right mx-2 text-xs"></i>
                <a href="operation_views.html" class="hover:text-primary transition-colors duration-200">运营视图</a>
                <i class="fas fa-chevron-right mx-2 text-xs"></i>
                <a href="#" class="hover:text-primary transition-colors duration-200">模板管理</a>
                <i class="fas fa-chevron-right mx-2 text-xs"></i>
                <span class="text-primary">模板复制</span>
            </div>

            <!-- 页面标题 -->
            <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-semibold text-gray-800 mb-6">模板复制</h1>

            <!-- 工具栏 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-6 flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center gap-3">
                    <button id="batchCopyBtn" class="px-4 py-2 rounded-lg btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-copy"></i>
                        <span>批量复制</span>
                    </button>
                    <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
                        <i class="fas fa-filter"></i>
                        <span>筛选</span>
                    </button>
                    <button class="px-4 py-2 rounded-lg btn-secondary flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        <span>导出日志</span>
                    </button>
                </div>
                <div class="relative">
                    <input type="text" placeholder="搜索模板名称或ID" class="pl-9 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full sm:w-64">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <!-- 复制设置面板 -->
            <div class="panel mb-6">
                <div class="panel-header">
                    <h2 class="text-sm font-medium text-gray-700">复制设置</h2>
                </div>
                <div class="panel-body grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">复制模式</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="copyMode" value="single" class="text-primary focus:ring-primary/30" checked>
                                <span class="ml-2 text-sm text-gray-700">单模板复制</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="copyMode" value="batch" class="text-primary focus:ring-primary/30">
                                <span class="ml-2 text-sm text-gray-700">批量复制</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">复制方式</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="copyType" value="complete" class="text-primary focus:ring-primary/30" checked>
                                <span class="ml-2 text-sm text-gray-700">完全复制</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="copyType" value="structure" class="text-primary focus:ring-primary/30">
                                <span class="ml-2 text-sm text-gray-700">结构复制</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">目标模板名称</label>
                        <input type="text" placeholder="输入新模板名称" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200" id="targetTemplateName">
                        <p class="text-xs text-gray-500 mt-1">模板名称不能超过50个字符</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">目标模板分类</label>
                        <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                            <option value="">请选择模板分类</option>
                            <option value="1">全国模板</option>
                            <option value="2">分省模板</option>
                            <option value="3">市级模板</option>
                            <option value="4">区县级模板</option>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板描述 (选填)</label>
                        <textarea placeholder="输入模板描述" rows="3" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200"></textarea>
                    </div>
                </div>
            </div>

            <!-- 复制进度面板 -->
            <div class="panel mb-6" id="copyProgressPanel" style="display: none;">
                <div class="panel-header">
                    <h2 class="text-sm font-medium text-gray-700">复制进度</h2>
                </div>
                <div class="panel-body space-y-6">
                    <div>
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-xs text-gray-500">加载阶段</span>
                            <span class="text-xs font-medium text-gray-700" id="loadingProgressText">0%</span>
                        </div>
                        <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                            <div class="absolute top-0 left-0 h-full bg-primary rounded-full" id="loadingProgressBar" style="width: 0%;"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-xs text-gray-500">查询阶段</span>
                            <span class="text-xs font-medium text-gray-700" id="queryProgressText">0%</span>
                        </div>
                        <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                            <div class="absolute top-0 left-0 h-full bg-secondary rounded-full" id="queryProgressBar" style="width: 0%;"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-xs text-gray-500">复制阶段</span>
                            <span class="text-xs font-medium text-gray-700" id="copyProgressText">0%</span>
                        </div>
                        <div class="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                            <div class="absolute top-0 left-0 h-full bg-success rounded-full" id="copyProgressBar" style="width: 0%;"></div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between text-xs text-gray-500 space-y-2 flex-wrap gap-4">
                        <div class="flex items-center gap-1">
                            <i class="fas fa-clock mr-1"></i>
                            <span>已耗时: <span id="elapsedTime">0秒</span></span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-tachometer-alt mr-1"></i>
                            <span>传输速度: <span id="transferSpeed">0 KB/s</span></span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-hourglass-half mr-1"></i>
                            <span>预计剩余: <span id="remainingTime">--</span></span>
                        </div>
                        <button class="text-danger hover:text-danger/80 transition-colors duration-200 flex items-center gap-1 ml-auto" id="cancelCopyBtn">
                            <i class="fas fa-times mr-1"></i>
                            <span>取消复制</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模板搜索和选择面板 -->
            <div class="panel mb-6">
                <div class="panel-header">
                    <h2 class="text-sm font-medium text-gray-700">模板搜索与选择</h2>
                    <button class="text-xs text-primary hover:text-primary/80 transition-colors duration-200">
                        <i class="fas fa-sliders-h mr-1"></i>高级搜索
                    </button>
                </div>
                <div class="panel-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">模板名称</label>
                            <input type="text" placeholder="输入模板名称" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">模板类型</label>
                            <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                                <option value="">全部模板类型</option>
                                <option value="1">全国模板</option>
                                <option value="2">分省模板</option>
                                <option value="3">市级模板</option>
                                <option value="4">区县级模板</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">行业分类</label>
                            <select class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 bg-white">
                                <option value="">全部行业</option>
                                <option value="1">通用</option>
                                <option value="2">零售行业</option>
                                <option value="3">金融行业</option>
                                <option value="4">制造业</option>
                                <option value="5">服务业</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center justify-end gap-3 mb-6">
                        <button class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm">重置</button>
                        <button class="px-4 py-2 rounded-lg btn-primary flex items-center justify-center gap-2 text-sm">
                            <i class="fas fa-search"></i>
                            <span>查询</span>
                        </button>
                    </div>

                    <!-- 模板列表 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
                                        <input type="checkbox" id="selectAllTemplates" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30">
                                    </th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板ID</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板类型</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业分类</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本号</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="templateList">
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="templateCheckbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" data-id="TPL-20230501-001" data-name="区域销售分析模板">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                                                <i class="fas fa-chart-bar text-gray-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">区域销售分析模板</div>
                                                <div class="text-xs text-gray-500">销售分析 > 区域分析</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">TPL-20230501-001</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">分省模板</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">零售行业</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">v1.0.0</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200" onclick="copyTemplate('区域销售分析模板', 'TPL-20230501-001')">复制</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="templateCheckbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" data-id="TPL-20230415-002" data-name="客户满意度分析模板">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                                                <i class="fas fa-chart-pie text-gray-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">客户满意度分析模板</div>
                                                <div class="text-xs text-gray-500">客户分析 > 满意度分析</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">TPL-20230415-002</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">全国模板</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">服务业</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-15</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">v1.1.0</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200" onclick="copyTemplate('客户满意度分析模板', 'TPL-20230415-002')">复制</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="templateCheckbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary/30" data-id="TPL-20230320-003" data-name="产品销量趋势模板">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-md flex items-center justify-center">
                                                <i class="fas fa-chart-line text-gray-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">产品销量趋势模板</div>
                                                <div class="text-xs text-gray-500">销售分析 > 趋势分析</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">TPL-20230320-003</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">市级模板</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">制造业</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-20</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">v1.2.0</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button class="text-primary hover:text-primary/80 transition-colors duration-200" onclick="copyTemplate('产品销量趋势模板', 'TPL-20230320-003')">复制</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">12</span> 条记录
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">上一页</span>
                                        <i class="fas fa-chevron-left text-xs"></i>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-primary/10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        1
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        2
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        3
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        4
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">下一页</span>
                                        <i class="fas fa-chevron-right text-xs"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 复制历史日志面板 -->
            <div class="panel mb-6">
                <div class="panel-header">
                    <h2 class="text-sm font-medium text-gray-700">复制操作历史</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作ID</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">源模板名称</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标模板名称</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">复制方式</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">CPY-20230512-001</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">区域销售分析模板</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">区域销售分析模板-副本</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">完全复制</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-12 09:30:25</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">成功</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-primary hover:text-primary/80 transition-colors duration-200 cursor-pointer">查看详情</td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">CPY-20230510-002</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">客户满意度分析模板</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">客户满意度分析模板-副本</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">结构复制</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10 14:22:18</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-success/10 text-success">成功</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-primary hover:text-primary/80 transition-colors duration-200 cursor-pointer">查看详情</td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">CPY-20230508-003</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">产品销量趋势模板</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">产品销量趋势模板-副本</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">完全复制</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-08 16:45:30</td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-danger/10 text-danger">失败</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-primary hover:text-primary/80 transition-colors duration-200 cursor-pointer">查看详情</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">8</span> 条记录
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">上一页</span>
                                    <i class="fas fa-chevron-left text-xs"></i>
                                </a>
                                <a href="#" aria-current="page" class="z-10 bg-primary/10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    2
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    3
                                </a>
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">下一页</span>
                                    <i class="fas fa-chevron-right text-xs"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 复制确认模态框 -->
    <div id="copyModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-lg w-full max-w-md overflow-hidden flex flex-col">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">确认复制模板</h3>
                <button id="closeCopyModal" class="text-gray-400 hover:text-gray-500 transition-colors duration-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                        <i class="fas fa-copy text-primary text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900" id="copyModalTitle">确认复制模板？</h4>
                        <p class="text-xs text-gray-500 mt-1" id="copyModalMessage">您确定要复制此模板吗？</p>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between text-sm mb-2">
                        <span class="text-gray-500">源模板名称:</span>
                        <span class="font-medium text-gray-900" id="copyModalSourceName">区域销售分析模板</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mb-2">
                        <span class="text-gray-500">源模板ID:</span>
                        <span class="font-medium text-gray-900" id="copyModalSourceId">TPL-20230501-001</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mb-2">
                        <span class="text-gray-500">目标模板名称:</span>
                        <span class="font-medium text-gray-900" id="copyModalTargetName">区域销售分析模板-副本</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">复制方式:</span>
                        <span class="font-medium text-gray-900" id="copyModalCopyType">完全复制</span>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                    <button id="cancelCopyModal" class="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm">取消</button>
                    <button id="confirmCopyModal" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200 text-sm">确认复制</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 复制模板函数
        function copyTemplate(templateName, templateId) {
            // 设置模态框内容
            document.getElementById('copyModalSourceName').textContent = templateName;
            document.getElementById('copyModalSourceId').textContent = templateId;
            
            // 根据复制方式设置目标模板名称
            const copyType = document.querySelector('input[name="copyType"]:checked').value;
            let targetName = templateName + '-副本';
            
            if (copyType === 'structure') {
                targetName = templateName + '-结构副本';
            }
            
            document.getElementById('copyModalTargetName').textContent = targetName;
            document.getElementById('copyModalCopyType').textContent = copyType === 'complete' ? '完全复制' : '结构复制';
            document.getElementById('targetTemplateName').value = targetName;
            
            // 显示模态框
            document.getElementById('copyModal').classList.remove('hidden');
            document.getElementById('copyModal').classList.add('flex');
        }

        // 关闭复制模态框
        document.getElementById('closeCopyModal').addEventListener('click', function() {
            document.getElementById('copyModal').classList.add('hidden');
            document.getElementById('copyModal').classList.remove('flex');
        });

        document.getElementById('cancelCopyModal').addEventListener('click', function() {
            document.getElementById('copyModal').classList.add('hidden');
            document.getElementById('copyModal').classList.remove('flex');
        });

        // 确认复制
        document.getElementById('confirmCopyModal').addEventListener('click', function() {
            document.getElementById('copyModal').classList.add('hidden');
            document.getElementById('copyModal').classList.remove('flex');
            
            // 显示进度面板
            document.getElementById('copyProgressPanel').style.display = 'block';
            
            // 开始模拟复制进度
            startCopySimulation();
        });

        // 取消复制
        document.getElementById('cancelCopyBtn').addEventListener('click', function() {
            document.getElementById('copyProgressPanel').style.display = 'none';
            showNotification('复制已取消', 'info');
        });

        // 模拟复制进度
        function startCopySimulation() {
            let loadingProgress = 0;
            let queryProgress = 0;
            let copyProgress = 0;
            let elapsedTime = 0;
            let isComplete = false;

            // 模拟耗时计算
            const startTime = Date.now();
            const totalDuration = 8000; // 总模拟时间8秒

            // 更新进度条
            const updateProgress = () => {
                if (isComplete) return;

                elapsedTime = Math.floor((Date.now() - startTime) / 1000);
                document.getElementById('elapsedTime').textContent = elapsedTime + '秒';

                // 计算进度
                const progressPercent = Math.min(100, Math.floor((Date.now() - startTime) / totalDuration * 100));

                if (progressPercent < 33) {
                    // 加载阶段
                    loadingProgress = progressPercent * 3;
                    document.getElementById('loadingProgressBar').style.width = loadingProgress + '%';
                    document.getElementById('loadingProgressText').textContent = Math.round(loadingProgress) + '%';
                    document.getElementById('transferSpeed').textContent = Math.floor(Math.random() * 50 + 10) + ' KB/s';
                } else if (progressPercent < 66) {
                    // 查询阶段
                    loadingProgress = 100;
                    queryProgress = (progressPercent - 33) * 3;
                    document.getElementById('loadingProgressBar').style.width = '100%';
                    document.getElementById('loadingProgressText').textContent = '100%';
                    document.getElementById('queryProgressBar').style.width = queryProgress + '%';
                    document.getElementById('queryProgressText').textContent = Math.round(queryProgress) + '%';
                    document.getElementById('transferSpeed').textContent = Math.floor(Math.random() * 100 + 50) + ' KB/s';
                } else {
                    // 复制阶段
                    loadingProgress = 100;
                    queryProgress = 100;
                    copyProgress = (progressPercent - 66) * 3;
                    document.getElementById('loadingProgressBar').style.width = '100%';
                    document.getElementById('loadingProgressText').textContent = '100%';
                    document.getElementById('queryProgressBar').style.width = '100%';
                    document.getElementById('queryProgressText').textContent = '100%';
                    document.getElementById('copyProgressBar').style.width = copyProgress + '%';
                    document.getElementById('copyProgressText').textContent = Math.round(copyProgress) + '%';
                    document.getElementById('transferSpeed').textContent = Math.floor(Math.random() * 150 + 100) + ' KB/s';
                }

                // 计算剩余时间
                if (progressPercent > 0 && progressPercent < 100) {
                    const estimatedTotalTime = Math.round(totalDuration / 1000);
                    const remainingTime = estimatedTotalTime - elapsedTime;
                    document.getElementById('remainingTime').textContent = remainingTime + '秒';
                } else if (progressPercent === 100) {
                    document.getElementById('remainingTime').textContent = '0秒';
                    isComplete = true;
                    setTimeout(() => {
                        showNotification('模板复制成功', 'success');
                        document.getElementById('copyProgressPanel').style.display = 'none';
                    }, 1000);
                }

                if (!isComplete) {
                    requestAnimationFrame(updateProgress);
                }
            };

            updateProgress();
        }

        // 全选模板
        document.getElementById('selectAllTemplates').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.templateCheckbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBatchCopyButton();
        });

        // 单个模板选择
        document.querySelectorAll('.templateCheckbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchCopyButton);
        });

        // 更新批量复制按钮状态
        function updateBatchCopyButton() {
            const checkedCount = document.querySelectorAll('.templateCheckbox:checked').length;
            const batchCopyBtn = document.getElementById('batchCopyBtn');

            if (checkedCount > 1) {
                batchCopyBtn.removeAttribute('disabled');
            } else {
                batchCopyBtn.setAttribute('disabled', true);
            }
        }

        // 批量复制按钮点击事件
        document.getElementById('batchCopyBtn').addEventListener('click', function() {
            const checkedTemplates = document.querySelectorAll('.templateCheckbox:checked');
            const templateNames = Array.from(checkedTemplates).map(checkbox => checkbox.dataset.name).join('、');
            
            // 设置模态框内容
            document.getElementById('copyModalTitle').textContent = '确认批量复制模板';
            document.getElementById('copyModalMessage').textContent = `您确定要复制选中的${checkedTemplates.length}个模板吗？`;
            document.getElementById('copyModalSourceName').textContent = templateNames;
            document.getElementById('copyModalSourceId').textContent = `共${checkedTemplates.length}个模板`;
            document.getElementById('copyModalTargetName').textContent = '批量复制的模板将自动添加"-副本"后缀';
            
            // 根据复制方式设置显示
            const copyType = document.querySelector('input[name="copyType"]:checked').value;
            document.getElementById('copyModalCopyType').textContent = copyType === 'complete' ? '完全复制' : '结构复制';
            
            // 显示模态框
            document.getElementById('copyModal').classList.remove('hidden');
            document.getElementById('copyModal').classList.add('flex');
        });

        // 通知提示函数
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg flex items-center z-50 transform transition-all duration-300 translate-x-full';

            // 设置通知类型样式
            if (type === 'success') {
                notification.classList.add('bg-success/10', 'text-success', 'border-l-4', 'border-success');
                notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i> <span>${message}</span>`;
            } else if (type === 'error') {
                notification.classList.add('bg-danger/10', 'text-danger', 'border-l-4', 'border-danger');
                notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i> <span>${message}</span>`;
            } else {
                notification.classList.add('bg-primary/10', 'text-primary', 'border-l-4', 'border-primary');
                notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i> <span>${message}</span>`;
            }

            // 添加到文档
            document.body.appendChild(notification);

            // 显示通知
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 3秒后隐藏通知
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>