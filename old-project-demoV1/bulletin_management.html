<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智化运营平台 - 运营通报管理</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
      }
      .tab {
        padding: 14px 24px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: var(--transition);
        color: var(--text-secondary);
      }
      .tab.active {
        border-bottom-color: var(--primary-color);
        color: var(--primary-color);
      }
      .tab:hover:not(.active) {
        background-color: var(--bg-color);
        color: var(--text-primary);
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="navbar-container">
        <div class="logo">
          <i class="fas fa-chart-line"></i>
          数智化运营平台
        </div>
        <div style="display: flex; align-items: center">
          <div class="dropdown" style="margin-right: 16px">
            <button class="dropdown-toggle">
              <i class="fas fa-bell"></i>
              <span class="badge">3</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <div style="font-weight: 500">新任务通知</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">您有3个新任务需要处理</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">数据采集完成</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">昨日数据采集已完成</div>
              </div>
              <div class="dropdown-item">
                <div style="font-weight: 500">系统更新</div>
                <div style="font-size: 12px; color: var(--text-tertiary)">平台将于今晚23:00进行维护</div>
              </div>
            </div>
          </div>
          <div class="dropdown">
            <button class="dropdown-toggle">
              <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px" />
              <span>管理员</span>
            </button>
            <div class="dropdown-menu">
              <div class="dropdown-item">
                <i class="fas fa-user"></i>
                个人中心
              </div>
              <div class="dropdown-item">
                <i class="fas fa-cog"></i>
                系统设置
              </div>
              <div class="dropdown-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child active" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-bullhorn page-title-icon"></i>
        运营通报管理
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit">智能洞察分析</a></div>
        <div class="breadcrumb-item active">运营通报管理</div>
      </div>

      <!-- 标签页 -->
      <div class="tabs">
        <div class="tab active" data-tab-target="bulletinTasks">运营通报基础信息</div>
        <div class="tab" data-tab-target="bulletinTemplates">通报模板管理</div>
        <div class="tab" data-tab-target="bulletinManagement">通报任务管理</div>
      </div>

      <!-- 运营通报基础信息 -->
      <div class="tab-content active" id="bulletinTasks">
        <!-- 搜索和操作栏 -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 12px">
          <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap">
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报编码" />
            </div>
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报名称" />
            </div>
            <div style="margin-bottom: 0">
              <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                <option value="all">全部通报类型</option>
                <option value="business">商客业务发展通报</option>
                <option value="support">商客支撑效能通报</option>
                <option value="tool">商客工具使用通报</option>
              </select>
            </div>
            <button class="btn btn-primary" style="border: 1px solid var(--border-color)">
              <i class="fas fa-search"></i>
              查询
            </button>
            <button class="btn btn-primary" style="border: 1px solid var(--border-color)">
              <i class="fas fa-sync-alt"></i>
              重置
            </button>
          </div>
          <div style="display: flex">
            <button class="btn btn-primary" data-modal-target="addBulletinTaskModal">
              <i class="fas fa-plus"></i>
              新增基础信息
            </button>
          </div>
        </div>

        <!-- 通报基础信息列表 -->
        <div class="card">
          <div class="table-container" style="overflow-x: auto">
            <table class="table">
              <thead>
                <tr>
                  <th>运营通报编码</th>
                  <th>运营通报名称</th>
                  <th>用途描述</th>
                  <th>通报类型</th>
                  <th>创建人</th>
                  <th>创建时间</th>
                  <th>最近修改时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>BUL-20230801-001</td>
                  <td>7月商客业务数据通报</td>
                  <td>月度商客业务发展数据汇总与分析</td>
                  <td>商客业务发展通报</td>
                  <td>张三</td>
                  <td>2023-07-01 09:30</td>
                  <td>2023-07-15 14:20</td>
                  <td>
                    <!-- 查看按钮添加查看详情方法 -->
                    <button class="btn" style="color: var(--primary-color)" onclick="viewDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTaskModal"><i class="fas fa-edit"></i></button>
                    <!-- 删除按钮添加删除确认方法 -->
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-20230801-002</td>
                  <td>支撑响应时效通报</td>
                  <td>商客支撑团队响应时效与问题解决率统计</td>
                  <td>商客支撑效能通报</td>
                  <td>李四</td>
                  <td>2023-07-05 10:15</td>
                  <td>2023-07-05 10:15</td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTaskModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-20230801-003</td>
                  <td>智能工具使用率通报</td>
                  <td>商客团队智能工具活跃使用情况分析</td>
                  <td>商客工具使用通报</td>
                  <td>王五</td>
                  <td>2023-07-10 11:45</td>
                  <td>2023-07-12 09:30</td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTaskModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="pagination">
            <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item">2</div>
            <div class="pagination-item">3</div>
            <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
          </div>
        </div>
      </div>

      <!-- 通报模板管理 -->
      <div class="tab-content" id="bulletinTemplates">
        <!-- 搜索和操作栏 -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 12px">
          <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap">
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报内容模板编码" />
            </div>
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报模板名称" />
            </div>
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报名称" />
            </div>
            <button class="btn btn-primary" style="border: 1px solid var(--border-color)">
              <i class="fas fa-search"></i>
              查询
            </button>
            <button class="btn btn-primary" style="border: 1px solid var(--border-color)">
              <i class="fas fa-sync-alt"></i>
              重置
            </button>
          </div>
          <div style="display: flex">
            <button class="btn btn-primary" data-modal-target="addBulletinTemplateModal">
              <i class="fas fa-plus"></i>
              新增通报模板
            </button>
          </div>
        </div>

        <!-- 通报模板列表 -->
        <div class="card">
          <div class="table-container table-container-space">
            <style>
              .table-container-space th {
                min-width: 120px;
              }
            </style>
            <table class="table">
              <thead>
                <tr>
                  <th>运营通报编码</th>
                  <th>运营通报名称</th>
                  <th>运营通报内容模板编码</th>
                  <th>运营通报模板名称</th>
                  <th>运营通报模板内容文字</th>
                  <th>运营通报模板内容参数</th>
                  <th>创建人</th>
                  <th>创建时间</th>
                  <th>最近修改时间</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>BUL-20230801-001</td>
                  <td>7月商客业务数据通报</td>
                  <td>TPL-BIZ-001</td>
                  <td>月度业务通报模板</td>
                  <td>本月商客业务总体完成情况良好，达成率{rate}%，较上月增长{growth}%...</td>
                  <td>{rate}：业务达成率, {growth}：环比增长率, {target}：月度目标值</td>
                  <td>张三</td>
                  <td>2023-06-01 14:30</td>
                  <td>2023-07-01 10:15</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewTemplateDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmTemplateDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-20230801-002</td>
                  <td>支撑响应时效通报</td>
                  <td>TPL-SUP-001</td>
                  <td>支撑效能通报模板</td>
                  <td>本月支撑响应平均时长{avgTime}分钟，问题解决率{solveRate}%，客户满意度{satRate}分...</td>
                  <td>{avgTime}：平均响应时长, {solveRate}：问题解决率, {satRate}：客户满意度</td>
                  <td>李四</td>
                  <td>2023-07-05 09:15</td>
                  <td>2023-07-10 15:45</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewTemplateDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmTemplateDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-20230801-003</td>
                  <td>智能工具使用率通报</td>
                  <td>TPL-TOOL-001</td>
                  <td>工具使用通报模板</td>
                  <td>本月智能工具活跃使用率{activeRate}%，平均每日使用时长{useTime}分钟，使用效率提升{effiUp}%...</td>
                  <td>{activeRate}：活跃使用率, {useTime}：平均使用时长, {effiUp}：效率提升率</td>
                  <td>王五</td>
                  <td>2023-07-10 11:20</td>
                  <td>2023-07-10 11:20</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewTemplateDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmTemplateDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="pagination">
            <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item">2</div>
            <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
          </div>
        </div>
      </div>
      <!-- 通报任务管理 -->
      <div class="tab-content" id="bulletinManagement">
        <!-- 搜索和操作栏 -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 12px">
          <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap">
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报名称" />
            </div>
            <div class="search-box" style="width: 200px; margin-bottom: 0">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="通报任务名称" />
            </div>
            <div style="margin-bottom: 0">
              <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color)">
                <option value="all">全部任务状态</option>
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
                <option value="completed">已完成</option>
              </select>
            </div>
            <button class="btn btn-primary" style="border: 1px solid var(--border-color)">
              <i class="fas fa-search"></i>
              查询
            </button>
            <button class="btn btn-primary" style="border: 1px solid var(--border-color)">
              <i class="fas fa-sync-alt"></i>
              重置
            </button>
          </div>
          <div style="display: flex">
            <button class="btn btn-primary" data-modal-target="addBulletinTaskConfigModal">
              <i class="fas fa-plus"></i>
              新增通报任务配置
            </button>
          </div>
        </div>

        <!-- 通报任务配置列表 -->
        <div class="card">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>通报任务名称</th>
                  <th>通报任务编码</th>
                  <th>通报生成任务执行时间</th>
                  <th>运营通报数据范围</th>
                  <th>通报生成提醒方式</th>
                  <th>通报生成提醒人</th>
                  <th>创建人</th>
                  <th>创建时间</th>
                  <th>任务状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>7月商客业务周通报</td>
                  <td>TASK-BUL-001</td>
                  <td>每周一 09:00</td>
                  <td>前一周数据（周一至周日）</td>
                  <td>系统消息、邮件</td>
                  <td>张三、李四</td>
                  <td>管理员</td>
                  <td>2023-06-20 15:30</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewTaskDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTaskConfigModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmTaskDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>支撑响应时效月度通报</td>
                  <td>TASK-BUL-002</td>
                  <td>每月1日 10:00</td>
                  <td>上月全月数据</td>
                  <td>系统消息、短信</td>
                  <td>王五</td>
                  <td>管理员</td>
                  <td>2023-07-01 09:15</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewTaskDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTaskConfigModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmTaskDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>智能工具使用季度通报</td>
                  <td>TASK-BUL-003</td>
                  <td>每季度第一个月5日 14:00</td>
                  <td>上一季度全季数据</td>
                  <td>邮件</td>
                  <td>赵六</td>
                  <td>管理员</td>
                  <td>2023-07-10 11:20</td>
                  <td><span class="tag tag-warning">禁用</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color)" onclick="viewTaskDetails(this)"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color)" data-modal-target="editBulletinTaskConfigModal"><i class="fas fa-edit"></i></button>
                    <button class="btn" style="color: var(--danger-color)" onclick="confirmTaskDelete(this)"><i class="fas fa-trash"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="pagination">
            <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item">2</div>
            <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
          </div>
        </div>
      </div>

      <!-- 新增通报任务配置模态框 -->
      <div class="modal" id="addBulletinTaskConfigModal">
        <div class="modal-content">
          <div class="modal-header">
            <div class="modal-title">
              <i class="fas fa-plus"></i>
              新增通报任务配置
            </div>
            <button class="modal-close">&times;</button>
          </div>
          <div class="modal-body">
            <form id="addBulletinTaskConfigForm">
              <div class="form-group">
                <label for="taskBulletinSelect">
                  运营通报名称
                  <span style="color: red">*</span>
                </label>
                <select id="taskBulletinSelect" name="taskBulletinSelect" required>
                  <option value="">请选择关联的运营通报</option>
                  <option value="BUL-20230801-001">7月商客业务数据通报</option>
                  <option value="BUL-20230801-002">支撑响应时效通报</option>
                  <option value="BUL-20230801-003">智能工具使用率通报</option>
                </select>
              </div>
              <div class="form-group">
                <label for="taskName">
                  通报任务名称
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="taskName" name="taskName" required placeholder="请输入通报任务名称" />
              </div>
              <div class="form-group">
                <label for="taskExecuteTime">
                  通报生成任务执行时间
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="taskExecuteTime" name="taskExecuteTime" required placeholder="例如：每周一 09:00 或 每月1日 10:00" />
              </div>
              <div class="form-group">
                <label for="taskDataRange">
                  运营通报数据范围
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="taskDataRange" name="taskDataRange" required placeholder="例如：前一周数据（周一至周日）" />
              </div>
              <div class="form-group">
                <label for="taskRemindMethod">
                  通报生成提醒方式
                  <span style="color: red">*</span>
                </label>
                <div style="display: flex; gap: 15px; margin-top: 8px">
                  <label style="display: flex; align-items: center; font-weight: normal">
                    <input type="checkbox" name="remindMethod" value="system" checked />
                    系统消息
                  </label>
                  <label style="display: flex; align-items: center; font-weight: normal">
                    <input type="checkbox" name="remindMethod" value="email" />
                    邮件
                  </label>
                  <label style="display: flex; align-items: center; font-weight: normal">
                    <input type="checkbox" name="remindMethod" value="sms" />
                    短信
                  </label>
                </div>
              </div>
              <div class="form-group">
                <label for="taskRemindPeople">
                  通报生成提醒人
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="taskRemindPeople" name="taskRemindPeople" required placeholder="请输入提醒人，多个用逗号分隔" />
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addBulletinTaskConfigModal').classList.remove('show')">取消</button>
            <button class="btn btn-primary" onclick="document.getElementById('addBulletinTaskConfigForm').submit()">保存</button>
          </div>
        </div>
      </div>

      <!-- 编辑通报任务配置模态框 -->
      <div class="modal" id="editBulletinTaskConfigModal">
        <div class="modal-content">
          <div class="modal-header">
            <div class="modal-title">
              <i class="fas fa-edit"></i>
              修改通报任务配置
            </div>
            <button class="modal-close">&times;</button>
          </div>
          <div class="modal-body">
            <form id="editBulletinTaskConfigForm">
              <div class="form-group">
                <label for="editTaskCode">通报任务编码</label>
                <input type="text" id="editTaskCode" name="editTaskCode" readonly style="background-color: #f5f5f5" />
              </div>
              <div class="form-group">
                <label for="editTaskBulletinSelect">
                  运营通报名称
                  <span style="color: red">*</span>
                </label>
                <select id="editTaskBulletinSelect" name="editTaskBulletinSelect" required>
                  <option value="BUL-20230801-001">7月商客业务数据通报</option>
                  <option value="BUL-20230801-002">支撑响应时效通报</option>
                  <option value="BUL-20230801-003">智能工具使用率通报</option>
                </select>
              </div>
              <div class="form-group">
                <label for="editTaskName">
                  通报任务名称
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="editTaskName" name="editTaskName" required placeholder="请输入通报任务名称" />
              </div>
              <div class="form-group">
                <label for="editTaskExecuteTime">
                  通报生成任务执行时间
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="editTaskExecuteTime" name="editTaskExecuteTime" required placeholder="例如：每周一 09:00 或 每月1日 10:00" />
              </div>
              <div class="form-group">
                <label for="editTaskDataRange">
                  运营通报数据范围
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="editTaskDataRange" name="editTaskDataRange" required placeholder="例如：前一周数据（周一至周日）" />
              </div>
              <div class="form-group">
                <label for="editTaskRemindMethod">
                  通报生成提醒方式
                  <span style="color: red">*</span>
                </label>
                <div style="display: flex; gap: 15px; margin-top: 8px">
                  <label style="display: flex; align-items: center; font-weight: normal">
                    <input type="checkbox" name="editRemindMethod" value="system" />
                    系统消息
                  </label>
                  <label style="display: flex; align-items: center; font-weight: normal">
                    <input type="checkbox" name="editRemindMethod" value="email" />
                    邮件
                  </label>
                  <label style="display: flex; align-items: center; font-weight: normal">
                    <input type="checkbox" name="editRemindMethod" value="sms" />
                    短信
                  </label>
                </div>
              </div>
              <div class="form-group">
                <label for="editTaskRemindPeople">
                  通报生成提醒人
                  <span style="color: red">*</span>
                </label>
                <input type="text" id="editTaskRemindPeople" name="editTaskRemindPeople" required placeholder="请输入提醒人，多个用逗号分隔" />
              </div>
              <div class="form-group">
                <label for="editTaskStatus">
                  任务状态
                  <span style="color: red">*</span>
                </label>
                <select id="editTaskStatus" name="editTaskStatus" required>
                  <option value="active">启用</option>
                  <option value="inactive">禁用</option>
                </select>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('editBulletinTaskConfigModal').classList.remove('show')">取消</button>
            <button class="btn btn-primary" onclick="document.getElementById('editBulletinTaskConfigForm').submit()">保存修改</button>
          </div>
        </div>
      </div>

      <!-- 查看通报任务详情模态框 -->
      <div class="modal detail-modal" id="viewTaskDetailModal">
        <div class="modal-content">
          <div class="modal-header">
            <div class="modal-title">
              <i class="fas fa-info-circle"></i>
              通报任务配置详情
            </div>
            <button class="modal-close">&times;</button>
          </div>
          <div class="modal-body">
            <div class="detail-item">
              <span class="detail-label">通报任务名称：</span>
              <span class="detail-value" id="taskDetailName"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">通报任务编码：</span>
              <span class="detail-value" id="taskDetailCode"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">运营通报名称：</span>
              <span class="detail-value" id="taskDetailBulletinName"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">通报生成任务执行时间：</span>
              <span class="detail-value" id="taskDetailExecuteTime"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">运营通报数据范围：</span>
              <span class="detail-value" id="taskDetailDataRange"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">通报生成提醒方式：</span>
              <span class="detail-value" id="taskDetailRemindMethod"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">通报生成提醒人：</span>
              <span class="detail-value" id="taskDetailRemindPeople"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建人：</span>
              <span class="detail-value" id="taskDetailCreator"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value" id="taskDetailCreateTime"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">任务状态：</span>
              <span class="detail-value" id="taskDetailStatus"></span>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" onclick="document.getElementById('viewTaskDetailModal').classList.remove('show')">关闭</button>
          </div>
        </div>
      </div>
      <!-- 查看模板详情模态框 -->
      <div class="modal detail-modal" id="viewTemplateDetailModal">
        <div class="modal-content">
          <div class="modal-header">
            <div class="modal-title">
              <i class="fas fa-info-circle"></i>
              运营通报模板详情
            </div>
            <button class="modal-close">&times;</button>
          </div>
          <div class="modal-body">
            <div class="detail-item">
              <span class="detail-label">运营通报名称：</span>
              <span class="detail-value" id="templateDetailBulletinName"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">运营通报模板名称：</span>
              <span class="detail-value" id="templateDetailName"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">运营通报编码：</span>
              <span class="detail-value" id="templateDetailBulletinCode"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">模板编码：</span>
              <span class="detail-value" id="templateDetailCode"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">模板内容文字：</span>
              <span class="detail-value" id="templateDetailContent"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">模板内容参数：</span>
              <span class="detail-value" id="templateDetailParams"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建人：</span>
              <span class="detail-value" id="templateDetailCreator"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value" id="templateDetailCreateTime"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">最近修改时间：</span>
              <span class="detail-value" id="templateDetailUpdateTime"></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态：</span>
              <span class="detail-value" id="templateDetailStatus"></span>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" onclick="document.getElementById('viewTemplateDetailModal').classList.remove('show')">关闭</button>
          </div>
        </div>
      </div>
    </div>
    <!-- 新增通报模板模态框 -->
    <div class="modal" id="addBulletinTemplateModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus"></i>
            新增运营通报模板
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addBulletinTemplateForm">
            <div class="form-group">
              <label for="templateCode">
                运营通报模板编码
                <span style="color: red">*</span>
              </label>
              <input type="text" id="templateCode" name="templateCode" required placeholder="请输入模板编码（如TPL-XXX-001）" />
            </div>
            <div class="form-group">
              <label for="templateName">
                运营通报模板名称
                <span style="color: red">*</span>
              </label>
              <input type="text" id="templateName" name="templateName" required placeholder="请输入模板名称" />
            </div>
            <div class="form-group">
              <label for="bulletinSelect">
                运营通报名称
                <span style="color: red">*</span>
              </label>
              <select id="bulletinSelect" name="bulletinSelect" required>
                <option value="">请选择关联的运营通报</option>
                <option value="BUL-20230801-001">7月商客业务数据通报</option>
                <option value="BUL-20230801-002">支撑响应时效通报</option>
                <option value="BUL-20230801-003">智能工具使用率通报</option>
              </select>
            </div>
            <div class="form-group">
              <label for="templateContent">
                运营通报模板内容文字
                <span style="color: red">*</span>
              </label>
              <textarea id="templateContent" name="templateContent" rows="6" required placeholder="请输入模板内容，可包含参数占位符如{param}"></textarea>
            </div>
            <div class="form-group">
              <label for="templateParams">
                运营通报模板内容参数
                <span style="color: red">*</span>
              </label>
              <textarea id="templateParams" name="templateParams" rows="3" required placeholder="请说明模板中的参数，格式：{param1}：参数1说明, {param2}：参数2说明"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addBulletinTemplateModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('addBulletinTemplateForm').submit()">保存</button>
        </div>
      </div>
    </div>

    <!-- 编辑通报模板模态框 -->
    <div class="modal" id="editBulletinTemplateModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-edit"></i>
            修改运营通报模板
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="editBulletinTemplateForm">
            <div class="form-group">
              <label for="editTemplateCode">运营通报内容模板编码</label>
              <input type="text" id="editTemplateCode" name="editTemplateCode" readonly style="background-color: #f5f5f5" />
            </div>
            <div class="form-group">
              <label for="editTemplateName">
                运营通报模板名称
                <span style="color: red">*</span>
              </label>
              <input type="text" id="editTemplateName" name="editTemplateName" required placeholder="请输入模板名称" />
            </div>
            <div class="form-group">
              <label for="editBulletinSelect">
                运营通报名称
                <span style="color: red">*</span>
              </label>
              <select id="editBulletinSelect" name="editBulletinSelect" required>
                <option value="BUL-20230801-001">7月商客业务数据通报</option>
                <option value="BUL-20230801-002">支撑响应时效通报</option>
                <option value="BUL-20230801-003">智能工具使用率通报</option>
              </select>
            </div>
            <div class="form-group">
              <label for="editTemplateContent">
                运营通报模板内容文字
                <span style="color: red">*</span>
              </label>
              <textarea id="editTemplateContent" name="editTemplateContent" rows="6" required placeholder="请输入模板内容，可包含参数占位符如{param}"></textarea>
            </div>
            <div class="form-group">
              <label for="editTemplateParams">
                运营通报模板内容参数
                <span style="color: red">*</span>
              </label>
              <textarea id="editTemplateParams" name="editTemplateParams" rows="3" required placeholder="请说明模板中的参数，格式：{param1}：参数1说明, {param2}：参数2说明"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('editBulletinTemplateModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('editBulletinTemplateForm').submit()">保存修改</button>
        </div>
      </div>
    </div>
    <!-- 新增运营通报基础信息模态框 -->
    <div class="modal" id="addBulletinTaskModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-plus"></i>
            新增运营通报基础信息
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="addBulletinTaskForm">
            <div class="form-group">
              <label for="bulletinCode">
                运营通报编码
                <span style="color: red">*</span>
              </label>
              <input type="text" id="bulletinCode" name="bulletinCode" required placeholder="请输入唯一编码（如BUL-YYYYMMDD-XXX）" />
            </div>
            <div class="form-group">
              <label for="bulletinName">
                运营通报名称
                <span style="color: red">*</span>
              </label>
              <input type="text" id="bulletinName" name="bulletinName" required placeholder="请输入通报名称" />
            </div>
            <div class="form-group">
              <label for="bulletinType">
                通报类型
                <span style="color: red">*</span>
              </label>
              <select id="bulletinType" name="bulletinType" required>
                <option value="">请选择通报类型</option>
                <option value="business">商客业务发展通报</option>
                <option value="support">商客支撑效能通报</option>
                <option value="tool">商客工具使用通报</option>
              </select>
            </div>
            <div class="form-group">
              <label for="bulletinPurpose">
                用途描述
                <span style="color: red">*</span>
              </label>
              <textarea id="bulletinPurpose" name="bulletinPurpose" rows="4" required placeholder="请描述该通报的用途和适用场景"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('addBulletinTaskModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('addBulletinTaskForm').submit()">保存</button>
        </div>
      </div>
    </div>

    <!-- 编辑运营通报基础信息模态框 -->
    <div class="modal" id="editBulletinTaskModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-edit"></i>
            修改运营通报基础信息
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <form id="editBulletinTaskForm">
            <div class="form-group">
              <label for="editBulletinCode">运营通报编码</label>
              <input type="text" id="editBulletinCode" name="editBulletinCode" readonly style="background-color: #f5f5f5" />
            </div>
            <div class="form-group">
              <label for="editBulletinName">
                运营通报名称
                <span style="color: red">*</span>
              </label>
              <input type="text" id="editBulletinName" name="editBulletinName" required placeholder="请输入通报名称" />
            </div>
            <div class="form-group">
              <label for="editBulletinType">
                通报类型
                <span style="color: red">*</span>
              </label>
              <select id="editBulletinType" name="editBulletinType" required>
                <option value="business">商客业务发展通报</option>
                <option value="support">商客支撑效能通报</option>
                <option value="tool">商客工具使用通报</option>
              </select>
            </div>
            <div class="form-group">
              <label for="editBulletinPurpose">
                用途描述
                <span style="color: red">*</span>
              </label>
              <textarea id="editBulletinPurpose" name="editBulletinPurpose" rows="4" required placeholder="请描述该通报的用途和适用场景"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn" style="border: 1px solid var(--border-color)" onclick="document.getElementById('editBulletinTaskModal').classList.remove('show')">取消</button>
          <button class="btn btn-primary" onclick="document.getElementById('editBulletinTaskForm').submit()">保存修改</button>
        </div>
      </div>
    </div>

    <!-- 查看运营通报基础信息详情模态框 -->
    <div class="modal detail-modal" id="viewBulletinDetailModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">
            <i class="fas fa-info-circle"></i>
            运营通报基础信息详情
          </div>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="detail-item">
            <span class="detail-label">运营通报名称：</span>
            <span class="detail-value" id="detailName"></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">用途描述：</span>
            <span class="detail-value" id="detailPurpose"></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">通报类型：</span>
            <span class="detail-value" id="detailType"></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">创建人：</span>
            <span class="detail-value" id="detailCreator"></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">创建时间：</span>
            <span class="detail-value" id="detailCreateTime"></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">最近修改时间：</span>
            <span class="detail-value" id="detailUpdateTime"></span>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" onclick="document.getElementById('viewBulletinDetailModal').classList.remove('show')">关闭</button>
        </div>
      </div>
    </div>

    <script src="js/common.js"></script>
    <script>
      // 标签页切换
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', () => {
          document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
          document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
          tab.classList.add('active');
          const target = tab.getAttribute('data-tab-target');
          document.getElementById(target).classList.add('active');
        });
      });

      // 新增表单提交
      document.getElementById('addBulletinTaskForm').addEventListener('submit', function (e) {
        e.preventDefault();
        const code = document.getElementById('bulletinCode').value;
        const name = document.getElementById('bulletinName').value;
        if (!code || !name) {
          alert('请填写必填字段');
          return;
        }
        alert('运营通报基础信息新增成功！');
        document.getElementById('addBulletinTaskModal').classList.remove('show');
        this.reset();
      });

      // 编辑模态框触发
      document.querySelectorAll('[data-modal-target="editBulletinTaskModal"]').forEach(btn => {
        btn.addEventListener('click', () => {
          const row = btn.closest('tr');
          document.getElementById('editBulletinCode').value = row.cells[0].textContent;
          document.getElementById('editBulletinName').value = row.cells[1].textContent;
          document.getElementById('editBulletinType').value = row.cells[3].textContent === '商客业务发展通报' ? 'business' : row.cells[3].textContent === '商客支撑效能通报' ? 'support' : 'tool';
          document.getElementById('editBulletinPurpose').value = row.cells[2].textContent;
          document.getElementById('editBulletinTaskModal').classList.add('show');
        });
      });

      // 查看详情方法
      function viewDetails(button) {
        // 获取当前点击按钮所在的表格行
        const row = button.closest('tr');

        // 提取行数据并填充详情模态框
        document.getElementById('detailName').textContent = row.cells[1].textContent;
        document.getElementById('detailPurpose').textContent = row.cells[2].textContent;
        document.getElementById('detailType').textContent = row.cells[3].textContent;
        document.getElementById('detailCreator').textContent = row.cells[4].textContent;
        document.getElementById('detailCreateTime').textContent = row.cells[5].textContent;
        document.getElementById('detailUpdateTime').textContent = row.cells[6].textContent;

        // 显示模态框
        document.getElementById('viewBulletinDetailModal').classList.add('show');
      }

      // 删除确认方法
      function confirmDelete(button) {
        // 获取当前行的通报名称，用于确认信息
        const row = button.closest('tr');
        const bulletinName = row.cells[1].textContent;

        // 显示二次确认对话框
        if (confirm(`确定要删除"${bulletinName}"吗？此操作不可撤销。`)) {
          // 如果用户确认，执行删除操作（这里只是模拟删除）
          // 实际应用中应该发送请求到服务器删除数据
          row.remove();
          alert('删除成功！');
        }
        // 如果用户取消，则不执行任何操作
      }

      // 关闭模态框（点击关闭按钮或外部区域）
      document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', () => {
          btn.closest('.modal').classList.remove('show');
        });
      });

      window.addEventListener('click', e => {
        if (e.target.classList.contains('modal')) {
          e.target.classList.remove('show');
        }
      });
      // 查看模板详情方法
      function viewTemplateDetails(button) {
        // 获取当前点击按钮所在的表格行
        const row = button.closest('tr');

        // 提取行数据并填充详情模态框
        document.getElementById('templateDetailBulletinCode').textContent = row.cells[0].textContent;
        document.getElementById('templateDetailBulletinName').textContent = row.cells[1].textContent;
        document.getElementById('templateDetailCode').textContent = row.cells[2].textContent;
        document.getElementById('templateDetailName').textContent = row.cells[3].textContent;
        document.getElementById('templateDetailContent').textContent = row.cells[4].textContent;
        document.getElementById('templateDetailParams').textContent = row.cells[5].textContent;
        document.getElementById('templateDetailCreator').textContent = row.cells[6].textContent;
        document.getElementById('templateDetailCreateTime').textContent = row.cells[7].textContent;
        document.getElementById('templateDetailUpdateTime').textContent = row.cells[8].textContent;
        document.getElementById('templateDetailStatus').textContent = row.cells[9].textContent;

        // 显示模态框
        document.getElementById('viewTemplateDetailModal').classList.add('show');
      }

      // 模板删除确认方法
      function confirmTemplateDelete(button) {
        // 获取当前行的模板名称，用于确认信息
        const row = button.closest('tr');
        const templateName = row.cells[3].textContent;

        // 显示二次确认对话框
        if (confirm(`确定要删除模板"${templateName}"吗？此操作不可撤销。`)) {
          // 如果用户确认，执行删除操作
          row.remove();
          alert('模板删除成功！');
        }
      }

      // 关闭模板详情模态框
      document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', () => {
          if (btn.closest('#viewTemplateDetailModal')) {
            document.getElementById('viewTemplateDetailModal').classList.remove('show');
          }
        });
      });
      // 新增模板表单提交处理
      document.getElementById('addBulletinTemplateForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 获取表单数据
        const templateCode = document.getElementById('templateCode').value;
        const templateName = document.getElementById('templateName').value;
        const bulletinId = document.getElementById('bulletinSelect').value;
        const bulletinName = document.getElementById('bulletinSelect').options[document.getElementById('bulletinSelect').selectedIndex].text;
        const templateContent = document.getElementById('templateContent').value;
        const templateParams = document.getElementById('templateParams').value;

        // 简单验证
        if (!templateCode || !templateName || !bulletinId || !templateContent || !templateParams) {
          alert('请填写所有必填字段');
          return;
        }

        // 模拟创建时间和创建人（实际应用中这些信息应该由后端提供）
        const createTime = new Date().toLocaleString();
        const creator = '管理员'; // 实际应用中应该是当前登录用户

        // 模拟添加到表格
        const tableBody = document.querySelector('#bulletinTemplates table tbody');
        const newRow = document.createElement('tr');

        newRow.innerHTML = `
    <td>${bulletinId}</td>
    <td>${bulletinName}</td>
    <td>${templateCode}</td>
    <td>${templateName}</td>
    <td>${templateContent}</td>
    <td>${templateParams}</td>
    <td>${creator}</td>
    <td>${createTime}</td>
    <td>${createTime}</td>
    <td><span class="tag tag-success">启用</span></td>
    <td>
      <button class="btn" style="color: var(--primary-color);" onclick="viewTemplateDetails(this)"><i class="fas fa-eye"></i></button>
      <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
      <button class="btn" style="color: var(--danger-color);" onclick="confirmTemplateDelete(this)"><i class="fas fa-trash"></i></button>
    </td>
  `;

        tableBody.appendChild(newRow);

        // 关闭模态框并重置表单
        alert('运营通报模板新增成功！');
        document.getElementById('addBulletinTemplateModal').classList.remove('show');
        this.reset();
      });

      // 编辑模板模态框触发
      document.querySelectorAll('[data-modal-target="editBulletinTemplateModal"]').forEach(btn => {
        btn.addEventListener('click', () => {
          const row = btn.closest('tr');

          // 填充表单数据
          document.getElementById('editTemplateCode').value = row.cells[2].textContent;
          document.getElementById('editTemplateName').value = row.cells[3].textContent;
          document.getElementById('editBulletinSelect').value = row.cells[0].textContent;
          document.getElementById('editTemplateContent').value = row.cells[4].textContent;
          document.getElementById('editTemplateParams').value = row.cells[5].textContent;

          // 显示模态框
          document.getElementById('editBulletinTemplateModal').classList.add('show');
        });
      });

      // 编辑模板表单提交处理
      document.getElementById('editBulletinTemplateForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 获取表单数据
        const templateCode = document.getElementById('editTemplateCode').value;
        const templateName = document.getElementById('editTemplateName').value;
        const bulletinId = document.getElementById('editBulletinSelect').value;
        const bulletinName = document.getElementById('editBulletinSelect').options[document.getElementById('editBulletinSelect').selectedIndex].text;
        const templateContent = document.getElementById('editTemplateContent').value;
        const templateParams = document.getElementById('editTemplateParams').value;

        // 简单验证
        if (!templateName || !bulletinId || !templateContent || !templateParams) {
          alert('请填写所有必填字段');
          return;
        }

        // 模拟修改时间和修改人（实际应用中这些信息应该由后端提供）
        const updateTime = new Date().toLocaleString();
        const modifier = '管理员'; // 实际应用中应该是当前登录用户

        // 找到对应的行并更新数据
        const rows = document.querySelectorAll('#bulletinTemplates table tbody tr');
        for (let row of rows) {
          if (row.cells[2].textContent === templateCode) {
            row.cells[0].textContent = bulletinId;
            row.cells[1].textContent = bulletinName;
            row.cells[3].textContent = templateName;
            row.cells[4].textContent = templateContent;
            row.cells[5].textContent = templateParams;
            row.cells[8].textContent = updateTime;
            break;
          }
        }

        // 关闭模态框
        alert('运营通报模板修改成功！');
        document.getElementById('editBulletinTemplateModal').classList.remove('show');
      });
      // 查看任务详情方法
      function viewTaskDetails(button) {
        // 获取当前点击按钮所在的表格行
        const row = button.closest('tr');

        // 提取行数据并填充详情模态框
        document.getElementById('taskDetailName').textContent = row.cells[0].textContent;
        document.getElementById('taskDetailCode').textContent = row.cells[1].textContent;
        document.getElementById('taskDetailExecuteTime').textContent = row.cells[2].textContent;
        document.getElementById('taskDetailDataRange').textContent = row.cells[3].textContent;
        document.getElementById('taskDetailRemindMethod').textContent = row.cells[4].textContent;
        document.getElementById('taskDetailRemindPeople').textContent = row.cells[5].textContent;
        document.getElementById('taskDetailCreator').textContent = row.cells[6].textContent;
        document.getElementById('taskDetailCreateTime').textContent = row.cells[7].textContent;
        document.getElementById('taskDetailStatus').textContent = row.cells[8].textContent;

        // 获取运营通报名称（这里需要根据实际数据关联，这里简化处理）
        const taskCode = row.cells[1].textContent;
        let bulletinName = '未知';
        if (taskCode === 'TASK-BUL-001') bulletinName = '7月商客业务数据通报';
        if (taskCode === 'TASK-BUL-002') bulletinName = '支撑响应时效通报';
        if (taskCode === 'TASK-BUL-003') bulletinName = '智能工具使用率通报';
        document.getElementById('taskDetailBulletinName').textContent = bulletinName;

        // 显示模态框
        document.getElementById('viewTaskDetailModal').classList.add('show');
      }

      // 任务删除确认方法
      function confirmTaskDelete(button) {
        // 获取当前行的任务名称，用于确认信息
        const row = button.closest('tr');
        const taskName = row.cells[0].textContent;

        // 显示二次确认对话框
        if (confirm(`确定要删除任务"${taskName}"吗？此操作不可撤销。`)) {
          // 如果用户确认，执行删除操作
          row.remove();
          alert('任务删除成功！');
        }
      }

      // 新增任务配置表单提交处理
      document.getElementById('addBulletinTaskConfigForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 获取表单数据
        const bulletinId = document.getElementById('taskBulletinSelect').value;
        const bulletinName = document.getElementById('taskBulletinSelect').options[document.getElementById('taskBulletinSelect').selectedIndex].text;
        const taskName = document.getElementById('taskName').value;
        const executeTime = document.getElementById('taskExecuteTime').value;
        const dataRange = document.getElementById('taskDataRange').value;
        const remindPeople = document.getElementById('taskRemindPeople').value;

        // 获取选中的提醒方式
        const remindMethods = [];
        document.querySelectorAll('input[name="remindMethod"]:checked').forEach(checkbox => {
          if (checkbox.value === 'system') remindMethods.push('系统消息');
          if (checkbox.value === 'email') remindMethods.push('邮件');
          if (checkbox.value === 'sms') remindMethods.push('短信');
        });

        // 简单验证
        if (!bulletinId || !taskName || !executeTime || !dataRange || remindMethods.length === 0 || !remindPeople) {
          alert('请填写所有必填字段');
          return;
        }

        // 生成任务编码（实际应用中应该由后端生成）
        const taskCode = 'TASK-BUL-' + Math.floor(100 + Math.random() * 900);

        // 模拟创建时间和创建人
        const createTime = new Date().toLocaleString();
        const creator = '管理员';

        // 模拟添加到表格
        const tableBody = document.querySelector('#bulletinManagement table tbody');
        const newRow = document.createElement('tr');

        newRow.innerHTML = `
    <td>${taskName}</td>
    <td>${taskCode}</td>
    <td>${executeTime}</td>
    <td>${dataRange}</td>
    <td>${remindMethods.join('、')}</td>
    <td>${remindPeople}</td>
    <td>${creator}</td>
    <td>${createTime}</td>
    <td><span class="tag tag-success">启用</span></td>
    <td>
      <button class="btn" style="color: var(--primary-color);" onclick="viewTaskDetails(this)"><i class="fas fa-eye"></i></button>
      <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTaskConfigModal"><i class="fas fa-edit"></i></button>
      <button class="btn" style="color: var(--danger-color);" onclick="confirmTaskDelete(this)"><i class="fas fa-trash"></i></button>
    </td>
  `;

        tableBody.appendChild(newRow);

        // 关闭模态框并重置表单
        alert('通报任务配置新增成功！');
        document.getElementById('addBulletinTaskConfigModal').classList.remove('show');
        this.reset();
      });

      // 编辑任务配置模态框触发
      document.querySelectorAll('[data-modal-target="editBulletinTaskConfigModal"]').forEach(btn => {
        btn.addEventListener('click', () => {
          const row = btn.closest('tr');

          // 填充表单数据
          document.getElementById('editTaskCode').value = row.cells[1].textContent;
          document.getElementById('editTaskName').value = row.cells[0].textContent;
          document.getElementById('editTaskExecuteTime').value = row.cells[2].textContent;
          document.getElementById('editTaskDataRange').value = row.cells[3].textContent;
          document.getElementById('editTaskRemindPeople').value = row.cells[5].textContent;

          // 设置任务状态
          const statusText = row.cells[8].textContent.trim();
          document.getElementById('editTaskStatus').value = statusText === '启用' ? 'active' : 'inactive';

          // 设置运营通报选择（简化处理）
          const taskCode = row.cells[1].textContent;
          if (taskCode === 'TASK-BUL-001') document.getElementById('editTaskBulletinSelect').value = 'BUL-20230801-001';
          if (taskCode === 'TASK-BUL-002') document.getElementById('editTaskBulletinSelect').value = 'BUL-20230801-002';
          if (taskCode === 'TASK-BUL-003') document.getElementById('editTaskBulletinSelect').value = 'BUL-20230801-003';

          // 设置提醒方式
          const remindMethods = row.cells[4].textContent.split('、');
          document.querySelectorAll('input[name="editRemindMethod"]').forEach(checkbox => {
            checkbox.checked = false;
            if (remindMethods.includes('系统消息') && checkbox.value === 'system') checkbox.checked = true;
            if (remindMethods.includes('邮件') && checkbox.value === 'email') checkbox.checked = true;
            if (remindMethods.includes('短信') && checkbox.value === 'sms') checkbox.checked = true;
          });

          // 显示模态框
          document.getElementById('editBulletinTaskConfigModal').classList.add('show');
        });
      });

      // 编辑任务配置表单提交处理
      document.getElementById('editBulletinTaskConfigForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 获取表单数据
        const taskCode = document.getElementById('editTaskCode').value;
        const bulletinId = document.getElementById('editTaskBulletinSelect').value;
        const taskName = document.getElementById('editTaskName').value;
        const executeTime = document.getElementById('editTaskExecuteTime').value;
        const dataRange = document.getElementById('editTaskDataRange').value;
        const remindPeople = document.getElementById('editTaskRemindPeople').value;
        const taskStatus = document.getElementById('editTaskStatus').value;

        // 获取选中的提醒方式
        const remindMethods = [];
        document.querySelectorAll('input[name="editRemindMethod"]:checked').forEach(checkbox => {
          if (checkbox.value === 'system') remindMethods.push('系统消息');
          if (checkbox.value === 'email') remindMethods.push('邮件');
          if (checkbox.value === 'sms') remindMethods.push('短信');
        });

        // 简单验证
        if (!taskName || !executeTime || !dataRange || remindMethods.length === 0 || !remindPeople) {
          alert('请填写所有必填字段');
          return;
        }

        // 找到对应的行并更新数据
        const rows = document.querySelectorAll('#bulletinManagement table tbody tr');
        for (let row of rows) {
          if (row.cells[1].textContent === taskCode) {
            row.cells[0].textContent = taskName;
            row.cells[2].textContent = executeTime;
            row.cells[3].textContent = dataRange;
            row.cells[4].textContent = remindMethods.join('、');
            row.cells[5].textContent = remindPeople;
            row.cells[8].innerHTML = taskStatus === 'active' ? '<span class="tag tag-success">启用</span>' : '<span class="tag tag-warning">禁用</span>';
            break;
          }
        }

        // 关闭模态框
        alert('通报任务配置修改成功！');
        document.getElementById('editBulletinTaskConfigModal').classList.remove('show');
      });

      // 关闭任务详情模态框
      document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', () => {
          if (btn.closest('#viewTaskDetailModal')) {
            document.getElementById('viewTaskDetailModal').classList.remove('show');
          }
        });
      });
    </script>
  </body>
</html>
