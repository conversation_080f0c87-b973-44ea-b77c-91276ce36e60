<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 穿透权限管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .tab-content {
      display: none;
      padding: 20px 0;
    }
    .tab-content.active {
      display: block;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
    }
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
    }
    .tab.active {
      border-bottom-color: var(--primary-color);
      color: var(--primary-color);
      font-weight: 500;
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务完成通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10086已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">任务告警</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#10087执行失败</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
          <div class="menu-item child" data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration" data-href="task_scheduling_diaodu.html">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group">
          <div class="menu-item child" data-href="task_scheduling_diaodu.html">任务调度看板</div>
          <div class="menu-item child" data-href="task_scheduling_export.html">统计分析与报表管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="data_masking_process.html">
            <span>任务调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group"  style="display: none">
            <div class="menu-item child" data-href="data_masking_process.html">数据脱密处理</div>
            <div class="menu-item child" data-href="111111.html">数据可逆模糊化处理</div>
            <div class="menu-item child " data-href="alarm_notification.html">告警通知</div>
            <div class="menu-item child " data-href="alarm_gener.html">告警生成</div>
            <div class="menu-item child " data-href="456.html">告警处理</div>
          </div>
          <div class="menu-item child parent" data-group="self_management" data-href="2222.html">
            <span>五级调度</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child  active" data-href="2222.html">管理⻚⾯穿透权限</div>
            <div class="menu-item child" data-href="penetration_page.html">维护穿透⻚⾯</div>
          </div>
        </div>
      </div>
      <div class="menu-group">
        <div class="menu-item parent" data-group="data_Devops" data-href="devops_dashboard.html">
          <i class="fas fa-chart-line menu-icon"></i>
          <span class="menu-text">DevOps 平台</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="devops_dashboard.html">DevOps 总览</div>
          <div class="menu-item child" data-href="pipeline_management.html">CI/CD 流水线</div>
          <div class="menu-item child" data-href="deployment_management.html">容器部署</div>
          <div class="menu-item child" data-href="monitoring_center.html">监控中心</div>
          <div class="menu-item child" data-href="service_topology.html">服务拓扑</div>
        </div>
      </div>
      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-shield-alt page-title-icon"></i>
      穿透权限管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透</a></div>
      <div class="breadcrumb-item active">穿透权限管理</div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
      <div class="tab active" data-tab="permission-management">权限管理</div>
      <div class="tab" data-tab="permission-audit">穿透权限审核</div>
      <div class="tab" data-tab="permission-statistics">穿透权限统计</div>
      <div class="tab" data-tab="permission-monitoring">穿透权限监控</div>
    </div>

    <!-- 权限管理内容区 -->
    <div class="tab-content active" id="permission-management">
      <div class="card" style="margin-bottom: 20px;">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">权限查询与管理</div>
            <div>
              <button class="btn btn-primary" style="margin-right: 8px;" onclick="openAddPermissionModal()"><i class="fas fa-plus"></i> 新增权限</button>
              <button class="btn btn-secondary" style="margin-right: 8px;"><i class="fas fa-download"></i> 导出</button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 权限查询条件 -->
          <form id="queryPermissionForm" style="margin-bottom: 20px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
              <div>
                <label for="queryPermissionCode">权限编码</label>
                <input type="text" id="queryPermissionCode" placeholder="请输入权限编码">
              </div>
              <div>
                <label for="queryRole">角色</label>
                <select id="queryRole">
                  <option value="">请选择角色</option>
                  <option value="admin">管理员管理员</option>
                  <option value="operator">操作员</option>
                  <option value="auditor">审核员</option>
                  <option value="viewer">查看员</option>
                </select>
              </div>
              <div>
                <label for="queryLevel">级别</label>
                <select id="queryLevel">
                  <option value="">请选择级别</option>
                  <option value="group">集团</option>
                  <option value="province">省公司</option>
                  <option value="city">市公司</option>
                </select>
              </div>
              <div>
                <label for="queryProvince">所属省公司</label>
                <input type="text" id="queryProvince" placeholder="请输入省公司名称">
              </div>
              <div>
                <label for="queryStatus">状态</label>
                <select id="queryStatus">
                  <option value="">请选择状态</option>
                  <option value="active">启用</option>
                  <option value="inactive">禁用</option>
                  <option value="expired">过期</option>
                </select>
              </div>
              <div>
                <label for="queryValidityPeriod">有效期</label>
                <input type="date" id="queryValidityPeriod">
              </div>
            </div>
            <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
              <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('queryPermissionForm')">重置</button>
              <button type="submit" class="btn btn-primary">查询</button>
            </div>
          </form>

          <!-- 权限列表 -->
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>权限名称</th>
                  <th>权限编码</th>
                  <th>角色</th>
                  <th>级别</th>
                  <th>所属省公司</th>
                  <th>有效期</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>集团管理员权限</td>
                  <td>PM001</td>
                  <td>管理员</td>
                  <td>集团</td>
                  <td>集团</td>
                  <td>2023-12-31</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary" onclick="openPermissionSettingsModal('PM001', '集团管理员权限', 'admin', 'group', '集团', '2023-12-31', 'active')"><i class="fas fa-cog"></i> 设置</button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> 删除</button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i> 查看</button>
                  </td>
                </tr>
                <tr>
                  <td>江苏操作员权限</td>
                  <td>PM002</td>
                  <td>操作员</td>
                  <td>省公司</td>
                  <td>江苏</td>
                  <td>2023-12-31</td>
                  <td><span class="tag tag-success">启用</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary" onclick="openPermissionSettingsModal('PM002', '江苏操作员权限', 'operator', 'province', '江苏', '2023-12-31', 'active')"><i class="fas fa-cog"></i> 设置</button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> 删除</button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i> 查看</button>
                  </td>
                </tr>
                <tr>
                  <td>上海查看员权限</td>
                  <td>PM003</td>
                  <td>查看员</td>
                  <td>市公司</td>
                  <td>上海</td>
                  <td>2023-06-30</td>
                  <td><span class="tag tag-warning">过期</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary" onclick="openPermissionSettingsModal('PM003', '上海查看员权限', 'viewer', 'city', '上海', '2023-06-30', 'expired')"><i class="fas fa-cog"></i> 设置</button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> 删除</button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i> 查看</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限设置弹窗（已修改为居中显示） -->
    <div id="permissionSettingsModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
      <div class="modal-content" style="width: 800px; margin: 0 auto; background: white; border-radius: 6px; box-shadow: 0 3px 12px rgba(0,0,0,0.15);">
        <div class="modal-header">
          <span class="close" onclick="closePermissionSettingsModal()">&times;</span>
          <h2 id="permissionModalTitle">穿透权限设置</h2>
        </div>
        <div class="modal-body">
          <form id="permissionSettingsForm">
            <input type="hidden" id="permissionId">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
              <div>
                <label for="permissionName">权限名称 <span style="color: red;">*</span></label>
                <input type="text" id="permissionName" placeholder="请输入权限名称" required>
              </div>
              <div>
                <label for="permissionCode">权限编码 <span style="color: red;">*</span></label>
                <input type="text" id="permissionCode" placeholder="请输入权限编码" required>
              </div>
              <div>
                <label for="role">角色 <span style="color: red;">*</span></label>
                <select id="role" required>
                  <option value="">请选择角色</option>
                  <option value="admin">管理员</option>
                  <option value="operator">操作员</option>
                  <option value="auditor">审核员</option>
                  <option value="viewer">查看员</option>
                </select>
              </div>
              <div>
                <label for="level">级别 <span style="color: red;">*</span></label>
                <select id="level" required>
                  <option value="">请选择级别</option>
                  <option value="group">集团</option>
                  <option value="province">省公司</option>
                  <option value="city">市公司</option>
                </select>
              </div>
              <div>
                <label for="province">所属省公司 <span style="color: red;">*</span></label>
                <input type="text" id="province" placeholder="请输入省公司名称" required>
              </div>
              <div>
                <label for="validityPeriod">有效期 <span style="color: red;">*</span></label>
                <input type="date" id="validityPeriod" required>
              </div>
              <div>
                <label for="status">状态 <span style="color: red;">*</span></label>
                <select id="status" required>
                  <option value="">请选择状态</option>
                  <option value="active">启用</option>
                  <option value="inactive">禁用</option>
                  <option value="expired">过期</option>
                </select>
              </div>
              <div style="grid-column: span 2;">
                <label for="description">权限描述</label>
                <textarea id="description" rows="4" placeholder="请输入权限描述信息"></textarea>
              </div>
            </div>
            <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
              <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('permissionSettingsForm')">重置</button>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 穿透权限审核内容区 -->
    <div class="tab-content" id="permission-audit">
      <div class="card" style="margin-bottom: 20px;">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">穿透权限审核列表</div>
            <div class="search-box" style="width: 250px;">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="搜索审核...">
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>审核编号</th>
                  <th>权限编码</th>
                  <th>申请人</th>
                  <th>审核人</th>
                  <th>审核时间</th>
                  <th>审核结果</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>AD001</td>
                  <td>PM001</td>
                  <td>张三</td>
                  <td>李四</td>
                  <td>2023-05-20 10:00:00</td>
                  <td><span class="tag tag-success">通过</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary" onclick="openAuditModal('AD001', 'PM001', '张三', '李四', '2023-05-20 10:00:00', 'approve')"><i class="fas fa-check"></i> 审核</button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-times"></i> 拒绝</button>
                  </td>
                </tr>
                <tr>
                  <td>AD002</td>
                  <td>PM002</td>
                  <td>王五</td>
                  <td>赵六</td>
                  <td>2023-05-21 11:30:00</td>
                  <td><span class="tag tag-success">通过</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary" onclick="openAuditModal('AD002', 'PM002', '王五', '赵六', '2023-05-21 11:30:00', 'approve')"><i class="fas fa-check"></i> 审核</button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-times"></i> 拒绝</button>
                  </td>
                </tr>
                <tr>
                  <td>AD003</td>
                  <td>PM003</td>
                  <td>钱七</td>
                  <td>孙八</td>
                  <td>2023-05-22 14:15:00</td>
                  <td><span class="tag tag-danger">拒绝</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary" onclick="openAuditModal('AD003', 'PM003', '钱七', '孙八', '2023-05-22 14:15:00', 'reject')"><i class="fas fa-check"></i> 审核</button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-times"></i> 拒绝</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>

      <!-- 穿透权限审核弹窗 -->
      <div id="auditModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
        <div class="modal-content" style="width: 800px; margin: 0 auto; background: white; border-radius: 6px; box-shadow: 0 3px 12px rgba(0,0,0,0.15);">
          <div class="modal-header">
            <span class="close" onclick="document.getElementById('auditModal').style.display = 'none'">&times;</span>
            <h2>穿透权限审核</h2>
          </div>
          <div class="modal-body">
            <form id="editAuditForm">
              <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div>
                  <label for="editAuditCode">审核编号 <span style="color: red;">*</span></label>
                  <input type="text" id="editAuditCode" placeholder="请输入审核编号" required readonly>
                </div>
                <div>
                  <label for="editPermissionCode">权限编码 <span style="color: red;">*</span></label>
                  <input type="text" id="editPermissionCode" placeholder="请输入权限编码" required readonly>
                </div>
                <div>
                  <label for="editApplicant">申请人 <span style="color: red;">*</span></label>
                  <input type="text" id="editApplicant" placeholder="请输入申请人" required readonly>
                </div>
                <div>
                  <label for="editAuditor">审核人 <span style="color: red;">*</span></label>
                  <input type="text" id="editAuditor" placeholder="请输入审核人" required>
                </div>
                <div>
                  <label for="editAuditTime">审核时间 <span style="color: red;">*</span></label>
                  <input type="datetime-local" id="editAuditTime" required>
                </div>
                <div>
                  <label for="editAuditResult">审核结果 <span style="color: red;">*</span></label>
                  <select id="editAuditResult" required>
                    <option value="">请选择审核结果</option>
                    <option value="approve">通过</option>
                    <option value="reject">拒绝</option>
                  </select>
                </div>
                <div style="grid-column: span 2;">
                  <label for="editAuditComment">审核意见</label>
                  <textarea id="editAuditComment" rows="4" placeholder="请输入审核意见"></textarea>
                </div>
              </div>
              <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
                <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('editAuditForm')">重置</button>
                <button type="submit" class="btn btn-primary">提交审核</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 穿透权限统计内容区 -->
    <div class="tab-content" id="permission-statistics">
      <div class="card" style="margin-bottom: 20px;">
        <div class="card-header">
          <div style="font-weight: 500; font-size: 16px;">统计条件</div>
        </div>
        <div class="card-body">
          <form id="statisticsForm">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
              <div>
                <label for="statisticsType">统计类型 <span style="color: red;">*</span></label>
                <select id="statisticsType" required>
                  <option value="">请选择统计类型</option>
                  <option value="byLevel">按级别统计</option>
                  <option value="byRole">按角色统计</option>
                  <option value="byProvince">按省公司统计</option>
                  <option value="byStatus">按状态统计</option>
                </select>
              </div>
              <div>
                <label for="dateRange">日期范围 <span style="color: red;">*</span></label>
                <input type="month" id="dateRange" required>
              </div>
            </div>
            <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
              <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('statisticsForm')">重置</button>
              <button type="submit" class="btn btn-primary">生成统计</button>
            </div>
          </form>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">穿透权限统计结果
              <span id="statisticsInfo" style="margin-left: 10px; font-size: 14px; color: #666;"></span>
            </div>
            <div>
              <button class="btn btn-secondary"><i class="fas fa-download"></i> 导出</button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div style="height: 300px; background-color: #f5f7fa; margin-bottom: 20px; position: relative; overflow: hidden;">
            <canvas id="statisticsChart" style="width: 100%; height: 100%;"></canvas>
            <div id="chartPlaceholder" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
              <div style="text-align: center;">
                <i class="fas fa-chart-pie" style="font-size: 48px; color: var(--primary-color); margin-bottom: 16px;"></i>
                <div>请选择统计条件并生成统计</div>
              </div>
            </div>
          </div>
          
          <div class="table-container">
            <table class="table" id="statisticsTable">
              <thead>
                <tr>
                  <th>统计项</th>
                  <th>数量</th>
                  <th>占比</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td colspan="3" style="text-align: center;">暂无统计数据，请选择统计条件并生成</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 穿透权限监控内容区 -->
    <div class="tab-content" id="permission-monitoring">
      <div class="card" style="margin-bottom: 20px;">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-weight: 500; font-size: 16px;">穿透权限监控列表</div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <div class="search-box" style="width: 220px; height: 36px; display: flex; align-items: center;">
                <i class="fas fa-search search-box-icon" style="margin: 0 10px;"></i>
                <input type="text" placeholder="搜索监控..." style="height: 100%; width: 100%; border: none; outline: none; padding: 0 5px;">
              </div>
              <button class="btn btn-primary" id="addMonitorBtn" style="height: 36px; padding: 0 16px;margin-top: -22px;">
                <i class="fas fa-plus"></i> 新增穿透权限监控
              </button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>监控编号</th>
                  <th>权限编码</th>
                  <th>监控类型</th>
                  <th>阈值</th>
                  <th>当前值</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>MN001</td>
                  <td>PM001</td>
                  <td>访问监控</td>
                  <td>100次/小时</td>
                  <td>85次/小时</td>
                  <td><span class="tag tag-success">正常</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>MN002</td>
                  <td>PM002</td>
                  <td>异常监控</td>
                  <td>5次/小时</td>
                  <td>3次/小时</td>
                  <td><span class="tag tag-success">正常</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
                <tr>
                  <td>MN003</td>
                  <td>PM003</td>
                  <td>安全监控</td>
                  <td>0次/小时</td>
                  <td>2次/小时</td>
                  <td><span class="tag tag-danger">告警</span></td>
                  <td>
                    <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                    <button class="btn btn-sm btn-info"><i class="fas fa-eye"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
            <div class="pagination">
              <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
              <button class="pagination-btn active">1</button>
              <button class="pagination-btn">2</button>
              <button class="pagination-btn">3</button>
              <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增穿透权限监控弹窗 -->
      <div id="addMonitorModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
        <div class="card" style="width: 800px; margin: 20px auto;">
          <div class="card-header">
            <div style="font-weight: 500; font-size: 16px; display: flex; justify-content: space-between; align-items: center;">
              新增穿透权限监控
              <button class="btn btn-sm btn-light" id="closeModal">&times;</button>
            </div>
          </div>
          <div class="card-body">
            <form id="addMonitoringForm">
              <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div>
                  <label for="monitoringCode">监控编号 <span style="color: red;">*</span></label>
                  <input type="text" id="monitoringCode" placeholder="请输入监控编号" required>
                </div>
                <div>
                  <label for="permissionCode">权限编码 <span style="color: red;">*</span></label>
                  <input type="text" id="permissionCode" placeholder="请输入权限编码" required>
                </div>
                <div>
                  <label for="monitoringType">监控类型 <span style="color: red;">*</span></label>
                  <select id="monitoringType" required>
                    <option value="">请选择监控类型</option>
                    <option value="access">访问监控</option>
                    <option value="abnormal">异常监控</option>
                    <option value="security">安全监控</option>
                  </select>
                </div>
                <div>
                  <label for="threshold">阈值 <span style="color: red;">*</span></label>
                  <input type="number" id="threshold" placeholder="请输入阈值" required>
                </div>
                <div>
                  <label for="monitoringStatus">监控状态 <span style="color: red;">*</span></label>
                  <select id="monitoringStatus" required>
                    <option value="">请选择监控状态</option>
                    <option value="active">启用</option>
                    <option value="inactive">禁用</option>
                  </select>
                </div>
                <div>
                  <label for="alarmMethod">告警方式 <span style="color: red;">*</span></label>
                  <select id="alarmMethod" required>
                    <option value="">请选择告警方式</option>
                    <option value="email">邮件</option>
                    <option value="sms">短信</option>
                    <option value="system">系统消息</option>
                  </select>
                </div>
                <div style="grid-column: span 2;">
                  <label for="monitoringDescription">监控描述</label>
                  <textarea id="monitoringDescription" rows="4" placeholder="请输入监控描述信息"></textarea>
                </div>
              </div>
              <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
                <button type="button" class="btn" style="margin-right: 12px;" onclick="resetForm('addMonitoringForm')">重置</button>
                <button type="submit" class="btn btn-primary">保存监控</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 重置表单函数
    function resetForm(formId) {
      document.getElementById(formId).reset();
    }
    
    // 标签页切换功能
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', function() {
        const target = this.getAttribute('data-tab');
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.toggle('active', content.id === target);
        });
        document.querySelectorAll('.tab').forEach(t => {
          t.classList.toggle('active', t === this);
        });
      });
    });

    // 权限设置弹窗相关函数
    window.openPermissionSettingsModal = function(code, name, role, level, province, validity, status) {
      const modal = document.getElementById('permissionSettingsModal');
      document.getElementById('permissionModalTitle').textContent = '编辑穿透权限';
      
      document.getElementById('permissionCode').value = code;
      document.getElementById('permissionName').value = name;
      document.getElementById('role').value = role;
      document.getElementById('level').value = level;
      document.getElementById('province').value = province;
      document.getElementById('validityPeriod').value = validity;
      document.getElementById('status').value = status;
      
      modal.style.display = 'flex';
      
      window.onclick = function(event) {
        if (event.target == modal) {
          modal.style.display = 'none';
        }
      }
    }

    window.openAddPermissionModal = function() {
      const modal = document.getElementById('permissionSettingsModal');
      document.getElementById('permissionModalTitle').textContent = '新增穿透权限';
      resetForm('permissionSettingsForm');
      modal.style.display = 'flex';
      
      window.onclick = function(event) {
        if (event.target == modal) {
          modal.style.display = 'none';
        }
      }
    }

    window.closePermissionSettingsModal = function() {
      document.getElementById('permissionSettingsModal').style.display = 'none';
    }

    // 审核弹窗相关函数
    window.openAuditModal = function(auditCode, permissionCode, applicant, auditor, auditTime, auditResult) {
      const modal = document.getElementById('auditModal');
      
      document.getElementById('editAuditCode').value = auditCode;
      document.getElementById('editPermissionCode').value = permissionCode;
      document.getElementById('editApplicant').value = applicant;
      document.getElementById('editAuditor').value = auditor;
      document.getElementById('editAuditTime').value = auditTime.replace(' ', 'T');
      document.getElementById('editAuditResult').value = auditResult;
      
      modal.style.display = 'flex';
    }

    // 监控弹窗相关函数
    document.getElementById('addMonitorBtn').addEventListener('click', function() {
      document.getElementById('addMonitorModal').style.display = 'flex';
    });

    document.getElementById('closeModal').addEventListener('click', function() {
      document.getElementById('addMonitorModal').style.display = 'none';
    });

    // 点击弹窗外部关闭
    window.onclick = function(event) {
      const modals = [
        document.getElementById('permissionSettingsModal'),
        document.getElementById('auditModal'),
        document.getElementById('addMonitorModal')
      ];
      
      modals.forEach(modal => {
        if (modal && event.target == modal) {
          modal.style.display = 'none';
        }
      });
    }

    // 表单提交处理
    document.getElementById('permissionSettingsForm').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('穿透权限设置已保存!');
      closePermissionSettingsModal();
    });

    document.getElementById('editAuditForm').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('穿透权限审核已更新!');
      document.getElementById('auditModal').style.display = 'none';
    });

    document.getElementById('queryPermissionForm').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('查询完成!');
    });

    document.getElementById('statisticsForm').addEventListener('submit', function(e) {
      e.preventDefault();
      generateStatistics();
    });

    document.getElementById('addMonitoringForm').addEventListener('submit', function(e) {
      e.preventDefault();
      alert('穿透权限监控已保存!');
      document.getElementById('addMonitorModal').style.display = 'none';
    });

    // 统计图表相关功能
    let statisticsChart = null;

    function initStatisticsChart() {
      const ctx = document.getElementById('statisticsChart').getContext('2d');
      statisticsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: [],
          datasets: [{
            data: [],
            backgroundColor: [],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
            }
          }
        }
      });
    }

    function generateStatisticsData(statisticsType, dateRange) {
      const baseData = {
        byLevel: {
          labels: ['集团', '省公司', '市公司'],
          datasets: [5, 20, 25],
          total: 50
        },
        byRole: {
          labels: ['管理员', '操作员', '审核员', '查看员'],
          datasets: [8, 15, 7, 20],
          total: 50
        },
        byProvince: {
          labels: ['江苏', '上海', '北京', '广东', '浙江'],
          datasets: [12, 8, 6, 15, 9],
          total: 50
        },
        byStatus: {
          labels: ['启用', '禁用', '过期'],
          datasets: [30, 10, 10],
          total: 50
        }
      };
      
      const year = dateRange.split('-')[0];
      const month = parseInt(dateRange.split('-')[1]);
      const multiplier = 1 + (month % 4) * 0.2;
      
      const adjustedData = JSON.parse(JSON.stringify(baseData[statisticsType]));
      if (adjustedData) {
        adjustedData.datasets = adjustedData.datasets.map(value => Math.round(value * multiplier));
        adjustedData.total = adjustedData.datasets.reduce((a, b) => a + b, 0);
      }
      
      return adjustedData || null;
    }

    function updateStatisticsTable(labels, data, total) {
      const tableBody = document.querySelector('#statisticsTable tbody');
      tableBody.innerHTML = '';
      
      labels.forEach((label, index) => {
        const value = data[index];
        const percentage = Math.round((value / total) * 100);
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${label}</td>
          <td>${value}</td>
          <td>${percentage}%</td>
        `;
        tableBody.appendChild(row);
      });
      
      const totalRow = document.createElement('tr');
      totalRow.innerHTML = `
        <td><strong>总计</strong></td>
        <td><strong>${total}</strong></td>
        <td><strong>100%</strong></td>
      `;
      tableBody.appendChild(totalRow);
    }

    function updateStatisticsInfo(statisticsType, dateRange) {
      const typeMap = {
        'byLevel': '按级别',
        'byRole': '按角色',
        'byProvince': '按省公司',
        'byStatus': '按状态'
      };
      
      const typeText = typeMap[statisticsType] || '自定义';
      const dateText = dateRange ? dateRange.replace('-', '年') + '月' : '全部时间';
      
      document.getElementById('statisticsInfo').textContent = `(${typeText} · ${dateText})`;
    }

    function generateStatistics() {
      const statisticsType = document.getElementById('statisticsType').value;
      const dateRange = document.getElementById('dateRange').value;
      
      if (!statisticsType || !dateRange) {
        alert('请选择统计类型和日期范围');
        return;
      }
      
      const statsData = generateStatisticsData(statisticsType, dateRange);
      
      if (statsData) {
        document.getElementById('chartPlaceholder').style.display = 'none';
        
        updateStatisticsInfo(statisticsType, dateRange);
        
        const colors = [
          'rgba(54, 162, 235, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 159, 64, 0.7)',
          'rgba(255, 205, 86, 0.7)'
        ];
        
        statisticsChart.data.labels = statsData.labels;
        statisticsChart.data.datasets[0].data = statsData.datasets;
        statisticsChart.data.datasets[0].backgroundColor = colors.slice(0, statsData.labels.length);
        statisticsChart.data.datasets[0].borderColor = colors.slice(0, statsData.labels.length).map(color => color.replace('0.7', '1'));
        statisticsChart.update();
        
        updateStatisticsTable(statsData.labels, statsData.datasets, statsData.total);
      }
    }

    // 页面加载完成后初始化图表
    window.onload = function() {
      initStatisticsChart();
    };
  </script>
   <script src="js/common.js"></script>
</body>
</html>
    