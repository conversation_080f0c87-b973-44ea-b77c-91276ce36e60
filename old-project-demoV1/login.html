<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=0.8">
  <title>数智化运营平台 - 登录</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <form id="loginForm" class="login-form">
        <div class="form-group">
          <label for="username"><i class="fas fa-user"></i> 用户名</label>
          <input type="text" id="username" name="username" required placeholder="请输入用户名">
        </div>
        <div class="form-group">
          <label for="password"><i class="fas fa-lock"></i> 密码</label>
          <input type="password" id="password" name="password" required placeholder="请输入密码">
        </div>
        <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
          <label style="display: flex; align-items: center;">
            <input type="checkbox" id="remember" name="remember">
            <span style="margin-left: 8px;">记住密码</span>
          </label>
          <a href="#" style="color: var(--primary-color); text-decoration: none;">忘记密码?</a>
        </div>
        <button type="submit" class="btn btn-primary login-btn"><i class="fas fa-sign-in-alt"></i> 登录</button>
      </form>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();
      // 模拟登录成功，实际应用中应该发送请求到后端
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      if (username && password) {
        // 显示加载动画
        const loginBtn = document.querySelector('.login-btn');
        const originalText = loginBtn.innerHTML;
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<span class="loading"></span> 登录中...';

        // 模拟网络延迟
        setTimeout(function() {
          // 登录成功，跳转到首页
          window.location.href = 'index.html';
        }, 1500);
      }
    });
  </script>
</body>
</html>