<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
 <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 运营报告内容格式管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
   <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child active" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-file-alt page-title-icon"></i>
      运营报告内容格式管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营报告内容格式管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex; width: 50%;">
        <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" id="searchInput" placeholder="搜索模板...">
        </div>
        <div style="display: flex; align-items: center;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部类型</option>
            <option value="text">文本</option>
            <option value="chart">图表</option>
            <option value="table">表格</option>
          </select>
<button id="searchBtn" class="btn" style="margin-left: 15px; padding: 6px 12px; height: 50px;  width: 100px;"><i class=""></i> 查询</button>        </div>
      </div>
      <div style="display: flex;">
          <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-filter"></i> 高级筛选</button>
          <button class="btn btn-primary" data-modal-target="addTemplateModal"><i class="fas fa-plus"></i> 新增模板</button>
        </div>
    </div>

    <!-- 模板列表表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table" id="templateTable">
          <thead>
            <tr>
              <th>模板编码</th>
              <th>模板名称</th>
              <th>模板描述</th>
              <th>模板类型</th>
              <th>修改人</th>
              <th>修改时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="templateTableBody">
            <tr>
              <td>REPORT_TEMP_001</td>
              <td>用户增长分析模板</td>
              <td>用于分析用户增长趋势的模板</td>
              <td><span class="tag tag-success">图表</span></td>
              <td>管理员</td>
              <td>2023-07-14 16:30</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" onclick="viewTemplate('REPORT_TEMP_001')"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" onclick="editTemplate('REPORT_TEMP_001')"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);" onclick="deleteTemplate('REPORT_TEMP_001')"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>REPORT_TEMP_002</td>
              <td>销售数据表格模板</td>
              <td>用于展示销售数据的表格模板</td>
              <td><span class="tag tag-info">表格</span></td>
              <td>管理员</td>
              <td>2023-07-16 09:15</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" onclick="viewTemplate('REPORT_TEMP_002')"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" onclick="editTemplate('REPORT_TEMP_002')"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);" onclick="deleteTemplate('REPORT_TEMP_002')"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>REPORT_TEMP_003</td>
              <td>月度总结文本模板</td>
              <td>用于月度总结的文本模板</td>
              <td><span class="tag tag-warning">文本</span></td>
              <td>管理员</td>
              <td>2023-07-01 14:45</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" onclick="viewTemplate('REPORT_TEMP_003')"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" onclick="editTemplate('REPORT_TEMP_003')"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--danger-color);" onclick="deleteTemplate('REPORT_TEMP_003')"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增模板模态框 -->
  <div class="modal" id="addTemplateModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增运营报告模板</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addTemplateForm">
          <div class="form-group">
            <label for="templateCode">模板编码</label>
            <input type="text" id="templateCode" name="templateCode" required placeholder="请输入模板编码">
          </div>
          <div class="form-group">
            <label for="templateName">模板名称</label>
            <input type="text" id="templateName" name="templateName" required placeholder="请输入模板名称">
          </div>
          <div class="form-group">
            <label for="templateDescription">模板描述</label>
            <textarea id="templateDescription" name="templateDescription" rows="3" required placeholder="请输入模板描述"></textarea>
          </div>
          <div class="form-group">
            <label for="templateType">模板类型</label>
            <select id="templateType" name="templateType" required>
              <option value="">请选择模板类型</option>
              <option value="text">文本</option>
              <option value="chart">图表</option>
              <option value="table">表格</option>
            </select>
          </div>
          <div class="form-group" id="templateContentGroup">
            <label for="templateContent">模板内容</label>
            <textarea id="templateContent" name="templateContent" rows="5" placeholder="请输入模板内容"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addTemplateModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="submitAddTemplate()">保存</button>
      </div>
    </div>
  </div>

  <!-- 编辑模板模态框 -->
  <div class="modal" id="editTemplateModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑运营报告模板</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="editTemplateForm">
          <div class="form-group">
            <label for="editTemplateCode">模板编码</label>
            <input type="text" id="editTemplateCode" name="editTemplateCode" required disabled placeholder="模板编码">
          </div>
          <div class="form-group">
            <label for="editTemplateName">模板名称</label>
            <input type="text" id="editTemplateName" name="editTemplateName" required placeholder="请输入模板名称">
          </div>
          <div class="form-group">
            <label for="editTemplateDescription">模板描述</label>
            <textarea id="editTemplateDescription" name="editTemplateDescription" rows="3" required placeholder="请输入模板描述"></textarea>
          </div>
          <div class="form-group">
            <label for="editTemplateType">模板类型</label>
            <select id="editTemplateType" name="editTemplateType" required>
              <option value="">请选择模板类型</option>
              <option value="text">文本</option>
              <option value="chart">图表</option>
              <option value="table">表格</option>
            </select>
          </div>
          <div class="form-group" id="editTemplateContentGroup">
            <label for="editTemplateContent">模板内容</label>
            <textarea id="editTemplateContent" name="editTemplateContent" rows="5" placeholder="请输入模板内容"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editTemplateModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="submitEditTemplate()">保存</button>
      </div>
    </div>
  </div>

  <!-- 查看模板模态框 -->
  <div class="modal" id="viewTemplateModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-eye"></i> 查看运营报告模板</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>模板编码</label>
          <div id="viewTemplateCode" style="padding: 6px 0;"></div>
        </div>
        <div class="form-group">
          <label>模板名称</label>
          <div id="viewTemplateName" style="padding: 6px 0;"></div>
        </div>
        <div class="form-group">
          <label>模板描述</label>
          <div id="viewTemplateDescription" style="padding: 6px 0;"></div>
        </div>
        <div class="form-group">
          <label>模板类型</label>
          <div id="viewTemplateType" style="padding: 6px 0;"></div>
        </div>
        <div class="form-group">
          <label>修改人</label>
          <div id="viewModifiedBy" style="padding: 6px 0;"></div>
        </div>
        <div class="form-group">
          <label>修改时间</label>
          <div id="viewModifiedTime" style="padding: 6px 0;"></div>
        </div>
        <div class="form-group">
          <label>模板内容</label>
          <div id="viewTemplateContent" style="padding: 6px; border: 1px solid var(--border-color); border-radius: 4px; min-height: 100px;"></div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="document.getElementById('viewTemplateModal').classList.remove('show')">关闭</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 模拟模板数据
    const templatesData = [
      {
        code: 'REPORT_TEMP_001',
        name: '用户增长分析模板',
        description: '用于分析用户增长趋势的模板',
        type: 'chart',
        typeText: '图表',
        modifiedBy: '管理员',
        modifiedTime: '2023-07-14 16:30',
        content: '这是一个图表模板，用于展示用户增长趋势。\n支持折线图、柱状图等多种图表类型。\n可配置时间范围、数据维度等参数。'
      },
      {
        code: 'REPORT_TEMP_002',
        name: '销售数据表格模板',
        description: '用于展示销售数据的表格模板',
        type: 'table',
        typeText: '表格',
        modifiedBy: '管理员',
        modifiedTime: '2023-07-16 09:15',
        content: '这是一个表格模板，用于展示销售数据。\n包含产品名称、销售数量、销售金额等字段。\n支持排序、筛选等功能。'
      },
      {
        code: 'REPORT_TEMP_003',
        name: '月度总结文本模板',
        description: '用于月度总结的文本模板',
        type: 'text',
        typeText: '文本',
        modifiedBy: '管理员',
        modifiedTime: '2023-07-01 14:45',
        content: '## 月度总结\n\n### 总体情况\n本月运营情况良好，各项指标均达到预期。\n\n### 重点工作\n1. 完成了新产品上线\n2. 开展了营销活动\n3. 优化了用户体验\n\n### 下月计划\n1. 持续优化产品功能\n2. 拓展新的用户渠道\n3. 提升客户满意度'
      }
    ];

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 搜索功能
      const searchBtn = document.getElementById('searchBtn');
      const searchInput = document.getElementById('searchInput');

      searchBtn.addEventListener('click', performSearch);
      searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          performSearch();
        }
      });

      // 表单提交事件
      document.getElementById('addTemplateForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitAddTemplate();
      });

      document.getElementById('editTemplateForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitEditTemplate();
      });
    });

    // 执行搜索
    function performSearch() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const typeFilter = document.querySelector('select').value;

      const filteredTemplates = templatesData.filter(template => {
        const matchesSearch = template.code.toLowerCase().includes(searchTerm) || 
                             template.name.toLowerCase().includes(searchTerm) || 
                             template.description.toLowerCase().includes(searchTerm);
        const matchesType = typeFilter === 'all' || template.type === typeFilter;

        return matchesSearch && matchesType;
      });

      renderTemplateTable(filteredTemplates);
    }

    // 渲染模板表格
    function renderTemplateTable(templates) {
      const tableBody = document.getElementById('templateTableBody');
      tableBody.innerHTML = '';

      templates.forEach(template => {
        let typeClass = '';
        switch(template.type) {
          case 'text':
            typeClass = 'tag-warning';
            break;
          case 'chart':
            typeClass = 'tag-success';
            break;
          case 'table':
            typeClass = 'tag-info';
            break;
        }

        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${template.code}</td>
          <td>${template.name}</td>
          <td>${template.description}</td>
          <td><span class="tag ${typeClass}">${template.typeText}</span></td>
          <td>${template.modifiedBy}</td>
          <td>${template.modifiedTime}</td>
          <td>
            <button class="btn" style="color: var(--primary-color);" onclick="viewTemplate('${template.code}')"><i class="fas fa-eye"></i></button>
            <button class="btn" style="color: var(--primary-color);" onclick="editTemplate('${template.code}')"><i class="fas fa-edit"></i></button>
            <button class="btn" style="color: var(--danger-color);" onclick="deleteTemplate('${template.code}')"><i class="fas fa-trash"></i></button>
          </td>
        `;
        tableBody.appendChild(row);
      });
    }

    // 查看模板
    function viewTemplate(code) {
      const template = templatesData.find(t => t.code === code);
      if (!template) return;

      document.getElementById('viewTemplateCode').textContent = template.code;
      document.getElementById('viewTemplateName').textContent = template.name;
      document.getElementById('viewTemplateDescription').textContent = template.description;
      document.getElementById('viewTemplateType').textContent = template.typeText;
      document.getElementById('viewModifiedBy').textContent = template.modifiedBy;
      document.getElementById('viewModifiedTime').textContent = template.modifiedTime;
      document.getElementById('viewTemplateContent').textContent = template.content;

      document.getElementById('viewTemplateModal').classList.add('show');
    }

    // 编辑模板
    function editTemplate(code) {
      const template = templatesData.find(t => t.code === code);
      if (!template) return;

      document.getElementById('editTemplateCode').value = template.code;
      document.getElementById('editTemplateName').value = template.name;
      document.getElementById('editTemplateDescription').value = template.description;
      document.getElementById('editTemplateType').value = template.type;
      document.getElementById('editTemplateContent').value = template.content;

      document.getElementById('editTemplateModal').classList.add('show');
    }

    // 删除模板
    function deleteTemplate(code) {
      if (confirm('确定要删除这个模板吗？')) {
        const index = templatesData.findIndex(t => t.code === code);
        if (index !== -1) {
          templatesData.splice(index, 1);
          performSearch(); // 重新渲染表格
          alert('模板删除成功！');
        }
      }
    }

    // 提交新增模板
    function submitAddTemplate() {
      const form = document.getElementById('addTemplateForm');
      if (!validateForm('addTemplateForm')) {
        alert('请填写所有必填字段');
        return;
      }

      const newTemplate = {
        code: document.getElementById('templateCode').value,
        name: document.getElementById('templateName').value,
        description: document.getElementById('templateDescription').value,
        type: document.getElementById('templateType').value,
        typeText: getTypeText(document.getElementById('templateType').value),
        modifiedBy: '管理员',
        modifiedTime: formatDate(new Date()),
        content: document.getElementById('templateContent').value
      };

      // 检查模板编码是否已存在
      if (templatesData.some(t => t.code === newTemplate.code)) {
        alert('模板编码已存在，请更换');
        return;
      }

      templatesData.push(newTemplate);
      form.reset();
      document.getElementById('addTemplateModal').classList.remove('show');
      performSearch(); // 重新渲染表格
      alert('模板添加成功！');
    }

    // 提交编辑模板
    function submitEditTemplate() {
      const form = document.getElementById('editTemplateForm');
      if (!validateForm('editTemplateForm')) {
        alert('请填写所有必填字段');
        return;
      }

      const code = document.getElementById('editTemplateCode').value;
      const templateIndex = templatesData.findIndex(t => t.code === code);

      if (templateIndex !== -1) {
        templatesData[templateIndex] = {
          ...templatesData[templateIndex],
          name: document.getElementById('editTemplateName').value,
          description: document.getElementById('editTemplateDescription').value,
          type: document.getElementById('editTemplateType').value,
          typeText: getTypeText(document.getElementById('editTemplateType').value),
          modifiedBy: '管理员',
          modifiedTime: formatDate(new Date()),
          content: document.getElementById('editTemplateContent').value
        };

        form.reset();
        document.getElementById('editTemplateModal').classList.remove('show');
        performSearch(); // 重新渲染表格
        alert('模板更新成功！');
      }
    }

    // 获取类型文本
    function getTypeText(type) {
      switch(type) {
        case 'text': return '文本';
        case 'chart': return '图表';
        case 'table': return '表格';
        default: return '';
      }
    }

    // 格式化日期
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
  </script>
</body>
</html>