<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自定义报表展示 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: bold;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: bold;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: bold;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
      <div class="menu-item " data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="data_source.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="insight" data-href="report_management.html">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="insight-group" style="display: none">
          <div class="menu-item child" data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child" data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="operationView" data-href="intelligent_query.html">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">运营视图</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group">
          <div class="menu-item child" data-href="intelligent_query.html">智能问数</div>
          <div class="menu-item child" data-href="operation_views.html">大屏模板</div>
           <div class="menu-item child " data-href="theme_management.html">画布管理</div>
          <div class="menu-item child parent" data-group="self_management" data-href="custom_report_display.html">
            <span>自定义报表</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group">
            <div class="menu-item child active" data-href="custom_report_display.html">自定义报表展示</div>
            <div class="menu-item child" data-href="report_history.html">报表历史记录</div>
            <div class="menu-item child" data-href="data_permission_control.html">数据权限控制</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="indicator_bloodline.html">
            <span>视图交互</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="indicator_bloodline.html">指标血缘管理</div>
            <div class="menu-item child" data-href="map_view.html">地图</div>
          </div>
          <div class="menu-item child parent" data-group="deployment" data-href="template_list.html">
            <span>模板管理</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="template_list.html">模板列表</div>
            <div class="menu-item child" data-href="template_permission.html">模板控制</div>
          </div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data" data-href="permission_manage.html">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">统一运营门户</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="permission_manage.html">视图权限管理</div>
          <div class="menu-item child" data-href="permission_and_log_management.html">权限日志管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-file-alt page-title-icon"></i>
      自定义报表展示
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">自定义报表</a></div>
      <div class="breadcrumb-item active">自定义报表展示</div>
    </div>

    <!-- 筛选条件区域 -->
    <div class="card" style="margin-bottom: 20px;">
      <div class="card-header">
        <i class="fas fa-filter"></i> 报表筛选条件
      </div>
      <div class="card-body">
        <div class="row">
          <div class="form-group col-4" style="padding: 0 20px;">
            <label>时间范围:</label>
            <div style="display: flex; gap: 10px;height:36px;">
              <input type="date" class="form-control">
              <span style="align-self: center;">至</span>
              <input type="date" class="form-control">
            </div>
          </div>
          <div class="form-group col-4" style="padding: 0 20px;">
            <label>区域:</label>
            <select class="form-control" style="height:36px;">
              <option value="all">全国</option>
              <option value="provincial">分省</option>
              <option value="city">市级</option>
              <option value="district">区县级</option>
            </select>
          </div>
          <div class="form-group col-4" style="padding: 0 20px;">
            <label>行业:</label>
            <select class="form-control"  style="height:36px;">
              <option value="all">全部行业</option>
              <option value="finance">金融</option>
              <option value="retail">零售</option>
              <option value="manufacturing">制造业</option>
              <option value="service">服务业</option>
            </select>
          </div>
          <div class="form-group col-4" style="padding: 0 20px;">
            <label>指标类型:</label>
            <select class="form-control"  style="height:36px;">
              <option value="all">全部指标</option>
              <option value="sales">销售额</option>
              <option value="orders">订单量</option>
              <option value="users">用户数</option>
              <option value="performance">绩效指标</option>
            </select>
          </div>
          <div class="form-group col-4" style="padding: 0 20px;">
            <label>数值范围:</label>
            <div style="display: flex; gap: 10px;height:36px;">
              <input type="number" class="form-control" placeholder="最小值">
              <span style="align-self: center;">至</span>
              <input type="number" class="form-control" placeholder="最大值">
            </div>
          </div>
          <div class="form-group col-4" style="padding: 0 20px;">
            <label>排序方式:</label>
            <select class="form-control"  style="height:36px;">
              <option value="asc">升序</option>
              <option value="desc">降序</option>
            </select>
          </div>
        </div>
        <div class="row" style="margin: 16px;"><div class="col-12" style="display: flex; justify-content: flex-end; gap: 10px;">
          <button class="btn" style="border: 1px solid var(--border-color);"><i class="fas fa-redo-alt"></i> 重置</button>
          <button class="btn btn-primary" id="generateReportBtn"><i class="fas fa-chart-line"></i> 生成报表</button>
        </div>
      </div>
    </div>

    <!-- 报表展示区域 -->
    <div class="card">
      <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div><i class="fas fa-table"></i> 报表数据展示</div>
          <div style="display: flex; gap: 10px;">
            <button class="btn btn-sm" style="border: 1px solid var(--border-color);"><i class="fas fa-download"></i> 导出</button>
            <button class="btn btn-sm" style="border: 1px solid var(--border-color);"><i class="fas fa-print"></i> 打印</button>
            <button class="btn btn-sm" style="border: 1px solid var(--border-color);"><i class="fas fa-cog"></i> 设置</button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <!-- 图表类型切换 -->
        <div style="display: flex; gap: 10px; margin-bottom: 16px;">
          <button class="btn btn-sm btn-primary" id="addNewBtn"><i class="fas fa-plus"></i> 新增</button>
          <button class="btn btn-sm btn-primary" data-chart-type="table"><i class="fas fa-table"></i> 表格</button>
          <button class="btn btn-sm" style="border: 1px solid var(--border-color);" data-chart-type="bar"><i class="fas fa-chart-bar"></i> 柱状图</button>
          <button class="btn btn-sm" style="border: 1px solid var(--border-color);" data-chart-type="line"><i class="fas fa-chart-line"></i> 折线图</button>
          <button class="btn btn-sm" style="border: 1px solid var(--border-color);" data-chart-type="pie"><i class="fas fa-chart-pie"></i> 饼图</button>
        </div>

        <!-- 表格展示 -->
        <div id="tableView" class="chart-view active">
          <table class="table">
            <thead>
              <tr>
                <th style="width: 10%;">区域</th>
                <th style="width: 15%;">行业</th>
                <th style="width: 15%;">销售额</th>
                <th style="width: 15%;">订单量</th>
                <th style="width: 15%;">用户数</th>
                <th style="width: 15%;">同比增长</th>
                <th style="width: 15%;">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全国</td>
                <td>全部行业</td>
                <td>¥12,580,450</td>
                <td>45,892</td>
                <td>128,456</td>
                <td style="color: var(--success-color);"><i class="fas fa-arrow-up"></i> 18.5%</td>
                <td>
                  <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
                  <button class="btn btn-xs btn-warning edit-btn"><i class="fas fa-edit"></i> 修改</button>
                  <button class="btn btn-xs btn-danger delete-btn"><i class="fas fa-trash-alt"></i> 删除</button>
                </td>
              </tr>
              <tr>
                <td>华东区</td>
                <td>金融</td>
                <td>¥3,250,890</td>
                <td>12,568</td>
                <td>35,890</td>
                <td style="color: var(--success-color);"><i class="fas fa-arrow-up"></i> 22.3%</td>
                <td>
                  <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
                </td>
              </tr>
              <tr>
                <td>华北区</td>
                <td>零售</td>
                <td>¥2,890,560</td>
                <td>9,876</td>
                <td>28,450</td>
                <td style="color: var(--success-color);"><i class="fas fa-arrow-up"></i> 15.7%</td>
                <td>
                  <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
                </td>
              </tr>
              <tr>
                <td>华南区</td>
                <td>制造业</td>
                <td>¥2,560,320</td>
                <td>8,456</td>
                <td>22,560</td>
                <td style="color: var(--danger-color);"><i class="fas fa-arrow-down"></i> 3.2%</td>
                <td>
                  <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
                </td>
              </tr>
              <tr>
                <td>西北区</td>
                <td>服务业</td>
                <td>¥1,850,230</td>
                <td>6,540</td>
                <td>18,950</td>
                <td style="color: var(--success-color);"><i class="fas fa-arrow-up"></i> 10.8%</td>
                <td>
                  <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
                </td>
              </tr>
              <tr>
                <td>西南区</td>
                <td>零售</td>
                <td>¥2,028,450</td>
                <td>8,452</td>
                <td>22,606</td>
                <td style="color: var(--success-color);"><i class="fas fa-arrow-up"></i> 16.2%</td>
                <td>
                  <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 图表展示 (默认隐藏) -->
        <div id="chartView" class="chart-view" style="display: none;">
          <canvas id="reportChart" height="300"></canvas>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 图表类型切换
    document.querySelectorAll('[data-chart-type]').forEach(button => {
      button.addEventListener('click', function() {
        const chartType = this.getAttribute('data-chart-type');
        const tableView = document.getElementById('tableView');
        const chartView = document.getElementById('chartView');
        const chartButtons = document.querySelectorAll('[data-chart-type]');

        // 更新按钮状态
        chartButtons.forEach(btn => {
          btn.classList.remove('btn-primary');
          btn.style.border = '1px solid var(--border-color)';
        });
        this.classList.add('btn-primary');
        this.style.border = 'none';

        // 显示对应的视图
        if (chartType === 'table') {
          tableView.style.display = 'block';
          chartView.style.display = 'none';
        } else {
          tableView.style.display = 'none';
          chartView.style.display = 'block';
          initChart(chartType);
        }
      });
    });

    // 初始化图表
    function initChart(type) {
      const ctx = document.getElementById('reportChart').getContext('2d');
      const chartData = {
        labels: ['全国', '华东区', '华北区', '华南区', '西北区', '西南区'],
        datasets: [{
          label: '销售额 (万元)',
          data: [1258, 325, 289, 256, 185, 203],
          backgroundColor: [
            'rgba(54, 162, 235, 0.5)',
            'rgba(75, 192, 192, 0.5)',
            'rgba(153, 102, 255, 0.5)',
            'rgba(255, 159, 64, 0.5)',
            'rgba(255, 99, 132, 0.5)',
            'rgba(255, 206, 86, 0.5)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)'
          ],
          borderWidth: 1
        }]
      };

      // 销毁已存在的图表
      if (window.reportChartInstance) {
        window.reportChartInstance.destroy();
      }

      // 创建新图表
      let chartConfig;
      if (type === 'bar') {
        chartConfig = {
          type: 'bar',
          data: chartData,
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        };
      } else if (type === 'line') {
        chartConfig = {
          type: 'line',
          data: chartData,
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        };
      } else if (type === 'pie') {
        chartConfig = {
          type: 'pie',
          data: chartData,
          options: {
            responsive: true
          }
        };
      }

      window.reportChartInstance = new Chart(ctx, chartConfig);
    }

    // 生成报表按钮点击事件
    document.getElementById('generateReportBtn').addEventListener('click', function() {
      // 显示加载状态
      this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
      this.disabled = true;

      // 模拟报表生成延迟
      setTimeout(() => {
        // 恢复按钮状态
        this.innerHTML = '<i class="fas fa-chart-line"></i> 生成报表';
        this.disabled = false;

        // 显示成功提示
        alert('报表生成成功！');
      }, 1500);
    });
  </script>
<!-- 新增/修改模态框 -->
<div id="dataModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
  <div class="modal-content" style="background-color: white; padding: 20px; border-radius: 8px; width: 500px; max-width: 90%; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
    <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; border-bottom: 1px solid #eee;">
      <h3 id="modalTitle" style="margin: 0;">新增数据</h3>
      <button id="closeModal" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
    </div>
    <div class="modal-body">
      <form id="dataForm">
        <div class="form-group" style="margin-bottom: 15px;">
          <label for="region">区域:</label>
          <select id="region" class="form-control" required>
            <option value="全国">全国</option>
            <option value="华东区">华东区</option>
            <option value="华北区">华北区</option>
            <option value="华南区">华南区</option>
            <option value="西北区">西北区</option>
            <option value="西南区">西南区</option>
          </select>
        </div>
        <div class="form-group" style="margin-bottom: 15px;">
          <label for="industry">行业:</label>
          <select id="industry" class="form-control" required>
            <option value="全部行业">全部行业</option>
            <option value="金融">金融</option>
            <option value="零售">零售</option>
            <option value="制造业">制造业</option>
            <option value="服务业">服务业</option>
          </select>
        </div>
        <div class="form-group" style="margin-bottom: 15px;">
          <label for="sales">销售额:</label>
          <input type="number" id="sales" class="form-control" required placeholder="请输入销售额">
        </div>
        <div class="form-group" style="margin-bottom: 15px;">
          <label for="orders">订单量:</label>
          <input type="number" id="orders" class="form-control" required placeholder="请输入订单量">
        </div>
        <div class="form-group" style="margin-bottom: 15px;">
          <label for="users">用户数:</label>
          <input type="number" id="users" class="form-control" required placeholder="请输入用户数">
        </div>
        <div style="margin-bottom: 15px;">
          <button id="addCustomFieldBtn" class="btn" style="border: 1px solid var(--border-color); display: flex; align-items: center; gap: 5px;">
            <i class="fas fa-plus"></i> 新增自定义字段
          </button>
        </div>
        <input type="hidden" id="dataId">
      </form>
    </div>
    <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 15px; border-top: 1px solid #eee; padding-top: 15px;">
      <button id="cancelBtn" class="btn" style="border: 1px solid var(--border-color);">取消</button>
      <button id="saveBtn" class="btn btn-primary">保存</button>
    </div>
  </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
  <div class="modal-content" style="background-color: white; padding: 20px; border-radius: 8px; width: 400px; max-width: 90%; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
    <div class="modal-header" style="margin-bottom: 15px; border-bottom: 1px solid #eee;">
      <h3 style="margin: 0;">确认删除</h3>
    </div>
    <div class="modal-body">
      <p>确定要删除这条数据吗？此操作不可撤销。</p>
      <input type="hidden" id="deleteId">
    </div>
    <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 15px; border-top: 1px solid #eee; padding-top: 15px;">
      <button id="cancelDeleteBtn" class="btn" style="border: 1px solid var(--border-color);">取消</button>
      <button id="confirmDeleteBtn" class="btn btn-danger">确认删除</button>
    </div>
  </div>
</div>
 <script src="js/common.js"></script>
<script>
// 模态框相关元素
const dataModal = document.getElementById('dataModal');
const deleteModal = document.getElementById('deleteModal');
const closeModal = document.getElementById('closeModal');
const cancelBtn = document.getElementById('cancelBtn');
const saveBtn = document.getElementById('saveBtn');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const addNewBtn = document.getElementById('addNewBtn');
const modalTitle = document.getElementById('modalTitle');
const dataForm = document.getElementById('dataForm');
const dataId = document.getElementById('dataId');
const deleteId = document.getElementById('deleteId');

// 打开新增模态框
addNewBtn.addEventListener('click', function() {
  modalTitle.textContent = '新增数据';
  dataForm.reset();
  dataId.value = '';
  dataModal.style.display = 'flex';
});

// 关闭模态框
function closeDataModal() {
  dataModal.style.display = 'none';
}

function closeDeleteModal() {
  deleteModal.style.display = 'none';
}

closeModal.addEventListener('click', closeDataModal);
cancelBtn.addEventListener('click', closeDataModal);
cancelDeleteBtn.addEventListener('click', closeDeleteModal);

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
  if (event.target === dataModal) {
    closeDataModal();
  } else if (event.target === deleteModal) {
    closeDeleteModal();
  }
});

// 保存数据
saveBtn.addEventListener('click', function() {
  if (dataForm.checkValidity()) {
    // 这里只是模拟保存，实际项目中会发送到服务器
    const region = document.getElementById('region').value;
    const industry = document.getElementById('industry').value;
    const sales = document.getElementById('sales').value;
    const orders = document.getElementById('orders').value;
    const users = document.getElementById('users').value;
    const id = dataId.value;

    // 模拟计算同比增长
    const growthRate = (Math.random() * 30 - 5).toFixed(1);
    const growthClass = growthRate >= 0 ? 'success-color' : 'danger-color';
    const growthIcon = growthRate >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

    if (id) {
      // 修改操作 - 找到对应行并更新
      const row = document.querySelector(`tr[data-id="${id}"]`);
      if (row) {
        row.children[0].textContent = region;
        row.children[1].textContent = industry;
        row.children[2].textContent = `¥${Number(sales).toLocaleString()}`;
        row.children[3].textContent = Number(orders).toLocaleString();
        row.children[4].textContent = Number(users).toLocaleString();
        row.children[5].innerHTML = `<i class="fas ${growthIcon}"></i> ${Math.abs(growthRate)}%`;
        row.children[5].style.color = `var(--${growthClass})`;
      }
    } else {
      // 新增操作 - 添加新行
      const tableBody = document.querySelector('.data-table tbody');
      const newRow = document.createElement('tr');
      newRow.setAttribute('data-id', Date.now()); // 模拟ID

      newRow.innerHTML = `
        <td>${region}</td>
        <td>${industry}</td>
        <td>¥${Number(sales).toLocaleString()}</td>
        <td>${Number(orders).toLocaleString()}</td>
        <td>${Number(users).toLocaleString()}</td>
        <td style="color: var(--${growthClass});"><i class="fas ${growthIcon}"></i> ${Math.abs(growthRate)}%</td>
        <td>
          <button class="btn btn-xs"><i class="fas fa-eye"></i> 详情</button>
          <button class="btn btn-xs btn-warning edit-btn"><i class="fas fa-edit"></i> 修改</button>
          <button class="btn btn-xs btn-danger delete-btn"><i class="fas fa-trash-alt"></i> 删除</button>
        </td>
      `;

      tableBody.appendChild(newRow);

      // 为新添加的按钮绑定事件
      newRow.querySelector('.edit-btn').addEventListener('click', function() {
        openEditModal(newRow);
      });
      newRow.querySelector('.delete-btn').addEventListener('click', function() {
        openDeleteModal(newRow.getAttribute('data-id'));
      });
    }

    closeDataModal();
    alert('保存成功！');
  }
});

// 打开编辑模态框
function openEditModal(row) {
  const id = row.getAttribute('data-id');
  const region = row.children[0].textContent;
  const industry = row.children[1].textContent;
  const sales = row.children[2].textContent.replace(/[¥,]/g, '');
  const orders = row.children[3].textContent.replace(/,/g, '');
  const users = row.children[4].textContent.replace(/,/g, '');

  modalTitle.textContent = '修改数据';
  document.getElementById('region').value = region;
  document.getElementById('industry').value = industry;
  document.getElementById('sales').value = sales;
  document.getElementById('orders').value = orders;
  document.getElementById('users').value = users;
  dataId.value = id;

  dataModal.style.display = 'flex';
}

// 打开删除确认模态框
function openDeleteModal(id) {
  deleteId.value = id;
  deleteModal.style.display = 'flex';
}

// 确认删除
confirmDeleteBtn.addEventListener('click', function() {
  const id = deleteId.value;
  const row = document.querySelector(`tr[data-id="${id}"]`);

  if (row) {
    row.parentNode.removeChild(row);
    closeDeleteModal();
    alert('删除成功！');
  }
});

// 为现有的修改和删除按钮绑定事件
document.querySelectorAll('.edit-btn').forEach(btn => {
  btn.addEventListener('click', function() {
    const row = this.closest('tr');
    openEditModal(row);
  });
});

document.querySelectorAll('.delete-btn').forEach(btn => {
  btn.addEventListener('click', function() {
    const row = this.closest('tr');
    openDeleteModal(row.getAttribute('data-id'));
  });
});

// 为表格行添加data-id属性
document.querySelectorAll('.data-table tbody tr').forEach((row, index) => {
  row.setAttribute('data-id', index + 1);
});

// 新增自定义字段按钮点击事件
const addCustomFieldBtn = document.getElementById('addCustomFieldBtn');
if (addCustomFieldBtn) {
  addCustomFieldBtn.addEventListener('click', function() {
    // 弹出输入框让用户输入自定义字段名称
    const fieldName = prompt('新增自定义字段', '');
    if (fieldName) {
      // 创建一个新的表单组
      const formGroup = document.createElement('div');
      formGroup.className = 'form-group';
      formGroup.style.marginBottom = '15px';
      formGroup.setAttribute('data-field-name', fieldName);

      // 创建标签
      const label = document.createElement('label');
      label.textContent = fieldName + ':';
      label.htmlFor = 'custom_' + fieldName;

      // 创建输入框
      const input = document.createElement('input');
      input.type = 'text';
      input.id = 'custom_' + fieldName;
      input.className = 'form-control';
      input.placeholder = '请输入' + fieldName;

      // 创建删除按钮
      const deleteBtn = document.createElement('button');
      deleteBtn.type = 'button';
      deleteBtn.className = 'btn btn-xs btn-danger';
      deleteBtn.style.marginLeft = '10px';
      deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
      deleteBtn.title = '删除此字段';
      deleteBtn.addEventListener('click', function() {
        formGroup.parentNode.removeChild(formGroup);
      });

      // 组装表单组
      const container = document.createElement('div');
      container.style.display = 'flex';
      container.style.alignItems = 'center';
      container.appendChild(label);
      container.appendChild(input);
      container.appendChild(deleteBtn);
      formGroup.appendChild(container);

      // 插入到新增自定义字段按钮之前
      addCustomFieldBtn.parentNode.parentNode.insertBefore(formGroup, addCustomFieldBtn.parentNode);
    }
  });
}
</script>
</body>
</html>