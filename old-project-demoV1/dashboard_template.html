<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 大屏模板</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;" onclick="window.location.href='dashboard_template.html'">大屏模板</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理
  <div style="margin-left: 24px; margin-top: 4px; font-size: 13px;">
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='canvas_management.html'">画布创建</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='theme_management.html'">主题管理</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='cascade_order_management.html'">层叠顺序管理</div>
  </div>
</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">组件库</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理
  <div style="margin-left: 24px; margin-top: 4px; font-size: 13px;">
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_creation.html'">模板创建</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_editing.html'">模板编辑</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_list.html'">模板列表</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_deletion.html'">模板删除</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_copy.html'">模板复制</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_query.html'">模板查询</div>
    <div style="padding: 6px 0; color: var(--text-secondary); cursor: pointer;" onclick="window.location.href='template_permission.html'">模板权限控制</div>
  </div>
</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">智能问数</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-chart-area page-title-icon"></i>
      运营视图 - 大屏模板
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item active">大屏模板</div>
    </div>

    <!-- 筛选和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部模板</option>
            <option value="national">全国模板</option>
            <option value="provincial">分省模板</option>
            <option value="city">市级模板</option>
            <option value="district">区县级模板</option>
          </select>
        </div>
        <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部行业</option>
            <option value="retail">零售行业</option>
            <option value="finance">金融行业</option>
            <option value="manufacturing">制造业</option>
            <option value="service">服务业</option>
          </select>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-filter"></i> 筛选</button>
        <button class="btn btn-primary" data-modal-target="createTemplateModal"><i class="fas fa-plus"></i> 创建模板</button>
      </div>
    </div>

    <!-- 模板列表 -->
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
      <!-- 全国运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/180/800/400" alt="全国运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">全国运营监控大屏</div>
            <div style="font-size: 12px;">全国级数据总览模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 全国</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 128</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 省份运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/160/800/400" alt="省份运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">省份运营分析大屏</div>
            <div style="font-size: 12px;">省级区域数据分析模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 分省</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 通用</span>
            <span><i class="fas fa-eye"></i> 96</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>

      <!-- 城市运营大屏模板 -->
      <div class="card" style="height: 320px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 200px; overflow: hidden;">
          <img src="https://picsum.photos/id/1039/800/400" alt="城市运营大屏模板" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 12px;">
            <div style="font-size: 16px; font-weight: 500;">城市运营监控大屏</div>
            <div style="font-size: 12px;">市级区域数据监控模板</div>
          </div>
        </div>
        <div style="flex-grow: 1; display: flex; flex-direction: column; justify-content: space-between; padding: 16px 0;">
          <div style="display: flex; align-items: center; font-size: 14px; color: var(--text-tertiary);">
            <span style="margin-right: 16px;"><i class="fas fa-map-marker-alt"></i> 市级</span>
            <span style="margin-right: 16px;"><i class="fas fa-industry"></i> 零售</span>
            <span><i class="fas fa-eye"></i> 72</span>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
            <button class="btn btn-primary"><i class="fas fa-play"></i> 预览</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 创建模板模态框 -->
  <div class="modal" id="createTemplateModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 创建大屏模板</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div style="margin-bottom: 16px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 500;">模板名称</label>
          <input type="text" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;" placeholder="请输入模板名称">
        </div>
        <div style="margin-bottom: 16px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 500;">模板类型</label>
          <select style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
            <option value="national">全国模板</option>
            <option value="provincial">分省模板</option>
            <option value="city">市级模板</option>
            <option value="district">区县级模板</option>
          </select>
        </div>
        <div style="margin-bottom: 16px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 500;">适用行业</label>
          <select style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
            <option value="all">全部行业</option>
            <option value="retail">零售行业</option>
            <option value="finance">金融行业</option>
            <option value="manufacturing">制造业</option>
            <option value="service">服务业</option>
          </select>
        </div>
        <div style="margin-bottom: 16px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 500;">模板描述</label>
          <textarea style="width: 100%; height: 80px; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; resize: none;" placeholder="请输入模板描述"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="margin-right: 12px;">取消</button>
        <button class="btn btn-primary">创建</button>
      </div>
    </div>
  </div>

  <script>
    // 模态框功能
    document.addEventListener('DOMContentLoaded', function() {
      const modal = document.getElementById('createTemplateModal');
      const modalBtn = document.querySelector('[data-modal-target="createTemplateModal"]');
      const closeBtn = document.querySelector('.modal-close');

      if (modalBtn) {
        modalBtn.addEventListener('click', function() {
          modal.style.display = 'block';
        });
      }

      if (closeBtn) {
        closeBtn.addEventListener('click', function() {
          modal.style.display = 'none';
        });
      }

      window.addEventListener('click', function(event) {
        if (event.target === modal) {
          modal.style.display = 'none';
        }
      });
    });
  </script>
</body>
</html>