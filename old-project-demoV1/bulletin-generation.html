<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 运营通报生成与审核</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 共用样式补充 */
    :root {
      --primary-color: #165DFF;
      --danger-color: #F53F3F;
      --success-color: #00B42A;
      --warning-color: #FF7D00;
      --text-primary: #1D2129;
      --text-secondary: #4E5969;
      --text-tertiary: #86909C;
      --border-color: #D9D9D9;
      --bg-color: #F2F3F5;
                  --primary-color: #1890ff;
  --secondary-color: #0050b3;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #ff4d4f;
  --info-color: #1890ff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #e8e8e8;
  --hover-color: #f0f0f0;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: var(--text-primary);
      background-color: #F7F8FA;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }
    
    .navbar {
      background-color: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      padding: 0 20px;
    }
    
    .navbar-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60px;
    }
    
    .logo {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
    }
    
    .logo i {
      margin-right: 8px;
    }
/*     
    .sidebar {
      width: 220px;
      background-color: white;
      border-right: 1px solid var(--border-color);
      padding: 20px 0;
      height: calc(100vh - 60px);
      overflow-y: auto;
    }
     */
    /* .menu-item {
      padding: 12px 20px;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .menu-item.active {
      background-color: rgba(22, 93, 255, 0.1);
      color: var(--primary-color);
      border-left: 3px solid var(--primary-color);
    }
     */
    .menu-icon {
      margin-right: 10px;
      width: 20px;
      text-align: center;
    }
    
    .main-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
    
    /* .page-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    
    .page-title-icon {
      margin-right: 8px;
      color: var(--primary-color);
    } */
    
    .breadcrumb {
      display: flex;
      margin-bottom: 20px;
      color: var(--text-tertiary);
      font-size: 14px;
    }
    
    .breadcrumb-item {
      display: flex;
      align-items: center;
    }
    
    /* .breadcrumb-item:not(:last-child)::after {
      content: "/";
      margin: 0 8px;
    } */
    
    .breadcrumb-item.active {
      color: var(--text-primary);
    }
    
    .tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
    }
    
    .tab {
      padding: 10px 20px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s;
    }
    
    .tab.active {
      border-bottom-color: var(--primary-color);
      color: var(--primary-color);
      font-weight: 500;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .card {
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      margin-bottom: 20px;
    }
    
    .table-container {
      overflow-x: auto;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      text-align: left;
    }
    
    th, td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-color);
    }
    
    th {
      background-color: #F7F8FA;
      font-weight: 500;
      white-space: nowrap;
    }
    
    .pagination {
      display: flex;
      justify-content: center;
      padding: 16px;
    }
    
    .pagination-item {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      margin: 0 4px;
      cursor: pointer;
      border: 1px solid var(--border-color);
    }
    
    .pagination-item.active {
      background-color: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
    
    .search-box {
      position: relative;
      margin-bottom: 16px;
    }
    
    .search-box input {
      width: 100%;
      padding: 8px 12px 8px 36px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    .search-box-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-tertiary);
    }
    
    .tag {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .tag-success {
      background-color: rgba(0, 180, 42, 0.1);
      color: var(--success-color);
    }
    
    .tag-warning {
      background-color: rgba(255, 125, 0, 0.1);
      color: var(--warning-color);
    }
    
    .tag-danger {
      background-color: rgba(245, 63, 63, 0.1);
      color: var(--danger-color);
    }
    
    /* 详情模态框样式 */
    .detail-modal .modal-content { width: 700px; }
    .detail-item { margin-bottom: 16px; }
    .detail-label { 
      display: inline-block; 
      width: 140px; 
      font-weight: 500; 
      color: #666; 
    }
    .detail-value { 
      display: inline-block; 
      vertical-align: top; 
      max-width: calc(100% - 140px); 
      word-break: break-word; 
    }
    
    /* 模态框基础样式补充 */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      align-items: center;
      justify-content: center;
    }
    
    .modal.show {
      display: flex;
    }
    
    .modal-content {
      background-color: white;
      border-radius: 4px;
      width: 600px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-height: 90vh;
      display: flex;
      flex-direction: column;
    }
    
    .modal-header {
      padding: 16px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 16px;
    }
    
    .modal-close {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #999;
    }
    
    .modal-body {
      padding: 16px;
      overflow-y: auto;
      flex: 1;
    }
    
    .modal-footer {
      padding: 12px 16px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
    
    .btn {
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      background-color: transparent;
      border: none;
      font-size: 14px;
    }
    
    .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }
    
    .btn-success {
      background-color: var(--success-color);
      color: white;
    }
    
    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }
    
    .form-group {
      margin-bottom: 16px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    .form-group textarea {
      min-height: 100px;
      resize: vertical;
    }
    
    .process-steps {
      display: flex;
      justify-content: space-between;
      margin: 30px 0;
      position: relative;
    }
    
    .process-steps::before {
      content: '';
      position: absolute;
      top: 15px;
      left: 50px;
      right: 50px;
      height: 2px;
      background-color: var(--border-color);
      z-index: 1;
    }
    
    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 2;
      width: 16%;
    }
    
    .step-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: white;
      border: 2px solid var(--border-color);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
    }
    
    .step.active .step-icon {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      color: white;
    }
    
    .step.completed .step-icon {
      background-color: var(--success-color);
      border-color: var(--success-color);
      color: white;
    }
    
    .step-name {
      font-size: 12px;
      text-align: center;
    }
    
    .editor-toolbar {
      border: 1px solid var(--border-color);
      border-bottom: none;
      padding: 8px;
      background-color: #F7F8FA;
      border-radius: 4px 4px 0 0;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .editor-button {
      padding: 4px 8px;
      border: none;
      background: none;
      border-radius: 2px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .editor-button:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .editor-content {
      border: 1px solid var(--border-color);
      min-height: 300px;
      padding: 12px;
      border-radius: 0 0 4px 4px;
    }
    
    .template-variable {
      display: inline-block;
      background-color: rgba(22, 93, 255, 0.1);
      color: var(--primary-color);
      padding: 0 4px;
      border-radius: 2px;
      margin: 0 2px;
    }
    
    .variable-list {
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 12px;
      margin-top: 8px;
    }
    
    .variable-item {
      display: inline-block;
      background-color: #F7F8FA;
      padding: 4px 8px;
      border-radius: 4px;
      margin: 0 4px 8px 0;
      cursor: pointer;
      font-size: 13px;
    }
    
    .variable-item:hover {
      background-color: rgba(22, 93, 255, 0.1);
      color: var(--primary-color);
    }
    
    .insight-result-item {
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      margin-bottom: 12px;
      background-color: #F7F8FA;
    }
    
    .insight-result-item:hover {
      background-color: rgba(22, 93, 255, 0.05);
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <div style="display: flex; flex: 1; overflow: hidden;">
    <!-- 侧边栏 -->
    <!-- <div class="sidebar">
      <div class="menu-item">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>
      <div class="menu-item">
        <i class="fas fa-database menu-icon"></i>
        <span class="menu-text">数据融通</span>
      </div>
      <div class="menu-item active">
        <i class="fas fa-chart-pie menu-icon"></i>
        <span class="menu-text">智能洞察分析</span>
        <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
          <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营报告管理</div>
          <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营通报管理</div>
          <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">通报生成与审核</div>
        </div>
      </div>
      <div class="menu-item">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>
      <div class="menu-item">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>
      <div class="menu-item">
        <i class="fas fa-tasks menu-icon"></i>
        <span class="menu-text">五级穿透调度</span>
      </div>
      <div class="menu-item">
        <i class="fas fa-server menu-icon"></i>
        <span class="menu-text">微服务管理</span>
      </div>
      <div class="menu-item">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>
      <div class="menu-item">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div> -->
     <!-- <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="report_management.html">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin_management.html">运营通报管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;" data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;" data-href="bulletin-generation.html">运营通报生成与审核</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div> -->
 <div class="sidebar">
      <div class="menu-item" data-href="index.html">
        <i class="fas fa-home menu-icon"></i>
        <span class="menu-text">首页</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="data">
          <i class="fas fa-database menu-icon"></i>
          <span class="menu-text">数据融通</span>
          <i class="fas menu-arrow fa-chevron-right"></i>
        </div>
        <div class="sub-menu" id="data-group" style="display: none">
          <div class="menu-item child" data-href="data_source.html">数据源管理</div>
          <div class="menu-item child" data-href="offline_task.html">离线采集任务管理</div>
          <div class="menu-item child" data-href="realtime_task.html">实时采集任务管理</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent active" data-group="insight">
          <i class="fas fa-chart-pie menu-icon"></i>
          <span class="menu-text" data-href="report_management.html">智能洞察分析</span>
          <i class="fas menu-arrow fa-chevron-down"></i>
        </div>
        <div class="sub-menu" id="insight-group">
          <div class="menu-item child " data-href="report_management.html">运营报告管理</div>
          <div class="menu-item child " data-href="bulletin_management.html">运营通报管理</div>
          <div class="menu-item child " data-href="bulletin-browse-statistics-v4.html">运营通报浏览查询统计</div>
          <div class="menu-item child active" data-href="bulletin-generation.html">运营通报生成与审核</div>
        </div>
      </div>

      <div class="menu-item" data-href="operation_views.html">
        <i class="fas fa-chart-bar menu-icon"></i>
        <span class="menu-text">运营视图</span>
      </div>

      <div class="menu-item" data-href="unified_portal.html">
        <i class="fas fa-tachometer-alt menu-icon"></i>
        <span class="menu-text">统一运营门户</span>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="penetration">
          <i class="fas fa-tasks menu-icon"></i>
          <span class="menu-text">五级穿透调度</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="penetration-group" style="display: none">
          <div class="menu-item child" data-href="five_level_penetration.html">五级穿透</div>
          <div class="menu-item child" data-href="task_scheduling.html">任务调度</div>
        </div>
      </div>

      <div class="menu-group">
        <div class="menu-item parent" data-group="microservice">
          <i class="fas fa-server menu-icon"></i>
          <span class="menu-text">微服务管理</span>
          <i class="fas fa-chevron-right menu-arrow"></i>
        </div>
        <div class="sub-menu" id="microservice-group" style="display: none">
          <div class="menu-item child parent" data-group="deployment">
            <span>服务部署</span>
            <i class="fas fa-chevron-right menu-arrow"></i>
          </div>
          <div class="sub-menu" id="deployment-group" style="display: none">
            <div class="menu-item child" data-href="microservice_deployment.html">应用部署</div>
            <div class="menu-item child">容器编排</div>
            <div class="menu-item child">资源分配</div>
          </div>
          <div class="menu-item child">集群管理</div>
          <div class="menu-item child">CI/CD流水线</div>
        </div>
      </div>

      <div class="menu-item" data-href="offline_task.html">
        <i class="fas fa-user-shield menu-icon"></i>
        <span class="menu-text">权限管理</span>
      </div>

      <div class="menu-item" data-href="bulletin_management.html">
        <i class="fas fa-cog menu-icon"></i>
        <span class="menu-text">系统设置</span>
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="page-title">
        <i class="fas fa-robot page-title-icon"></i>
        运营通报生成与审核
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb">
        <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
        <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
        <div class="breadcrumb-item active">通报生成与审核</div>
      </div>

      <!-- 标签页 -->
      <div class="tabs">
        <div class="tab active" data-tab-target="autoGeneration">自动生成通报</div>
        <div class="tab" data-tab-target="manualEntry">手工录入通报</div>
        <div class="tab" data-tab-target="reviewManagement">通报审核管理</div>
      </div>

      <!-- 自动生成通报 -->
      <div class="tab-content active" id="autoGeneration">
        <div class="card">
          <div class="modal-body" style="border-bottom: 1px solid #eee;">
            <h3 style="margin-top: 0;">自动生成流程</h3>
            <div class="process-steps">
              <div class="step active" id="step1">
                <div class="step-icon"><i class="fas fa-chart-pie"></i></div>
                <div class="step-name">获取洞察分析结果</div>
              </div>
              <div class="step" id="step2">
                <div class="step-icon"><i class="fas fa-heading"></i></div>
                <div class="step-name">生成通报标题</div>
              </div>
              <div class="step" id="step3">
                <div class="step-icon"><i class="fas fa-calculator"></i></div>
                <div class="step-name">填充指标数值</div>
              </div>
              <div class="step" id="step4">
                <div class="step-icon"><i class="fas fa-users"></i></div>
                <div class="step-name">确定通报对象</div>
              </div>
              <div class="step" id="step5">
                <div class="step-icon"><i class="fas fa-file-alt"></i></div>
                <div class="step-name">生成通报全文</div>
              </div>
              <div class="step" id="step6">
                <div class="step-icon"><i class="fas fa-bell"></i></div>
                <div class="step-name">发送审核提醒</div>
              </div>
            </div>
          </div>

          <!-- 步骤1：获取洞察分析结果 -->
          <div class="process-content" id="content1" style="padding: 20px;">
            <h4 style="margin-top: 0;"><i class="fas fa-chart-pie"></i> 获取洞察分析结果</h4>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 12px;">
              <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                <div class="search-box" style="width: 200px; margin-bottom: 0;">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" id="insightType" placeholder="运营通报类型">
                </div>
                <div class="search-box" style="width: 200px; margin-bottom: 0;">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" id="insightRegion" placeholder="运营通报区域范围">
                </div>
                <div class="search-box" style="width: 200px; margin-bottom: 0;">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" id="insightParam" placeholder="模板内容参数">
                </div>
                <button class="btn btn-primary" onclick="searchInsightResults()"><i class="fas fa-search"></i> 查询洞察结果</button>
              </div>
            </div>

            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th>模板内容参数名称</th>
                    <th>指标名称</th>
                    <th>指标编码</th>
                    <th>指标值</th>
                    <th>运营通报区域</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="insightResultsTable">
                  <!-- 结果将通过JS动态填充 -->
                </tbody>
              </table>
            </div>

            <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
              <button class="btn btn-primary" onclick="goToStep(2)"><i class="fas fa-arrow-right"></i> 下一步：生成通报标题</button>
            </div>
          </div>

          <!-- 步骤2：生成通报标题 -->
          <div class="process-content" id="content2" style="padding: 20px; display: none;">
            <h4 style="margin-top: 0;"><i class="fas fa-heading"></i> 生成运营通报标题</h4>
            
            <div style="margin-bottom: 20px;">
              <div class="form-group">
                <label for="titleBulletinType">运营通报类型 <span style="color: red;">*</span></label>
                <select id="titleBulletinType" required>
                  <option value="">请选择通报类型</option>
                  <option value="business">商客业务发展通报</option>
                  <option value="support">商客支撑效能通报</option>
                  <option value="tool">商客工具使用通报</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="titleTime">通报时间 <span style="color: red;">*</span></label>
                <input type="text" id="titleTime" placeholder="例如：2023年8月 或 2023年第三季度">
              </div>
              
              <div class="form-group">
                <label for="titleRegion">通报区域范围 <span style="color: red;">*</span></label>
                <input type="text" id="titleRegion" placeholder="例如：全国 或 华东地区">
              </div>
              
              <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                <button class="btn btn-primary" onclick="generateTitle()"><i class="fas fa-magic"></i> 调用大模型生成标题</button>
                <button class="btn" style="border: 1px solid var(--border-color);" onclick="resetTitle()"><i class="fas fa-sync-alt"></i> 重置</button>
              </div>
              
              <div class="form-group">
                <label for="generatedTitle">生成的运营通报标题</label>
                <input type="text" id="generatedTitle" readonly style="background-color: #f5f5f5;">
              </div>
              
              <div class="form-group">
                <label>生成请求与应答报文</label>
                <div style="display: flex; gap: 10px;">
                  <div style="flex: 1;">
                    <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">请求报文</div>
                    <textarea id="titleRequest" style="width: 100%; height: 100px; font-family: monospace; font-size: 12px;" readonly></textarea>
                  </div>
                  <div style="flex: 1;">
                    <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">应答报文</div>
                    <textarea id="titleResponse" style="width: 100%; height: 100px; font-family: monospace; font-size: 12px;" readonly></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
              <button class="btn" style="border: 1px solid var(--border-color);" onclick="goToStep(1)"><i class="fas fa-arrow-left"></i> 上一步</button>
              <button class="btn btn-primary" onclick="saveTitleAndGoToStep3()"><i class="fas fa-arrow-right"></i> 下一步：填充指标数值</button>
            </div>
          </div>

          <!-- 步骤3：填充运营通报内容指标值 -->
          <div class="process-content" id="content3" style="padding: 20px; display: none;">
            <h4 style="margin-top: 0;"><i class="fas fa-calculator"></i> 填充运营通报内容指标值</h4>
            
            <div class="form-group">
              <label>选择通报模板 <span style="color: red;">*</span></label>
              <select id="templateSelect" onchange="loadTemplateContent()">
                <option value="">请选择模板</option>
                <option value="template1">月度业务通报模板</option>
                <option value="template2">支撑效能通报模板</option>
                <option value="template3">工具使用通报模板</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>模板内容</label>
              <div class="editor-content" id="templateContent" style="min-height: 200px;"></div>
            </div>
            
            <div class="form-group">
              <label>待填充的指标值</label>
              <div id="指标值列表">
                <!-- 将通过JS动态填充 -->
              </div>
            </div>
            
            <div class="form-group">
              <label>填充后的内容</label>
              <div class="editor-content" id="filledContent" style="min-height: 200px;"></div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
              <button class="btn" style="border: 1px solid var(--border-color);" onclick="goToStep(2)"><i class="fas fa-arrow-left"></i> 上一步</button>
              <button class="btn btn-primary" onclick="saveFilledContentAndGoToStep4()"><i class="fas fa-arrow-right"></i> 下一步：确定通报对象</button>
            </div>
          </div>

          <!-- 步骤4：生成运营通报对象 -->
          <div class="process-content" id="content4" style="padding: 20px; display: none;">
            <h4 style="margin-top: 0;"><i class="fas fa-users"></i> 生成运营通报对象</h4>
            
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 12px;">
              <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                <div class="search-box" style="width: 200px; margin-bottom: 0;">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" id="recipientType" placeholder="运营通报类型">
                </div>
                <div class="search-box" style="width: 200px; margin-bottom: 0;">
                  <i class="fas fa-search search-box-icon"></i>
                  <input type="text" id="recipientRegion" placeholder="运营通报区域范围">
                </div>
                <button class="btn btn-primary" onclick="searchRecipients()"><i class="fas fa-search"></i> 查询通报对象</button>
              </div>
            </div>

            <div class="table-container">
              <table class="table">
                <thead>
                  <tr>
                    <th><input type="checkbox" id="selectAllRecipients"></th>
                    <th>通报对象组织机构</th>
                    <th>通报对象角色</th>
                    <th>通报对象工号</th>
                    <th>通报对象姓名</th>
                  </tr>
                </thead>
                <tbody id="recipientsTable">
                  <!-- 结果将通过JS动态填充 -->
                </tbody>
              </table>
            </div>
            
            <div class="form-group">
              <label>已选择的通报对象</label>
              <div id="selectedRecipients" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; min-height: 60px;"></div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
              <button class="btn" style="border: 1px solid var(--border-color);" onclick="goToStep(3)"><i class="fas fa-arrow-left"></i> 上一步</button>
              <button class="btn btn-primary" onclick="saveRecipientsAndGoToStep5()"><i class="fas fa-arrow-right"></i> 下一步：生成通报全文</button>
            </div>
          </div>

          <!-- 步骤5：生成运营通报内容全文 -->
          <div class="process-content" id="content5" style="padding: 20px; display: none;">
            <h4 style="margin-top: 0;"><i class="fas fa-file-alt"></i> 生成运营通报内容全文</h4>
            
            <div class="form-group">
              <label>运营通报标题</label>
              <input type="text" id="finalTitle" readonly style="background-color: #f5f5f5;">
            </div>
            
            <div class="form-group">
              <label>填充后的模板内容</label>
              <div class="editor-content" id="finalFilledContent" style="min-height: 200px;"></div>
            </div>
            
            <div class="form-group">
              <label>通报对象</label>
              <div id="finalRecipients" style="padding: 12px; border: 1px solid var(--border-color); border-radius: 4px; min-height: 60px;"></div>
            </div>
            
            <div style="margin-bottom: 20px;">
              <button class="btn btn-primary" onclick="generateFullContent()"><i class="fas fa-magic"></i> 调用大模型生成全文</button>
            </div>
            
            <div class="form-group">
              <label>生成的运营通报全文</label>
              <div class="editor-toolbar">
                <button class="editor-button"><i class="fas fa-bold"></i></button>
                <button class="editor-button"><i class="fas fa-italic"></i></button>
                <button class="editor-button"><i class="fas fa-underline"></i></button>
                <button class="editor-button"><i class="fas fa-heading"></i></button>
                <button class="editor-button"><i class="fas fa-list-ul"></i></button>
                <button class="editor-button"><i class="fas fa-list-ol"></i></button>
                <button class="editor-button"><i class="fas fa-table"></i></button>
                <button class="editor-button"><i class="fas fa-image"></i></button>
              </div>
              <div class="editor-content" id="fullContent" contenteditable="true"></div>
            </div>
            
            <div class="form-group">
              <label>生成请求与应答报文</label>
              <div style="display: flex; gap: 10px;">
                <div style="flex: 1;">
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">请求报文</div>
                  <textarea id="contentRequest" style="width: 100%; height: 100px; font-family: monospace; font-size: 12px;" readonly></textarea>
                </div>
                <div style="flex: 1;">
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">应答报文</div>
                  <textarea id="contentResponse" style="width: 100%; height: 100px; font-family: monospace; font-size: 12px;" readonly></textarea>
                </div>
              </div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
              <button class="btn" style="border: 1px solid var(--border-color);" onclick="goToStep(4)"><i class="fas fa-arrow-left"></i> 上一步</button>
              <button class="btn btn-primary" onclick="saveFullContentAndGoToStep6()"><i class="fas fa-arrow-right"></i> 下一步：发送审核提醒</button>
            </div>
          </div>

          <!-- 步骤6：发送运营通报生成提醒 -->
          <div class="process-content" id="content6" style="padding: 20px; display: none;">
            <h4 style="margin-top: 0;"><i class="fas fa-bell"></i> 发送运营通报生成提醒</h4>
            
            <div class="form-group">
              <label>运营通报实例ID</label>
              <input type="text" id="bulletinInstanceId" readonly style="background-color: #f5f5f5;">
            </div>
            
            <div class="form-group">
              <label>运营通报标题</label>
              <input type="text" id="reminderTitle" readonly style="background-color: #f5f5f5;">
            </div>
            
            <div class="form-group">
              <label>运营通报生成时间</label>
              <input type="text" id="generationTime" readonly style="background-color: #f5f5f5;">
            </div>
            
            <div class="form-group">
              <label>通报生成提醒对象 <span style="color: red;">*</span></label>
              <input type="text" id="reminderRecipients" placeholder="请输入提醒对象工号，多个用逗号分隔">
            </div>
            
            <div class="form-group">
              <label>通报生成提醒方式 <span style="color: red;">*</span></label>
              <div style="display: flex; gap: 15px; margin-top: 8px;">
                <label style="display: flex; align-items: center; font-weight: normal;">
                  <input type="checkbox" name="reminderMethod" value="system" checked> 系统消息
                </label>
                <label style="display: flex; align-items: center; font-weight: normal;">
                  <input type="checkbox" name="reminderMethod" value="email"> 邮件
                </label>
                <label style="display: flex; align-items: center; font-weight: normal;">
                  <input type="checkbox" name="reminderMethod" value="sms"> 短信
                </label>
              </div>
            </div>

            <div style="display: flex; justify-content: space-between; margin-top: 20px;">
              <button class="btn" style="border: 1px solid var(--border-color);" onclick="goToStep(5)"><i class="fas fa-arrow-left"></i> 上一步</button>
              <button class="btn btn-success" onclick="sendReminder()"><i class="fas fa-paper-plane"></i> 发送审核提醒</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 手工录入通报 -->
      <div class="tab-content" id="manualEntry">
        <div class="card">
          <div style="padding: 20px;">
            <h3 style="margin-top: 0;">手工录入运营通报</h3>
            
            <form id="manualBulletinForm">
              <div class="form-group">
                <label for="manualBulletinType">运营通报类型 <span style="color: red;">*</span></label>
                <select id="manualBulletinType" required>
                  <option value="">请选择通报类型</option>
                  <option value="business">商客业务发展通报</option>
                  <option value="support">商客支撑效能通报</option>
                  <option value="tool">商客工具使用通报</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="manualTitle">运营通报标题 <span style="color: red;">*</span></label>
                <input type="text" id="manualTitle" required placeholder="请输入通报标题">
              </div>
              
              <div class="form-group">
                <label for="manualRecipients">运营通报对象 <span style="color: red;">*</span></label>
                <div style="display: flex; gap: 10px;">
                  <input type="text" id="manualRecipients" required placeholder="请输入通报对象，多个用逗号分隔">
                  <button type="button" class="btn" style="border: 1px solid var(--border-color); width: auto;" data-modal-target="selectRecipientsModal">选择</button>
                </div>
              </div>
              
              <div class="form-group">
                <label for="manualContent">运营通报内容 <span style="color: red;">*</span></label>
                <div class="editor-toolbar">
                  <button class="editor-button" type="button"><i class="fas fa-bold"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-italic"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-underline"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-heading"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-list-ul"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-list-ol"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-table"></i></button>
                  <button class="editor-button" type="button"><i class="fas fa-image"></i></button>
                </div>
                <div class="editor-content" id="manualContent" contenteditable="true"></div>
              </div>
              
              <div class="form-group">
                <label>可选：插入指标变量</label>
                <div class="variable-list">
                  <div class="variable-item" onclick="insertVariable('{totalUsers}')">{totalUsers}：总用户数</div>
                  <div class="variable-item" onclick="insertVariable('{newUsers}')">{newUsers}：新增用户数</div>
                  <div class="variable-item" onclick="insertVariable('{activityRate}')">{activityRate}：活跃度</div>
                  <div class="variable-item" onclick="insertVariable('{revenue}')">{revenue}：营收</div>
                  <div class="variable-item" onclick="insertVariable('{growthRate}')">{growthRate}：增长率</div>
                </div>
              </div>
              
              <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                <button type="button" class="btn" style="border: 1px solid var(--border-color);" onclick="resetManualForm()">重置</button>
                <button type="submit" class="btn btn-primary">保存并提交审核</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 通报审核管理 -->
      <div class="tab-content" id="reviewManagement">
        <!-- 搜索和操作栏 -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 12px;">
          <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
            <div class="search-box" style="width: 200px; margin-bottom: 0;">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="运营通报标题">
            </div>
            <div class="search-box" style="width: 200px; margin-bottom: 0;">
              <i class="fas fa-search search-box-icon"></i>
              <input type="text" placeholder="通报实例ID">
            </div>
            <div style="margin-bottom: 0;">
              <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="all">全部审核状态</option>
                <option value="pending">待审核</option>
                <option value="approved">审核通过</option>
                <option value="rejected">审核驳回</option>
              </select>
            </div>
            <button class="btn" style="border: 1px solid var(--border-color);"><i class="fas fa-search"></i> 查询</button>
            <button class="btn" style="border: 1px solid var(--border-color);"><i class="fas fa-sync-alt"></i> 重置</button>
          </div>
        </div>

        <!-- 通报审核列表 -->
        <div class="card">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th>通报实例ID</th>
                  <th>运营通报标题</th>
                  <th>通报类型</th>
                  <th>创建方式</th>
                  <th>创建人</th>
                  <th>创建时间</th>
                  <th>审核状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>BUL-INS-202308001</td>
                  <td>2023年8月商客业务发展通报</td>
                  <td>商客业务发展通报</td>
                  <td>自动生成</td>
                  <td>系统自动</td>
                  <td>2023-08-01 09:00</td>
                  <td><span class="tag tag-warning">待审核</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" onclick="viewBulletinForReview('BUL-INS-202308001')"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="reviewModal"><i class="fas fa-check-circle"></i> 审核</button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-INS-202308002</td>
                  <td>7月支撑响应效能分析通报</td>
                  <td>商客支撑效能通报</td>
                  <td>手工录入</td>
                  <td>张三</td>
                  <td>2023-08-01 10:15</td>
                  <td><span class="tag tag-warning">待审核</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" onclick="viewBulletinForReview('BUL-INS-202308002')"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="reviewModal"><i class="fas fa-check-circle"></i> 审核</button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-INS-202307045</td>
                  <td>第二季度智能工具使用情况通报</td>
                  <td>商客工具使用通报</td>
                  <td>自动生成</td>
                  <td>系统自动</td>
                  <td>2023-07-05 14:30</td>
                  <td><span class="tag tag-success">审核通过</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" onclick="viewBulletinForReview('BUL-INS-202307045')"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--text-tertiary);" disabled><i class="fas fa-check-circle"></i> 已审核</button>
                  </td>
                </tr>
                <tr>
                  <td>BUL-INS-202307032</td>
                  <td>6月业务发展问题通报</td>
                  <td>商客业务发展通报</td>
                  <td>手工录入</td>
                  <td>李四</td>
                  <td>2023-07-01 09:45</td>
                  <td><span class="tag tag-danger">审核驳回</span></td>
                  <td>
                    <button class="btn" style="color: var(--primary-color);" onclick="viewBulletinForReview('BUL-INS-202307032')"><i class="fas fa-eye"></i></button>
                    <button class="btn" style="color: var(--primary-color);" data-modal-target="reviewModal"><i class="fas fa-check-circle"></i> 重新审核</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="pagination">
            <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
            <div class="pagination-item active">1</div>
            <div class="pagination-item">2</div>
            <div class="pagination-item">3</div>
            <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 选择通报对象模态框 -->
  <div class="modal" id="selectRecipientsModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-users"></i> 选择通报对象</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="search-box" style="margin-bottom: 16px;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" placeholder="搜索对象姓名或工号...">
        </div>
        
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th><input type="checkbox" id="selectAllModalRecipients"></th>
                <th>姓名</th>
                <th>工号</th>
                <th>组织机构</th>
                <th>角色</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><input type="checkbox" class="recipient-checkbox" data-name="张三" data-id="EMP001"></td>
                <td>张三</td>
                <td>EMP001</td>
                <td>商客业务部</td>
                <td>经理</td>
              </tr>
              <tr>
                <td><input type="checkbox" class="recipient-checkbox" data-name="李四" data-id="EMP002"></td>
                <td>李四</td>
                <td>EMP002</td>
                <td>商客业务部</td>
                <td>主管</td>
              </tr>
              <tr>
                <td><input type="checkbox" class="recipient-checkbox" data-name="王五" data-id="EMP003"></td>
                <td>王五</td>
                <td>EMP003</td>
                <td>支撑部</td>
                <td>经理</td>
              </tr>
              <tr>
                <td><input type="checkbox" class="recipient-checkbox" data-name="赵六" data-id="EMP004"></td>
                <td>赵六</td>
                <td>EMP004</td>
                <td>技术部</td>
                <td>主管</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('selectRecipientsModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="confirmRecipientsSelection()">确定</button>
      </div>
    </div>
  </div>

  <!-- 审核模态框 -->
  <div class="modal" id="reviewModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-check-circle"></i> 运营通报审核</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="reviewForm">
          <div class="form-group">
            <label for="reviewBulletinId">运营通报实例ID</label>
            <input type="text" id="reviewBulletinId" readonly style="background-color: #f5f5f5;">
          </div>
          
          <div class="form-group">
            <label for="reviewBulletinTitle">运营通报标题</label>
            <input type="text" id="reviewBulletinTitle" readonly style="background-color: #f5f5f5;">
          </div>
          
          <div class="form-group">
            <label>审核结果 <span style="color: red;">*</span></label>
            <div style="display: flex; gap: 15px; margin-top: 8px;">
              <label style="display: flex; align-items: center; font-weight: normal;">
                <input type="radio" name="reviewResult" value="approved" checked> 审核通过
              </label>
              <label style="display: flex; align-items: center; font-weight: normal;">
                <input type="radio" name="reviewResult" value="rejected"> 审核驳回
              </label>
            </div>
          </div>
          
          <div class="form-group">
            <label for="reviewComments">审核意见 <span style="color: red;">*</span></label>
            <textarea id="reviewComments" rows="4" required placeholder="请输入审核意见"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('reviewModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="submitReview()">提交审核</button>
      </div>
    </div>
  </div>

  <!-- 查看通报详情模态框 -->
  <div class="modal detail-modal" id="viewBulletinDetailModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-file-alt"></i> 运营通报详情</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="detail-item">
          <span class="detail-label">通报实例ID：</span>
          <span class="detail-value" id="bulletinDetailId"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">运营通报标题：</span>
          <span class="detail-value" id="bulletinDetailTitle"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">通报类型：</span>
          <span class="detail-value" id="bulletinDetailType"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建方式：</span>
          <span class="detail-value" id="bulletinDetailCreateMethod"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建人：</span>
          <span class="detail-value" id="bulletinDetailCreator"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建时间：</span>
          <span class="detail-value" id="bulletinDetailCreateTime"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">审核状态：</span>
          <span class="detail-value" id="bulletinDetailStatus"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">通报对象：</span>
          <span class="detail-value" id="bulletinDetailRecipients"></span>
        </div>
        <div class="detail-item">
          <span class="detail-label">通报内容：</span>
          <span class="detail-value" id="bulletinDetailContent" style="display: block; margin-top: 8px;"></span>
        </div>
        <div class="detail-item" id="reviewOpinionsSection" style="display: none;">
          <span class="detail-label">审核意见：</span>
          <span class="detail-value" id="bulletinDetailReviewOpinions"><span class="detail-value" id="bulletinDetailReviewOpinions"></span>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" id="editBulletinBtn" onclick="editBulletin()">修改</button>
        <button class="btn btn-primary" onclick="document.getElementById('viewBulletinDetailModal').classList.remove('show')">关闭</button>
      </div>
    </div>
  </div>

  <!-- 编辑通报模态框 -->
  <div class="modal" id="editBulletinModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 修改运营通报</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="editBulletinForm">
          <div class="form-group">
            <label for="editBulletinInstanceId">运营通报实例ID</label>
            <input type="text" id="editBulletinInstanceId" readonly style="background-color: #f5f5f5;">
          </div>
          
          <div class="form-group">
            <label for="editBulletinTitle">运营通报标题 <span style="color: red;">*</span></label>
            <input type="text" id="editBulletinTitle" required>
          </div>
          
          <div class="form-group">
            <label for="editBulletinRecipients">运营通报对象 <span style="color: red;">*</span></label>
            <input type="text" id="editBulletinRecipients" required>
          </div>
          
          <div class="form-group">
            <label for="editBulletinContent">运营通报内容 <span style="color: red;">*</span></label>
            <div class="editor-toolbar">
              <button class="editor-button" type="button"><i class="fas fa-bold"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-italic"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-underline"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-heading"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-list-ul"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-list-ol"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-table"></i></button>
              <button class="editor-button" type="button"><i class="fas fa-image"></i></button>
            </div>
            <div class="editor-content" id="editBulletinContent" contenteditable="true"></div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editBulletinModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="saveBulletinEdit()">保存修改</button>
      </div>
    </div>
  </div>
 <script src="js/common.js"></script>
  <script>
    // 当前步骤
    let currentStep = 1;
    // 存储生成过程中的数据
    let bulletinData = {
      insightResults: [],
      title: '',
      filledContent: '',
      recipients: [],
      fullContent: ''
    };

    // 标签页切换
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
        tab.classList.add('active');
        const target = tab.getAttribute('data-tab-target');
        document.getElementById(target).classList.add('active');
      });
    });

    // 步骤导航
    function goToStep(step) {
      // 隐藏当前步骤内容
      document.getElementById(`content${currentStep}`).style.display = 'none';
      document.getElementById(`step${currentStep}`).classList.remove('active');
      
      // 显示目标步骤内容
      currentStep = step;
      document.getElementById(`content${currentStep}`).style.display = 'block';
      document.getElementById(`step${currentStep}`).classList.add('active');
      
      // 更新步骤状态
      for (let i = 1; i < currentStep; i++) {
        document.getElementById(`step${i}`).classList.add('completed');
      }
      for (let i = currentStep + 1; i <= 6; i++) {
        document.getElementById(`step${i}`).classList.remove('completed');
      }
    }

    // 步骤1：查询洞察分析结果
    function searchInsightResults() {
      // 模拟查询结果
      const results = [
        { paramName: "业务达成率", indexName: "商客业务完成率", indexCode: "BIZ-001", indexValue: "96.5%", region: "全国" },
        { paramName: "环比增长率", indexName: "业务环比增长", indexCode: "BIZ-002", indexValue: "8.2%", region: "全国" },
        { paramName: "月度目标值", indexName: "月度业务目标", indexCode: "BIZ-003", indexValue: "5000万", region: "全国" },
        { paramName: "平均响应时长", indexName: "支撑平均响应时间", indexCode: "SUP-001", indexValue: "15分钟", region: "全国" },
        { paramName: "问题解决率", indexName: "支撑问题解决率", indexCode: "SUP-002", indexValue: "98.3%", region: "全国" }
      ];
      
      bulletinData.insightResults = results;
      
      // 填充表格
      const tableBody = document.getElementById('insightResultsTable');
      tableBody.innerHTML = '';
      
      results.forEach(result => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${result.paramName}</td>
          <td>${result.indexName}</td>
          <td>${result.indexCode}</td>
          <td>${result.indexValue}</td>
          <td>${result.region}</td>
          <td><button class="btn" style="color: var(--primary-color);" onclick="selectInsightResult('${result.paramName}')"><i class="fas fa-check"></i> 选择</button></td>
        `;
        tableBody.appendChild(row);
      });
    }

    // 选择洞察结果
    function selectInsightResult(paramName) {
      const result = bulletinData.insightResults.find(r => r.paramName === paramName);
      if (result) {
        alert(`已选择：${result.paramName} - ${result.indexValue}`);
      }
    }

    // 步骤2：生成标题
    function generateTitle() {
      const bulletinType = document.getElementById('titleBulletinType').value;
      const time = document.getElementById('titleTime').value;
      const region = document.getElementById('titleRegion').value;
      
      if (!bulletinType || !time || !region) {
        alert('请填写所有必填字段');
        return;
      }
      
      // 转换类型为中文
      let typeText = '';
      switch(bulletinType) {
        case 'business': typeText = '商客业务发展'; break;
        case 'support': typeText = '商客支撑效能'; break;
        case 'tool': typeText = '商客工具使用'; break;
      }
      
      // 模拟调用大模型生成标题
      const generatedTitle = `${time}${region}${typeText}通报`;
      document.getElementById('generatedTitle').value = generatedTitle;
      
      // 保存标题
      bulletinData.title = generatedTitle;
      
      // 模拟请求和响应报文
      const request = {
        "action": "generate_title",
        "parameters": {
          "bulletin_type": bulletinType,
          "time": time,
          "region": region
        }
      };
      
      const response = {
        "code": 200,
        "message": "success",
        "result": {
          "title": generatedTitle
        }
      };
      
      document.getElementById('titleRequest').value = JSON.stringify(request, null, 2);
      document.getElementById('titleResponse').value = JSON.stringify(response, null, 2);
    }

    // 重置标题生成
    function resetTitle() {
      document.getElementById('titleBulletinType').value = '';
      document.getElementById('titleTime').value = '';
      document.getElementById('titleRegion').value = '';
      document.getElementById('generatedTitle').value = '';
      document.getElementById('titleRequest').value = '';
      document.getElementById('titleResponse').value = '';
      bulletinData.title = '';
    }

    // 保存标题并前往步骤3
    function saveTitleAndGoToStep3() {
      if (!bulletinData.title) {
        alert('请先生成标题');
        return;
      }
      
      goToStep(3);
    }

    // 步骤3：加载模板内容
    function loadTemplateContent() {
      const templateId = document.getElementById('templateSelect').value;
      let templateText = '';
      
      // 根据选择的模板加载不同内容
      switch(templateId) {
        case 'template1':
          templateText = ` 月度商客业务发展通报
 一、业务总体情况
本月商客业务总体完成情况良好，达成率{业务达成率}，较上月增长{环比增长率}。
月度目标值为{月度目标值}，实际完成情况符合预期。

 二、区域业务分析
各区域业务完成情况如下：
1. 华东地区：完成率102%
2. 华北地区：完成率98%
3. 华南地区：完成率95%

 三、下一步工作计划
1. 加强华南地区市场推广
2. 优化产品方案，提升客户满意度
3. 开展销售培训，提高转化率`;
          break;
        case 'template2':
          templateText = ` 商客支撑效能通报
 一、支撑总体情况
本月支撑响应平均时长{平均响应时长}，问题解决率{问题解决率}，客户满意度较高。

 二、典型问题分析
1. 系统故障类问题：占比30%，平均解决时间20分钟
2. 业务咨询类问题：占比50%，平均解决时间10分钟
3. 其他问题：占比20%，平均解决时间15分钟

 三、改进措施
1. 优化常见问题知识库
2. 加强支撑人员技能培训
3. 完善问题反馈机制`;
          break;
        case 'template3':
          templateText = ` 商客工具使用通报
 一、工具使用概况
本月智能工具活跃使用率{活跃使用率}，平均每日使用时长{平均使用时长}，使用效率提升{效率提升率}。

 二、工具使用分析
1. 客户管理工具：使用率85%，主要用于客户信息维护
2. 数据分析工具：使用率60%，主要用于业务报表生成
3. 自动化工具：使用率40%，主要用于流程自动化

 三、推广计划
1. 开展工具使用培训
2. 收集用户反馈，优化工具功能
3. 建立工具使用激励机制`;
          break;
      }
      
      // 显示模板内容
      document.getElementById('templateContent').textContent = templateText;
      
      // 填充指标值列表
      const 指标值列表 = document.getElementById('指标值列表');
      指标值列表.innerHTML = '';
      
      // 根据模板提取变量并填充示例值
      if (templateId === 'template1') {
        指标值列表.innerHTML = `
          <div class="insight-result-item">
            <div><strong>业务达成率：</strong>96.5%</div>
          </div>
          <div class="insight-result-item">
            <div><strong>环比增长率：</strong>8.2%</div>
          </div>
          <div class="insight-result-item">
            <div><strong>月度目标值：</strong>5000万</div>
          </div>
        `;
        
        // 填充后的内容
        const filledText = templateText
          .replace(/{业务达成率}/g, '96.5%')
          .replace(/{环比增长率}/g, '8.2%')
          .replace(/{月度目标值}/g, '5000万');
          
        document.getElementById('filledContent').textContent = filledText;
        bulletinData.filledContent = filledText;
      } else if (templateId === 'template2') {
        指标值列表.innerHTML = `
          <div class="insight-result-item">
            <div><strong>平均响应时长：</strong>15分钟</div>
          </div>
          <div class="insight-result-item">
            <div><strong>问题解决率：</strong>98.3%</div>
          </div>
        `;
        
        // 填充后的内容
        const filledText = templateText
          .replace(/{平均响应时长}/g, '15分钟')
          .replace(/{问题解决率}/g, '98.3%');
          
        document.getElementById('filledContent').textContent = filledText;
        bulletinData.filledContent = filledText;
      } else if (templateId === 'template3') {
        指标值列表.innerHTML = `
          <div class="insight-result-item">
            <div><strong>活跃使用率：</strong>75%</div>
          </div>
          <div class="insight-result-item">
            <div><strong>平均使用时长：</strong>45分钟</div>
          </div>
          <div class="insight-result-item">
            <div><strong>效率提升率：</strong>30%</div>
          </div>
        `;
        
        // 填充后的内容
        const filledText = templateText
          .replace(/{活跃使用率}/g, '75%')
          .replace(/{平均使用时长}/g, '45分钟')
          .replace(/{效率提升率}/g, '30%');
          
        document.getElementById('filledContent').textContent = filledText;
        bulletinData.filledContent = filledText;
      }
    }

    // 保存填充内容并前往步骤4
    function saveFilledContentAndGoToStep4() {
      if (!document.getElementById('templateSelect').value) {
        alert('请选择模板并填充内容');
        return;
      }
      
      goToStep(4);
    }

    // 步骤4：查询通报对象
    function searchRecipients() {
      // 模拟查询结果
      const recipients = [
        { org: "商客业务部", role: "经理", id: "EMP001", name: "张三" },
        { org: "商客业务部", role: "主管", id: "EMP002", name: "李四" },
        { org: "商客业务部", role: "专员", id: "EMP005", name: "钱七" },
        { org: "支撑部", role: "经理", id: "EMP003", name: "王五" },
        { org: "支撑部", role: "工程师", id: "EMP006", name: "孙八" },
        { org: "技术部", role: "主管", id: "EMP004", name: "赵六" }
      ];
      
      // 填充表格
      const tableBody = document.getElementById('recipientsTable');
      tableBody.innerHTML = '';
      
      recipients.forEach(recipient => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td><input type="checkbox" class="recipient-check" data-id="${recipient.id}" data-name="${recipient.name}" data-org="${recipient.org}" data-role="${recipient.role}"></td>
          <td>${recipient.org}</td>
          <td>${recipient.role}</td>
          <td>${recipient.id}</td>
          <td>${recipient.name}</td>
        `;
        tableBody.appendChild(row);
      });
      
      // 绑定全选事件
      document.getElementById('selectAllRecipients').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.recipient-check');
        checkboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
        });
        updateSelectedRecipients();
      });
      
      // 绑定单选事件
      document.querySelectorAll('.recipient-check').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedRecipients);
      });
    }

    // 更新已选择的通报对象
    function updateSelectedRecipients() {
      const selected = [];
      document.querySelectorAll('.recipient-check:checked').forEach(checkbox => {
        selected.push({
          id: checkbox.getAttribute('data-id'),
          name: checkbox.getAttribute('data-name'),
          org: checkbox.getAttribute('data-org'),
          role: checkbox.getAttribute('data-role')
        });
      });
      
      bulletinData.recipients = selected;
      
      // 显示已选择的对象
      const container = document.getElementById('selectedRecipients');
      container.innerHTML = '';
      
      if (selected.length === 0) {
        container.innerHTML = '<div style="color: var(--text-tertiary);">未选择任何通报对象</div>';
        return;
      }
      
      selected.forEach(recipient => {
        const tag = document.createElement('span');
        tag.className = 'tag tag-success';
        tag.style.marginRight = '8px';
        tag.style.marginBottom = '8px';
        tag.style.display = 'inline-block';
        tag.textContent = `${recipient.name}（${recipient.org}）`;
        container.appendChild(tag);
      });
    }

    // 保存通报对象并前往步骤5
    function saveRecipientsAndGoToStep5() {
      if (bulletinData.recipients.length === 0) {
        alert('请至少选择一个通报对象');
        return;
      }
      
      // 在步骤5中显示标题、填充内容和通报对象
      document.getElementById('finalTitle').value = bulletinData.title;
      document.getElementById('finalFilledContent').textContent = bulletinData.filledContent;
      
      const recipientsContainer = document.getElementById('finalRecipients');
      recipientsContainer.innerHTML = '';
      bulletinData.recipients.forEach(recipient => {
        const tag = document.createElement('span');
        tag.className = 'tag tag-success';
        tag.style.marginRight = '8px';
        tag.style.marginBottom = '8px';
        tag.style.display = 'inline-block';
        tag.textContent = `${recipient.name}（${recipient.org}）`;
        recipientsContainer.appendChild(tag);
      });
      
      goToStep(5);
    }

    // 步骤5：生成全文
    function generateFullContent() {
      if (!bulletinData.title || !bulletinData.filledContent || bulletinData.recipients.length === 0) {
        alert('请确保已完成前面的步骤');
        return;
      }
      
      // 模拟调用大模型生成全文
      const fullContent = `# ${bulletinData.title}

 一、核心摘要
本次通报涵盖了${bulletinData.title}的关键指标和分析结论，总体表现良好，但仍有提升空间。

 二、详细内容
${bulletinData.filledContent.replace(/#/g, '#')}

 三、结论与建议
1. 继续保持优势领域的良好发展态势
2. 针对薄弱环节制定专项改进计划
3. 加强跨部门协作，提升整体效能

 四、下一步工作安排
1. 组织专项讨论会议，落实改进措施
2. 建立周度跟踪机制，监控改进效果
3. 在下个周期评估改进措施的有效性`;
      
      document.getElementById('fullContent').innerHTML = fullContent.replace(/# (.*?)\n/g, '<h3>$1</h3>').replace(/ (.*?)\n/g, '<h4>$1</h4>').replace(/1. (.*?)\n/g, '<p>• $1</p>');
      bulletinData.fullContent = fullContent;
      
      // 模拟请求和响应报文
      const request = {
        "action": "generate_full_content",
        "parameters": {
          "title": bulletinData.title,
          "template_content": bulletinData.filledContent,
          "recipients": bulletinData.recipients.map(r => r.name)
        }
      };
      
      const response = {
        "code": 200,
        "message": "success",
        "result": {
          "full_content": fullContent
        }
      };
      
      document.getElementById('contentRequest').value = JSON.stringify(request, null, 2);
      document.getElementById('contentResponse').value = JSON.stringify(response, null, 2);
    }

    // 保存全文并前往步骤6
    function saveFullContentAndGoToStep6() {
      if (!bulletinData.fullContent) {
        alert('请先生成通报全文');
        return;
      }
      
      // 生成实例ID
      const instanceId = 'BUL-INS-' + new Date().getFullYear() + 
                        (new Date().getMonth() + 1).toString().padStart(2, '0') + 
                        Math.floor(100 + Math.random() * 900);
      
      // 在步骤6中显示相关信息
      document.getElementById('bulletinInstanceId').value = instanceId;
      document.getElementById('reminderTitle').value = bulletinData.title;
      document.getElementById('generationTime').value = new Date().toLocaleString();
      
      // 填充默认提醒对象
      document.getElementById('reminderRecipients').value = 'ADMIN001,ADMIN002';
      
      goToStep(6);
    }

    // 步骤6：发送提醒
    function sendReminder() {
      const instanceId = document.getElementById('bulletinInstanceId').value;
      const title = document.getElementById('reminderTitle').value;
      const recipients = document.getElementById('reminderRecipients').value;
      
      if (!recipients) {
        alert('请输入提醒对象');
        return;
      }
      
      // 获取选中的提醒方式
      const methods = [];
      document.querySelectorAll('input[name="reminderMethod"]:checked').forEach(checkbox => {
        methods.push(checkbox.value);
      });
      
      if (methods.length === 0) {
        alert('请至少选择一种提醒方式');
        return;
      }
      
      // 模拟发送提醒
      alert(`审核提醒已发送！\n运营通报实例ID：${instanceId}\n标题：${title}\n提醒对象：${recipients}\n提醒方式：${methods.join('、')}`);
      
      // 完成整个流程
      goToStep(6);
      alert('运营通报自动生成流程已完成，等待管理员审核。');
    }

    // 手工录入相关功能
    function insertVariable(variable) {
      const editor = document.getElementById('manualContent');
      // 在光标位置插入变量
      const selection = window.getSelection();
      if (selection.rangeCount) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        const textNode = document.createTextNode(variable);
        range.insertNode(textNode);
        range.setStartAfter(textNode);
        range.setEndAfter(textNode);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        editor.textContent += variable;
      }
    }

    function resetManualForm() {
      document.getElementById('manualBulletinType').value = '';
      document.getElementById('manualTitle').value = '';
      document.getElementById('manualRecipients').value = '';
      document.getElementById('manualContent').textContent = '';
    }

    document.getElementById('manualBulletinForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const type = document.getElementById('manualBulletinType').value;
      const title = document.getElementById('manualTitle').value;
      const recipients = document.getElementById('manualRecipients').value;
      const content = document.getElementById('manualContent').innerText;
      
      if (!type || !title || !recipients || !content) {
        alert('请填写所有必填字段');
        return;
      }
      
      // 生成实例ID
      const instanceId = 'BUL-INS-' + new Date().getFullYear() + 
                        (new Date().getMonth() + 1).toString().padStart(2, '0') + 
                        Math.floor(900 + Math.random() * 100);
      
      alert(`运营通报已保存并提交审核！\n实例ID：${instanceId}\n标题：${title}`);
      this.reset();
    });

    // 选择通报对象
    document.getElementById('selectAllModalRecipients').addEventListener('change', function() {
      const checkboxes = document.querySelectorAll('.recipient-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });

    function confirmRecipientsSelection() {
      const selected = [];
      document.querySelectorAll('.recipient-checkbox:checked').forEach(checkbox => {
        selected.push(checkbox.getAttribute('data-name') + '(' + checkbox.getAttribute('data-id') + ')');
      });
      
      if (selected.length > 0) {
        document.getElementById('manualRecipients').value = selected.join(', ');
      }
      
      document.getElementById('selectRecipientsModal').classList.remove('show');
    }

    // 审核相关功能
    function viewBulletinForReview(instanceId) {
      // 根据实例ID获取不同的详情数据
      let details = {};
      
      if (instanceId === 'BUL-INS-202308001') {
        details = {
          id: 'BUL-INS-202308001',
          title: '2023年8月商客业务发展通报',
          type: '商客业务发展通报',
          createMethod: '自动生成',
          creator: '系统自动',
          createTime: '2023-08-01 09:00',
          status: '待审核',
          recipients: '张三（商客业务部）、李四（商客业务部）、王五（支撑部）',
          content: `# 2023年8月商客业务发展通报

 一、核心摘要
本月商客业务总体完成情况良好，达成率96.5%，较上月增长8.2%，整体发展态势积极。

 二、详细内容
# 月度商客业务发展通报
 一、业务总体情况
本月商客业务总体完成情况良好，达成率96.5%，较上月增长8.2%。
月度目标值为5000万，实际完成情况符合预期。

 二、区域业务分析
各区域业务完成情况如下：
• 华东地区：完成率102%
• 华北地区：完成率98%
• 华南地区：完成率95%

 三、下一步工作计划
• 加强华南地区市场推广
• 优化产品方案，提升客户满意度
• 开展销售培训，提高转化率

 三、结论与建议
1. 继续保持优势领域的良好发展态势
2. 针对华南地区制定专项推广计划
3. 加强跨部门协作，提升整体效能

 四、下一步工作安排
1. 组织专项讨论会议，落实改进措施
2. 建立周度跟踪机制，监控改进效果
3. 在下个周期评估改进措施的有效性`,
          reviewOpinions: ''
        };
        document.getElementById('editBulletinBtn').style.display = 'inline-block';
      } else if (instanceId === 'BUL-INS-202308002') {
        details = {
          id: 'BUL-INS-202308002',
          title: '7月支撑响应效能分析通报',
          type: '商客支撑效能通报',
          createMethod: '手工录入',
          creator: '张三',
          createTime: '2023-08-01 10:15',
          status: '待审核',
          recipients: '王五（支撑部）、赵六（技术部）',
          content: `# 7月支撑响应效能分析通报

 一、总体情况
7月份支撑响应工作总体表现良好，客户满意度达到95%。响应及时率为98%，问题一次性解决率为92%。

 二、详细数据分析
1. 平均响应时长：15分钟，较上月减少2分钟
2. 问题解决率：98.3%，较上月提升1.2%
3. 客户满意度：95分，较上月持平

 三、存在问题
1. 复杂技术问题解决时间较长，平均需要40分钟
2. 部分新员工对业务不熟悉，需要加强培训
3. 知识库更新不及时，影响解决效率

 四、改进措施
1. 开展复杂问题处理专项培训
2. 建立新员工导师制度
3. 完善知识库更新机制，指定专人负责`,
          reviewOpinions: ''
        };
        document.getElementById('editBulletinBtn').style.display = 'inline-block';
      } else if (instanceId === 'BUL-INS-202307045') {
        details = {
          id: 'BUL-INS-202307045',
          title: '第二季度智能工具使用情况通报',
          type: '商客工具使用通报',
          createMethod: '自动生成',
          creator: '系统自动',
          createTime: '2023-07-05 14:30',
          status: '审核通过',
          recipients: '各部门负责人',
          content: `# 第二季度智能工具使用情况通报

 一、使用概况
第二季度智能工具整体使用率为75%，较上一季度提升10个百分点，使用效率提升明显。

 二、详细分析
1. 客户管理工具：使用率85%，主要用于客户信息维护和跟进
2. 数据分析工具：使用率60%，主要用于业务报表生成和趋势分析
3. 自动化工具：使用率40%，主要用于流程自动化和批量处理

 三、使用效果
1. 工作效率平均提升30%
2. 错误率降低25%
3. 员工满意度提升至85分

 四、推广计划
1. 开展工具使用进阶培训
2. 收集用户反馈，优化工具功能
3. 建立工具使用激励机制`,
          reviewOpinions: '内容详实，数据准确，同意发布。'
        };
        document.getElementById('editBulletinBtn').style.display = 'none';
      } else if (instanceId === 'BUL-INS-202307032') {
        details = {
          id: 'BUL-INS-202307032',
          title: '6月业务发展问题通报',
          type: '商客业务发展通报',
          createMethod: '手工录入',
          creator: '李四',
          createTime: '2023-07-01 09:45',
          status: '审核驳回',
          recipients: '商客业务部全体员工',
          content: `# 6月业务发展问题通报

 一、总体情况
6月份商客业务发展未达预期，完成率仅为85%，较上月下降5个百分点。

 二、主要问题
1. 新客户开发数量不足，仅完成目标的70%
2. 老客户续约率下降，较上月下降8%
3. 部分区域市场份额被竞争对手抢占

 三、原因分析
1. 市场推广力度不足
2. 产品竞争力有待提升
3. 销售团队积极性不高`,
          reviewOpinions: '内容过于简略，缺乏具体数据支撑和解决方案，需要补充完善后重新提交。'
        };
        document.getElementById('editBulletinBtn').style.display = 'inline-block';
      }
      
      // 填充详情模态框
      document.getElementById('bulletinDetailId').textContent = details.id;
      document.getElementById('bulletinDetailTitle').textContent = details.title;
      document.getElementById('bulletinDetailType').textContent = details.type;
      document.getElementById('bulletinDetailCreateMethod').textContent = details.createMethod;
      document.getElementById('bulletinDetailCreator').textContent = details.creator;
      document.getElementById('bulletinDetailCreateTime').textContent = details.createTime;
      document.getElementById('bulletinDetailStatus').textContent = details.status;
      document.getElementById('bulletinDetailRecipients').textContent = details.recipients;
      document.getElementById('bulletinDetailContent').innerHTML = details.content.replace(/# (.*?)\n/g, '<h3>$1</h3>').replace(/ (.*?)\n/g, '<h4>$1</h4>').replace(/1. (.*?)\n/g, '<p>• $1</p>').replace(/• (.*?)\n/g, '<p>• $1</p>');
      
      // 显示审核意见（如果有）
      if (details.reviewOpinions) {
        document.getElementById('reviewOpinionsSection').style.display = 'block';
        document.getElementById('bulletinDetailReviewOpinions').textContent = details.reviewOpinions;
      } else {
        document.getElementById('reviewOpinionsSection').style.display = 'none';
      }
      
      // 保存当前查看的实例ID，用于审核
      document.getElementById('reviewBulletinId').value = details.id;
      document.getElementById('reviewBulletinTitle').value = details.title;
      
      // 显示详情模态框
      document.getElementById('viewBulletinDetailModal').classList.add('show');
    }

    function editBulletin() {
      // 获取当前查看的通报信息
      const id = document.getElementById('bulletinDetailId').textContent;
      const title = document.getElementById('bulletinDetailTitle').textContent;
      const recipients = document.getElementById('bulletinDetailRecipients').textContent;
      const content = document.getElementById('bulletinDetailContent').innerText;
      
      // 填充编辑表单
      document.getElementById('editBulletinInstanceId').value = id;
      document.getElementById('editBulletinTitle').value = title;
      document.getElementById('editBulletinRecipients').value = recipients;
      document.getElementById('editBulletinContent').textContent = content;
      
      // 关闭详情模态框，显示编辑模态框
      document.getElementById('viewBulletinDetailModal').classList.remove('show');
      document.getElementById('editBulletinModal').classList.add('show');
    }

    function saveBulletinEdit() {
      const id = document.getElementById('editBulletinInstanceId').value;
      const title = document.getElementById('editBulletinTitle').value;
      const recipients = document.getElementById('editBulletinRecipients').value;
      const content = document.getElementById('editBulletinContent').innerText;
      
      if (!title || !recipients || !content) {
        alert('请填写所有必填字段');
        return;
      }
      
      alert(`通报修改已保存，将重新提交审核！\n实例ID：${id}`);
      
      // 关闭编辑模态框
      document.getElementById('editBulletinModal').classList.remove('show');
    }

    function submitReview() {
      const id = document.getElementById('reviewBulletinId').value;
      const title = document.getElementById('reviewBulletinTitle').value;
      const result = document.querySelector('input[name="reviewResult"]:checked').value;
      const comments = document.getElementById('reviewComments').value;
      
      if (!comments) {
        alert('请输入审核意见');
        return;
      }
      
      const resultText = result === 'approved' ? '审核通过' : '审核驳回';
      alert(`审核已完成！\n实例ID：${id}\n标题：${title}\n审核结果：${resultText}`);
      
      // 关闭审核模态框和详情模态框
      document.getElementById('reviewModal').classList.remove('show');
      document.getElementById('viewBulletinDetailModal').classList.remove('show');
    }

    // 关闭模态框（点击关闭按钮或外部区域）
    document.querySelectorAll('.modal-close').forEach(btn => {
      btn.addEventListener('click', () => {
        btn.closest('.modal').classList.remove('show');
      });
    });
    
    window.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal')) {
        e.target.classList.remove('show');
      }
    });

    // 页面加载时初始化第一步
    window.onload = function() {
      document.getElementById(`content${currentStep}`).style.display = 'block';
    };
  </script>
</body>
</html>
    