<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>数智化运营平台 - 统一运营门户</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">视图权限管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">权限日志管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-globe-asia page-title-icon"></i>
      统一运营门户
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item active">统一运营门户</div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
      <div class="tab active" data-tab-target="viewPermission">视图权限管理</div>
      <div class="tab" data-tab-target="permissionLog">权限日志管理</div>
    </div>

    <!-- 视图权限管理 -->
    <div class="tab-content active" id="viewPermission">
      <!-- 搜索和操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div style="display: flex; width: 50%;">
          <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
            <i class="fas fa-search search-box-icon"></i>
            <input type="text" placeholder="搜索权限...">
          </div>
          <div style="margin-right: 12px;">
            <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="all">全部状态</option>
              <option value="active">启用</option>
              <option value="inactive">禁用</option>
            </select>
          </div>
        </div>
        <div style="display: flex;">
          <button class="btn btn-primary" data-modal-target="addPermissionModal"><i class="fas fa-plus"></i> 新增权限</button>
        </div>
      </div>

      <!-- 权限列表 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>权限名称</th>
                <th>描述</th>
                <th>关联视图</th>
                <th>创建人</th>
                <th>创建时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>全国运营大屏查看权限</td>
                <td>允许查看全国运营监控大屏</td>
                <td>全国运营监控大屏</td>
                <td>管理员</td>
                <td>2023-06-01 14:30</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editPermissionModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>数据分析报告查看权限</td>
                <td>允许查看各类数据分析报告</td>
                <td>所有分析报告</td>
                <td>管理员</td>
                <td>2023-06-10 10:15</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editPermissionModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>数据编辑权限</td>
                <td>允许编辑和修改平台数据</td>
                <td>所有数据模块</td>
                <td>管理员</td>
                <td>2023-06-15 09:45</td>
                <td><span class="tag tag-warning">禁用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editPermissionModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 权限日志管理 -->
    <div class="tab-content" id="permissionLog">
      <!-- 搜索和操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div style="display: flex; width: 70%;">
          <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
            <i class="fas fa-search search-box-icon"></i>
            <input type="text" placeholder="搜索日志...">
          </div>
          <div style="margin-right: 12px;">
            <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="all">所有操作类型</option>
              <option value="create">创建</option>
              <option value="edit">编辑</option>
              <option value="delete">删除</option>
              <option value="grant">授权</option>
              <option value="revoke">撤销</option>
            </select>
          </div>
          <div style="display: flex; gap: 8px;">
            <input type="date" placeholder="开始日期">
            <input type="date" placeholder="结束日期">
          </div>
        </div>
        <div style="display: flex;">
          <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-download"></i> 导出</button>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>操作人</th>
                <th>操作时间</th>
                <th>操作类型</th>
                <th>操作描述</th>
                <th>IP地址</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>管理员</td>
                <td>2023-07-16 14:30</td>
                <td><span class="tag tag-primary">创建</span></td>
                <td>创建了新的视图权限: 省级运营大屏查看权限</td>
                <td>*************</td>
                <td><span class="tag tag-success">成功</span></td>
              </tr>
              <tr>
                <td>张三</td>
                <td>2023-07-16 13:15</td>
                <td><span class="tag tag-warning">授权</span></td>
                <td>为用户李四授予数据分析报告查看权限</td>
                <td>*************</td>
                <td><span class="tag tag-success">成功</span></td>
              </tr>
              <tr>
                <td>李四</td>
                <td>2023-07-16 10:45</td>
                <td><span class="tag tag-danger">删除</span></td>
                <td>尝试删除数据编辑权限</td>
                <td>192.168.1.102</td>
                <td><span class="tag tag-danger">失败</span></td>
              </tr>
              <tr>
                <td>管理员</td>
                <td>2023-07-15 16:20</td>
                <td><span class="tag tag-info">编辑</span></td>
                <td>修改了全国运营大屏查看权限的描述信息</td>
                <td>*************</td>
                <td><span class="tag tag-success">成功</span></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增权限模态框 -->
  <div class="modal" id="addPermissionModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增视图权限</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addPermissionForm">
          <div class="form-group">
            <label for="permissionName">权限名称</label>
            <input type="text" id="permissionName" name="permissionName" required placeholder="请输入权限名称">
          </div>
          <div class="form-group">
            <label for="permissionDesc">权限描述</label>
            <textarea id="permissionDesc" name="permissionDesc" rows="3" required placeholder="请输入权限描述"></textarea>
          </div>
          <div class="form-group">
            <label for="relatedView">关联视图</label>
            <select id="relatedView" name="relatedView" required>
              <option value="">请选择关联视图</option>
              <option value="national_screen">全国运营监控大屏</option>
              <option value="provincial_screen">省份运营分析大屏</option>
              <option value="city_screen">城市运营监控大屏</option>
              <option value="district_screen">区县运营分析大屏</option>
              <option value="all_reports">所有分析报告</option>
            </select>
          </div>
          <div class="form-group">
            <label for="permissionStatus">权限状态</label>
            <div style="display: flex; gap: 16px;">
              <label style="display: flex; align-items: center;"><input type="radio" name="permissionStatus" value="active" checked> 启用</label>
              <label style="display: flex; align-items: center;"><input type="radio" name="permissionStatus" value="inactive"> 禁用</label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addPermissionModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addPermissionForm').submit()">保存</button>
      </div>
    </div>
  </div>

  <!-- 编辑权限模态框 -->
  <div class="modal" id="editPermissionModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑视图权限</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑视图权限表单内容与新增表单类似，实际应用中会加载权限的当前配置。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editPermissionModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 标签页切换
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // 移除所有标签页的active类
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

        // 给当前点击的标签页添加active类
        tab.classList.add('active');

        // 显示对应的内容
        const target = tab.getAttribute('data-tab-target');
        document.getElementById(target).classList.add('active');
      });
    });

    // 新增权限表单提交
    document.getElementById('addPermissionForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addPermissionForm')) {
        // 模拟提交成功
        alert('视图权限创建成功！');
        document.getElementById('addPermissionModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });
  </script>
</body>
</html>